<template>
  <nd-dialog ref="dialogRef" :title="page.title"  width="60vw" height="65vh">
  <div style="padding: 15px 12px;">
    <el-form
      :model="page.formData"
      :rules="rules"
      label-width="120px"
      ref="formRef"
      style="
        background: #fff;
        padding: 12px;
        border-radius: 5px;
        border: 1px solid #eaeaea;
      "
      v-if="page.type !== 'detail'"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <nd-input
              v-model.trim="page.formData.title"
              placeholder="请输入标题"
              maxlength="30"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="简介" prop="summary">
            <nd-input
              v-model="page.formData.summary"
              placeholder="请输入简介"
              maxlength="300"
              clearable
              type="textarea"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <nd-input
              v-model="page.formData.sort"
              placeholder="请输入排序"
              maxlength="30"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公开" prop="publiced" required>
            <nd-radio-group v-model="page.formData.publiced">
              <nd-radio :label="1">是</nd-radio>
              <nd-radio :label="0">否</nd-radio>
            </nd-radio-group>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
                            <el-form-item label="公开栏目" prop="publiced">
                                <nd-select v-model="page.formData.publiced" clearable style="width: 100%" @change="handlepublicedChange">
                                    <el-option label="知识科普" :value="1" />
                                </nd-select>
                            </el-form-item>
                        </el-col> -->
        <el-col :span="24">
          <el-form-item label="内容" prop="content">
            <div style="height: 350px;width: 100%;">
              <div class="editor-container">
              <QuillEditor
                :options="editorOption"
                theme="snow"
                v-model:content="page.formData.content"
                contentType="html"
              />
            </div>
            </div>
           
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="封面图片" prop="file">
            <ndb-upload
              :files="page.formData.file"
              fzgs="knowledge"
              :limit="1"
              tip1=""
              tip2="最多只能上传一张图片"
            ></ndb-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建人">
            <nd-input
              v-model="page.formData.userName"
              readonly
              placeholder="请输入创建人"
              maxlength="30"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="更新时间">
            <nd-input
              v-model="page.formData.updateTime"
              readonly
              placeholder="请输入创建人"
              maxlength="30"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="form-container" v-else>
      <div class="form-row">
        <div class="form-item full-width">
          <div class="form-label">标题</div>
          <div class="form-content">{{ page.formData.title }}</div>
        </div>

        <div class="form-item full-width">
          <div class="form-label">简介</div>
          <div class="form-content">{{ page.formData.summary }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">排序</div>
          <div class="form-content">{{ page.formData.sort }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">公开</div>
          <div class="form-content" v-if="page.formData.publiced == 1">是</div>
          <div class="form-content" v-if="page.formData.publiced == 0">否</div>
        </div>

        <div class="form-item full-width">
          <div class="form-label">内容</div>
          <div class="form-content">
            <div class="ql-snow">
              <div
                class="notes ql-editor"
                style="padding: 1px"
                v-html="page.formData.content"
              ></div>
            </div>
          </div>
        </div>

        <div class="form-item full-width">
          <div class="form-label">封面图片</div>
          <div class="form-content">
            <img
              v-if="page.formData.allPath"
              :src="page.formData.allPath"
              style="width: 80px; height: 80px"
            />
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">创建人</div>
          <div class="form-content">{{ page.formData.userName }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">更新时间</div>
          <div class="form-content">{{ page.formData.updateTime }}</div>
        </div>
      </div>
    </div>
  </div>

    <template #footer>
      <nd-button
        type="primary"
        @click="submitForm"
        v-if="page.type !== 'detail'"
        >提交</nd-button
      >
      <nd-button @click="close" v-if="page.type !== 'detail'">取消</nd-button>
      <nd-button @click="close" v-if="page.type === 'detail'">关闭</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndSelect from "@/components/ndSelect.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndbUpload from "@/components/business/ndbUpload/index.vue";
import { ref, reactive, inject, nextTick } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
const $axios = inject("$axios");

const dialogRef = ref(null);

const emit = defineEmits(["before-close"]);

const page = reactive({
  type: "",
  title: "新增",
  formData: {
    id: "",
    title: "",
    summary: "",
    sort: "",
    content: "",
    publiced: 1,
    file: [], // 附件
    updateTime: "",
    userName: "",
    allPath: "",
  },
});

const rules = {
  title: [{ required: true, message: "标题为必填项", trigger: "blur" }],
  summary: [{ required: true, message: "简介为必填项", trigger: "blur" }],
  sort: [
    { required: true, message: "排序为必填项", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === "") return callback();
        const regex = /^[1-9]\d*$/;
        if (!regex.test(value)) {
          callback(new Error("只能输入正整数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  publiced: [{ required: true, message: "公开为必填项", trigger: "blur" }],
  content: [
    { required: true, message: "内容为必填项", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (isNull(value)) {
          callback(new Error("内容不能为空"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

const isNull = (content) => {
  const withImg = content.replace(/<img[^>]*>/gi, "{{MEDIA}}");
  const filtered = withImg
    .replace(/<[^>]+>/g, "")
    .replace(/&nbsp;/gi, "")
    .trim();
  return (
    filtered === "" &&
    !withImg.includes("{{MEDIA}}") &&
    !/^[\s\uFEFF\xA0]+$/.test(content)
  );
};

const editorOption = ref({
  modules: {
    toolbar: [
      [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
      ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
      //['blockquote', 'code-block'], // 引用  代码块
      [{ header: 1 }, { header: 2 }], // 1、2 级标题
      // [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
      [{ script: "sub" }, { script: "super" }], // 上标/下标
      [{ indent: "-1" }, { indent: "+1" }], // 缩进
      [{ direction: "rtl" }], // 文本方向
      // [{ size: ['12px', false, '16px', '18px', '20px', '30px'] }], // 字体大小
      [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题

      // [{ font: [false, 'SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial'] }], // 字体种类
      [{ align: [] }], // 对齐方式
      ["clean"], // 清除文本格式
      //['link', 'image', 'video'] // 链接、图片、视频
      ["image"], // 链接、图片、视频
    ],
  },
  placeholder: "请输入内容",
});

const open = (type, row) => {
  resetForm();
  console.log("type", type);
  if (type === "add") {
    page.title = "新增";
    page.type = "add";
    getUser();
  } else if (type === "edit") {
    page.type = "edit";
    page.title = "编辑";
    page.formData.id = row.klgId;
    getDetail(row.klgId);
  } else if (type === "detail") {
    page.type = "detail";
    page.title = "详情";
    getDetail(row.klgId);
  }
  dialogRef.value.open();
};

const formRef = ref(null);

const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      let params = {
        klgId: page.formData.id,
        title: page.formData.title,
        summary: page.formData.summary,
        sort: page.formData.sort,
        content: page.formData.content,
        publiced: page.formData.publiced,
        updateTime: page.formData.updateTime,
        userName: page.formData.userName,
        file: page.formData.file[0],
      };
      const url = params.klgId ? "/information/update" : "/information/save";
      $axios({
        url: url,
        method: "post",
        serverName: "nd-base2",
        data: params,
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success(res.data.message);
          emit("before-close");
          dialogRef.value.close();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    }
  });
};

//获取登录人信息
const getUser = () => {
  $axios({
    url: "/information/getSystemInfo",
    method: "get",
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      page.formData.userName = res.data.data.userName;
      page.formData.updateTime = res.data.data.updateTime;
    }
  });
};

// 获取详情方法
const getDetail = (knowledgeId) => {
  $axios({
    url: `/information/findById/${knowledgeId}`,
    method: "get",
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      page.formData.title = res.data.data.title;
      page.formData.summary = res.data.data.summary;
      page.formData.sort = res.data.data.sort;
      page.formData.content = res.data.data.content;
      page.formData.publiced = res.data.data.publiced;
      page.formData.updateTime = res.data.data.updateTime;
      page.formData.userName = res.data.data.userName;
      if (Object.keys(res.data.data.file).length > 0) {
        page.formData.file = [res.data.data.file];
      } else {
        page.formData.file = [];
      }
      page.formData.allPath = res.data.data.file.fullPath;
    } else {
      ElMessage.error(res.data.message);
    }
  });
};

// 重置表单方法
const resetForm = () => {
  page.formData.id = "";
  page.formData.title = "";
  page.formData.summary = "";
  page.formData.sort = "";
  page.formData.content = "";
  page.formData.publiced = 1;
  page.formData.updateTime = "";
  page.formData.userName = "";
  page.formData.file = [];
  page.formData.allPath = "";
  nextTick(() => {
    if (formRef.value) {
      formRef.value.resetFields();
    }
  });
};

const close = () => {
  dialogRef.value.close();
  resetForm();
  emit("before-close");
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.fileError {
  padding-top: 20px;
}
.editor-container {
  width: 100%;
  height: 300px; 
}
.form-container {
  padding: 12px;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #eaeaea;
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
  }

  .form-item {
    flex: 0 0 50%;
    padding: 8px;
    display: flex;
    align-items: center;

    .form-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      color: #303133;
    }
  }
  .form-item.full-width {
    flex: 0 0 100%;
  }
}
</style>
