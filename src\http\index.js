import axios from 'axios';

// 配置响应时间
axios.defaults.timeout = 10000;

// 获取服务地址配置
const getServerUrl = () => {
  if (process.env.NODE_ENV === 'development') {
    return {
      baseUrl1: '/api1',  // Node服务代理
      baseUrl2: '/api2'   // Java服务代理
    }
  } else {
    // 生产环境从config.js读取配置
    return {
      baseUrl1: window.ipConfig?.baseUrl1 || 'https://api1.example.com',
      baseUrl2: window.ipConfig?.baseUrl2 || 'https://api2.example.com'
    }
  }
}

// 添加请求拦截器
axios.interceptors.request.use(config => {
  // 添加token
  const token = localStorage.getItem("token");
  if (token) {
    config.headers.token = token
  }

  // 根据serverName设置不同的baseURL
  const serverUrls = getServerUrl();

  if (config.serverName === 'baseUrl2') {
    // Java服务
    config.baseURL = serverUrls.baseUrl2;
  } else {
    // 默认Node服务 (baseUrl1)
    config.baseURL = serverUrls.baseUrl1;
  }

  return config;
}, err => {
  return Promise.reject(err);
});

// 添加响应拦截器
axios.interceptors.response.use((res) => {
  return res.data;
}, (error) => {
  console.error('请求错误:', error);
  return Promise.reject(error);
});

export default axios;