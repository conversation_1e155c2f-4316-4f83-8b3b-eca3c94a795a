<template>
  <ndb-page-tree-list>
    <template #tree>
      <nd-tree
        :data="page.treeData"
        node-key="id"
        :props="treeProps"
        :load="loadNode"
        lazy
        :default-expanded-keys="defaultExpandedKeys"
        :current-node-key="currentNodeKey"
        :highlight-current="true"
        @node-click="handleNodeClick"
      />
    </template>

    <ndb-page-list>
      <template #button>
        <nd-button @click="openAddDialog" type="primary" icon="plus"
          >新增</nd-button
        >
        <nd-button @click="openImportDialog" icon="upload">导入</nd-button>
        <nd-button @click="handleResetPassword">重置密码</nd-button>
        <nd-button @click="handlePermissionManagement">权限管理</nd-button>
        <nd-button @click="handleBatchDelete" icon="delete">删除 </nd-button>
        <el-checkbox
          v-model="page.searchData.self"
          style="margin-left: 20px"
          @change="handleSelfChange"
          >仅看本级</el-checkbox
        >
      </template>

      <template #search>
        <nd-search-more>
          <nd-search-more-item title="用户名称">
            <nd-input
              v-model.trim="page.searchData.userName"
              placeholder="请输入用户名称"
            />
          </nd-search-more-item>

          <nd-search-more-item title="用户账号">
            <nd-input
              v-model.trim="page.searchData.account"
              placeholder="请输入用户账号"
            />
          </nd-search-more-item>

          <nd-search-more-item title="所属供应商">
            <nd-input
              v-model.trim="page.searchData.deptName"
              placeholder="请输入所属供应商"
            />
          </nd-search-more-item>

          <nd-search-more-item title="手机号码">
            <nd-input
              v-model.trim="page.searchData.phone"
              placeholder="请输入手机号码"
            />
          </nd-search-more-item>

          <nd-search-more-item title="身份证号">
            <nd-input
              v-model.trim="page.searchData.card"
              placeholder="请输入身份证号"
            />
          </nd-search-more-item>

          <nd-search-more-item title="角色权限">
            <nd-input
              v-model.trim="page.searchData.roleName"
              placeholder="请输入角色权限"
            />
          </nd-search-more-item>

          <nd-search-more-item title="是否授权">
            <nd-select v-model="page.searchData.auth" clearable>
              <el-option label="全部" :value="3" />
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </nd-select>
          </nd-search-more-item>

          <nd-search-more-item title="状态">
            <nd-select v-model="page.searchData.status" clearable>
              <el-option label="全部" :value="3" />
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </nd-select>
          </nd-search-more-item>

          <template #footer>
            <nd-button type="primary" @click="getTableData">查询</nd-button>
            <nd-button @click="reset">重置</nd-button>
          </template>
        </nd-search-more>
      </template>

      <template #table>
        <nd-table
          style="height: 100%"
          :data="page.table.data"
          border
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            header-align="center"
          />
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
            header-align="center"
          />
          <el-table-column
            prop="prop"
            label="用户账号"
            header-align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span class="goto-detail" @click="openDetailDialog(row)">{{
                row.account
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="userName"
            label="用户名称"
            header-align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="deptName"
            label="所属供应商"
            header-align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="phone"
            label="手机号码"
            header-align="center"
          />
          <el-table-column prop="card" label="身份证号" header-align="center" min-width="150px" />
          <el-table-column
            prop="permission"
            label="角色权限"
            header-align="center"
            show-overflow-tooltip
          />
          <el-table-column prop="status" label="状态" align="center">
            <template #default="{ row }">
              <div>{{ row.status == 1 ? "正常" : "禁用" }}</div>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="180"
            fixed="right"
            header-align="center"
          >
            <template #default="{ row }">
              <div style="display: flex; gap: 8px; justify-content: center">
                <nd-button type="edit" @click="openEditDialog(row)"
                  >编辑</nd-button
                >
                <nd-button type="delete" @click="handleDelete(row)"
                  >删除</nd-button
                >
              </div>
            </template>
          </el-table-column>
        </nd-table>
      </template>

      <template #page>
        <nd-pagination
          v-model:current-page="page.pager.page"
          v-model:page-size="page.pager.size"
          :total="page.pager.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </template>
    </ndb-page-list>
  </ndb-page-tree-list>

  <add-dialog ref="dialogRef" @before-close="getTableData"></add-dialog>
  <PermissionManagement
    ref="permissionManagementDialogRef"
    :selectedUsers="page.table.selectedRows"
    @refresh="getTableData"
  ></PermissionManagement>
  <ndb-import
    ref="importRef"
    titles="导入"
    projectId="1"
    modeType="supplierUser"
    @upbates="getTableData"
  />
</template>

<script setup>
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndbPageTreeList from "@/components/business/ndbPageTreeList/index.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTree from "@/components/ndTree.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import PermissionManagement from "./components/PermissionManagement.vue";
import addDialog from "./components/addDialog.vue";

import { reactive, ref, inject, onMounted, watch } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
const $axios = inject("$axios");
import { useRoute } from "vue-router";
const route = useRoute();

onMounted(() => {
  if (route.query.supplierId) {
    page.searchData.self = false;
  } else {
    page.searchData.self = true;
  }
  getTreeData();
});

watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.supplierId && newQuery.supplierId !== page.searchData.deptId) {
      page.searchData.deptId = newQuery.supplierId;
      page.searchData.self = false;
      getTableData();
    } else if (!newQuery.supplierId) {
      page.searchData.self = true;
      page.searchData.deptId = "";
      getTableData();
    }
  }
);

const page = reactive({
  treeData: [],
  childrenArr:[],
  searchData: {
    userName: "",
    account: "",
    deptName: "",
    deptId: route.query.supplierId || "",
    phone: "",
    card: "",
    roleName: "",
    status: 3,
    auth: 3,
    self: true,
  },
  table: {
    selectedRows: [],
    data: [],
  },
  pager: {
    page: 1,
    size: 10,
    total: 10,
  },
});

const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "leaf",
};

const dialogRef = ref(null);
const defaultExpandedKeys = ref([]);
const currentNodeKey = ref("");
const importRef = ref(null);
const firstRes = ref(true);

// 设置当前选中节点
const setCurrentNode = (key) => {
  currentNodeKey.value = key;
};

const getTreeData = () => {
  $axios({
    url: "/common/areaTree",
    method: "get",
    serverName: "nd-base2",
    params: {
      id: "",
      benji: true,
      containDept: false,
      deptType: "2",
      limitLevel: 3,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.treeData = res.data.data;
      page.treeData = res.data.data.map((item) => ({
        ...item,
        leaf: item.level === 3,
        children: item.level < 3 ? [] : undefined
      }));
      if (firstRes.value) {
        page.childrenArr = res.data.data[0].children;
      }
      // 设置默认选中的第一个节点id
      if (page.treeData.length > 0) {
        const firstNodeId = page.treeData[0].id;
        setCurrentNode(firstNodeId);
        defaultExpandedKeys.value = [firstNodeId];
        // handleNodeClick(page.treeData[0]);
        page.searchData.areaId = page.treeData[0].id;
        page.searchData.areaName = page.treeData[0].name;
        getTableData();
      }
      // defaultExpandedKeys.value = page.treeData.map((item) => item.id);
    }
  });
};

// 点击树节点
const handleNodeClick = (node) => {
  setCurrentNode(node.id);
  page.searchData.areaId = node.id;
  page.searchData.areaName = node.name;
  getTableData();
};
// 加载树节点
const loadNode = (node, resolve) => {
  if (node.level === 0 || node.data.leaf) return resolve([]);
  if (firstRes.value) {
    firstRes.value = false;
    return resolve(page.childrenArr);
  }
  $axios({
    url: "/common/areaTree",
    method: "get",
    serverName: "nd-base2",
    params: {
      id: node.data.id,
      benji: false,
      containDept: false,
      deptType: "2",
      limitLevel: 3,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      const children = res.data.data.map((item) => ({
        ...item,
        leaf: item.level === 3,
        children: [],
      }));
      resolve(children);
    }
  });
};

//查看本级
const handleSelfChange = (val) => {
  getTableData();
};

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

//导入
const openImportDialog = () => {
  importRef.value.open();
};

//重置密码
const handleResetPassword = async () => {
  if (page.table.selectedRows.length === 0) {
    ElMessage.warning("请先勾选用户记录！");
    return;
  }
  const count = page.table.selectedRows.length;
  try {
    await ElMessageBox.confirm(
      `是否对这${count}条数据进行密码重置？重置后不可恢复，请确认！`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    const userIds = page.table.selectedRows.map((item) => item.userId);
    const response = await $axios.post(
      "/supplierUser/reset",
      { userIds },
      { serverName: "nd-base2" }
    );
    if (response.data && response.data.code === 2000) {
      ElMessage.success(`重置成功！用户初始化密码为SYGYS@8888`);
      getTableData();
    } else {
      ElMessage.error(response.data.message || "重置密码失败");
    }
  } catch (e) {
    if (e !== "cancel") {
      console.error("重置密码失败:", e);
      ElMessage.error("重置密码失败");
    }
  }
};

const permissionManagementDialogRef = ref(null);

const handlePermissionManagement = () => {
  if (page.table.selectedRows.length === 0) {
    ElMessage.warning("请先勾选用户记录！");
    return;
  }
  if (
    permissionManagementDialogRef.value &&
    permissionManagementDialogRef.value.openDialog
  ) {
    permissionManagementDialogRef.value.openDialog(page.table.selectedRows);
  } else {
    console.error("权限管理弹窗引用未正确初始化");
  }
};

//表格数据
const getTableData = async () => {
  try {
    const params = {
      page: page.pager.page,
      size: page.pager.size,
      account: page.searchData.account || "",
      areaId: page.searchData.areaId || "",
      userName: page.searchData.userName || "",
      deptName: page.searchData.deptName || "",
      card: page.searchData.card || "",
      phone: page.searchData.phone || "",
      roleName: page.searchData.roleName || "",
      auth: page.searchData.auth === 3 ? "" : page.searchData.auth,
      self: page.searchData.self === true ? 1 : 0,
      status: page.searchData.status === 3 ? "" : page.searchData.status,
    };
    if (page.searchData.deptId) {
      params.deptId = page.searchData.deptId;
    }
    const response = await $axios.get("/supplierUser/index", {
      data: params,
      serverName: "nd-base2",
    });
    if (response.data && response.data.code === 2000) {
      page.table.data = response.data.data.records || [];
      page.pager.total = response.data.data.total || 0;
      page.pager.page = response.data.data.current || 1;
      page.pager.size = response.data.data.size || 10;
    } else {
      console.error("接口返回错误:", response.data.message || response.data);
    }
  } catch (e) {
    console.error("数据加载失败:", e);
  }
};

//新增
const openAddDialog = () => {
  dialogRef.value?.open(page.searchData, "add");
};
//编辑
const openEditDialog = (parmas) => {
  dialogRef.value?.open(parmas.userId, "edit");
};
//详情
const openDetailDialog = (parmas) => {
  dialogRef.value?.open(parmas.userId, "detail");
};

//重置
const reset = () => {
  page.searchData.userName = "";
  page.searchData.account = "";
  page.searchData.deptName = "";
  page.searchData.phone = "";
  page.searchData.card = "";
  page.searchData.roleName = "";
  page.searchData.status = 3;
  page.searchData.auth = 3;
  page.searchData.self = true;
  getTableData();
};

//勾选
const handleSelectionChange = (rows) => {
  page.table.selectedRows = rows;
};

//批量删除
const handleBatchDelete = async () => {
  if (page.table.selectedRows.length === 0) {
    ElMessage.warning("请先勾选用户记录！");
    return;
  }
  const count = page.table.selectedRows.length;
  try {
    await ElMessageBox.confirm(
      `用户删除后将无法再登录平台，确定将选中的${count}条记录删除?`,
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    const selectedIds = page.table.selectedRows.map((item) => item.userId);
    const response = await $axios.post(
      "/supplierUser/delete",
      { idList: selectedIds },
      { serverName: "nd-base2" }
    );
    if (response.data && response.data.code === 2000) {
      getTableData();
      page.table.selectedRows = [];
      ElMessage.success("删除成功");
    } else {
      ElMessage.error(response.data.message || "删除失败");
    }
  } catch (e) {
    if (e !== "cancel") {
      console.error("删除失败:", e);
      ElMessage.error("删除失败: " + e.message);
    }
  }
};

//删除
const handleDelete = async (row) => {
  console.log(row, "row");
  try {
    await ElMessageBox.confirm(
      "用户删除后将无法再登录平台，确定删除？",
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    const response = await $axios.post(
      "/supplierUser/delete",
      { idList: [row.userId] },
      { serverName: "nd-base2" }
    );
    if (response.data && response.data.code === 2000) {
      getTableData();
      ElMessage.success("删除成功");
    } else {
      ElMessage.error(response.data.message || "删除失败");
    }
  } catch (e) {
    if (e !== "cancel") {
      console.error("删除失败:", e);
      ElMessage.error("删除失败: " + e.message);
    }
  }
};
</script>

<style lang="scss" scoped>
.goto-detail {
  cursor: pointer;
  color: #409eff;
}
:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
