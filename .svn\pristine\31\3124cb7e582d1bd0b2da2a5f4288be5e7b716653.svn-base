<template>
    <div class="ndDescriptions">
        <el-descriptions v-bind="$attrs" :labelStyle="props.labelStyle" :column="props.column" :border="props.border">
            <slot />
        </el-descriptions>
    </div>
</template>

<script setup>
let props = defineProps({
    labelStyle: {
        type: Object,
        default() {
            return {
                'text-align': 'right',
                background: '#F6FAFF',
                'width': '170px',
                'font-size': '12px',
                padding: '6px 10px',
                color: '#666666'
            }
        }
    },
    column: {
        type: Number,
        default: 2
    },
    border: {
        type: Boolean,
        default: true
    }
})
</script>

<style lang="scss" scoped>
// 描述列表标题
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
    text-align: right;
    background: rgb(246, 250, 255);
    width: 170px;
    font-size: 14px;
    padding: 6px 10px;
    color: rgb(102, 102, 102);
    font-weight: normal;
}
:deep( .el-descriptions__label.el-descriptions__cell.is-bordered-label) {
    background-color: #f9f9f9;
}

// 描述列表内容
:deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content) {
    font-size: 14px;
    padding: 6px 10px;
    color: rgb(102, 102, 102);
    font-weight: normal;
}
</style>