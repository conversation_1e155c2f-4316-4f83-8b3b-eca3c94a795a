<template>
  <div class="header-box">
    <div class="left-box">
      <div class="system-name" @click="titleClick">{{ title }}</div>
      <slot></slot>
    </div>
    <div class="right-box">
      <!-- <div class="logout" @click="logout()"></div>
      <div class="text" @click="logout()">退出</div> -->
    </div>
  </div>
</template>

<script setup>
import { Location } from '@element-plus/icons-vue'
import { ref, inject, onMounted, reactive, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter();

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: "智慧工时系统"
  },
  // // 类型
  // type: {
  //   type: String,
  //   default: "list" // list detail echart
  // },
  // // 是否显示header
  // showHeader: {
  //   type: Boolean,
  //   default: true
  // },
  // // 是否显示footer
  // showFooter: {
  //   type: Boolean,
  //   default: true
  // },
})

const emit = defineEmits(['title-click'])

onMounted(() => {
  // // 获得菜单数据
  // getMenuData();
  // // 树转表格
  // treeToTable(menu.data);
})

// 退出
function logout() {
  ElMessageBox.confirm(
    '确定退出系统?',
    '退出系统',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    localStorage.setItem("syAuths", "");
    localStorage.setItem("syToken", "");
    router.push('loginView');
  }).catch(() => {

  })
}
// 标题点击
function titleClick() {
  emit("title-click");
}

// defineExpose({
//   getMenuName,
//   getMenuNameById,
//   getMenuPath,
//   getMenuUrl,
//   getMenuTarget,
// })
</script>

<style lang="scss" scoped>
.header-box {
  width: 100%;
  height: 58px;
  background-color: #ffffff;
  // border: 1px solid #F1F1F1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
  position:relative;
  box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.05);

  .left-box {
    width: auto;
    height: 100%;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;

    .system-name {
      width: auto;
      height: 30px;
      font-size: 22px;
      font-weight: bolder;
      letter-spacing: 0.1em;
      color: #0B8DF1;
      margin-right: 15px;
      cursor: pointer;
    }
  }

  .right-box {
    width: auto;
    height: 100%;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;

    .logout {
      width: 14px;
      height: 14px;
      background: url('@/assets/images/mainView/logout.png') no-repeat center center;
      background-size: 100% 100%;
      cursor: pointer;
    }

    .text {
      line-height: 0px;
      padding-left: 6px;
      cursor: pointer;
      font-size: 14px;
      color: #555555;
    }
  }
}
</style>