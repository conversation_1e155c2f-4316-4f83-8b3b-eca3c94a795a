<template>
    <ndb-page-list>
        <template #button>
            <nd-button
                @click="addOrEditRef?.open({ type: 'add' })"
                type="primary"
                icon="plus"
                >新建</nd-button
            >
            <nd-button @click="openImportDialog" icon="Download"
                >导入</nd-button
            >
            <!-- 添加删除按钮 -->
            <nd-button @click="deleteFarmer" icon="Delete">删除</nd-button>
            <!-- 添加重置密码按钮 -->
            <nd-button @click="resetPassword" icon="Key">重置密码</nd-button>
        </template>
        <template #search>
            <nd-search-more arrowMarginLeft="30px">
                <!-- 账户名称查询输入框 -->
                <nd-search-more-item title="账户名称">
                    <nd-input
                        v-model.trim="searchData.nickname"
                        placeholder="请输入账户名称"
                        clearable
                        style="width: 100%"
                    />
                </nd-search-more-item>
                <!-- 新增账号查询输入框 -->
                <nd-search-more-item title="账号">
                    <nd-input
                        v-model.trim="searchData.account"
                        placeholder="请输入账号"
                        clearable
                        style="width: 100%"
                    />
                </nd-search-more-item>
                <!-- 新增姓名查询输入框 -->
                <nd-search-more-item title="姓名">
                    <nd-input
                        v-model.trim="searchData.realname"
                        placeholder="请输入姓名"
                        clearable
                        style="width: 100%"
                    />
                </nd-search-more-item>
                <!-- 新增联系电话查询输入框 -->
                <nd-search-more-item title="联系电话">
                    <nd-input
                        v-model.trim="searchData.lxdh"
                        placeholder="请输入联系电话"
                        clearable
                        style="width: 100%"
                    />
                </nd-search-more-item>
                <!-- 新增身份证号查询输入框 -->
                <nd-search-more-item title="身份证号">
                    <nd-input
                        v-model.trim="searchData.idCard"
                        placeholder="请输入身份证号"
                        clearable
                        style="width: 100%"
                    />
                </nd-search-more-item>
                <template #footer>
                    <nd-button type="primary" @click="getTableData" authKey="">
                        查询
                    </nd-button>
                    <nd-button
                        @click="
                            reset();
                            getTableData();
                        "
                    >
                        重置
                    </nd-button>
                </template>
            </nd-search-more>
        </template>
        <template #table>
            <nd-table
                style="height: 100%"
                :data="tableData.data"
                @selection-change="handleSelectionChange"
                v-loading="tableData.loading"
            >
                <el-table-column
                    align="center"
                    label="#"
                    type="selection"
                    width="52px"
                    fixed="left"
                ></el-table-column>
                <!-- 添加位序列 -->
                <el-table-column
                    align="center"
                    label="序号"
                    width="60px"
                    type="index"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    label="账户名称"
                    prop=""
                    min-width="120"
                    show-overflow-tooltip
                >
                    <template v-slot="scope">
                        <div
                            @click="
                                addOrEditRef?.open({
                                    type: 'detail',
                                    memberId: scope.row.memberId,
                                })
                            "
                            style="color: #409eff; cursor: pointer"
                        >
                            {{ scope.row.nickname }}
                        </div>
                    </template>
                </el-table-column>
                <!-- 修改账号列的prop属性 -->
                <el-table-column
                    align="center"
                    label="账号"
                    prop="account"
                    min-width="100px"
                    show-overflow-tooltip
                ></el-table-column>
                <!-- 修改姓名列的prop属性 -->
                <el-table-column
                    align="center"
                    label="姓名"
                    prop="realname"
                    min-width="100px"
                    show-overflow-tooltip
                >
                </el-table-column>
                <!-- 修改联系电话列的prop属性 -->
                <el-table-column
                    align="center"
                    label="联系电话"
                    prop="lxdh"
                    show-overflow-tooltip
                    min-width="150"
                >
                    <template v-slot="scope">
                        <EncryptionDecryption
                            :key="tableData.loading + scope.row.lxdh"
                            :ciphertext="scope.row.lxdh"
                            :keyText="scope.row.lxdhJm"
                        />
                    </template>
                </el-table-column>
                <!-- 修改身份证号码列的prop属性 -->
                <el-table-column
                    align="center"
                    label="身份证号码"
                    prop="idCard"
                    show-overflow-tooltip
                    min-width="200"
                >
                    <template v-slot="scope">
                        <EncryptionDecryption
                            :key="tableData.loading + scope.row.idCardJm"
                            :ciphertext="scope.row.idCard"
                            :keyText="scope.row.idCardJm"
                        />
                    </template>
                </el-table-column>
                <!-- 修改状态列的prop属性 -->
                <el-table-column
                    align="center"
                    label="状态"
                    prop="status"
                    show-overflow-tooltip
                    width="110"
                >
                    <template v-slot="scope">
                        <div class="type">
                            <div
                                class="icon"
                                :class="{
                                    normal: scope.row.status === 1,
                                    disable: scope.row.status !== 1,
                                }"
                            ></div>
                            <div
                                class="status-tag"
                                v-if="scope.row.status === 1"
                            >
                                正常
                            </div>
                            <div class="status-tag" v-else>禁用</div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    fixed="right"
                    align="center"
                    label="操作"
                    width="190px"
                >
                    <template #default="scoped">
                        <div
                            style="display: flex; justify-content: space-around"
                        >
                            <nd-button
                                type="edit"
                                @click="
                                    addOrEditRef?.open({
                                        type: 'edit',
                                        memberId: scoped.row.memberId,
                                    })
                                "
                                >编辑</nd-button
                            >
                            <nd-button
                                type="edit"
                                @click="
                                    addAgreementRef?.open({
                                        type: 'add',
                                        memberId: scoped.row.memberId,
                                    })
                                "
                                >上传协议</nd-button
                            >
                            <nd-button
                                type="edit"
                                @click="seeOrder(scoped.row.memberId)"
                                >查看订单</nd-button
                            >
                        </div>
                    </template>
                </el-table-column>
            </nd-table>
        </template>
        <template #page>
            <nd-pagination
                :current-page="tableParams.page"
                :page-size="tableParams.size"
                :total="tableData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </template>
    </ndb-page-list>
    <ndb-import
        ref="importRef"
        titles="导入"
        projectId="1"
        modeType="yzhMember"
        @upbates="addRefresh"
    />
    <AddOrEdit
        ref="addOrEditRef"
        @addRefresh="addRefresh"
        @upDateRefresh="editRefresh"
    />
    <!-- 引入新增协议弹窗组件 -->
    <AddAgreement ref="addAgreementRef" />
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";

// 导入子组件
import AddOrEdit from "./components/AddOrEdit/index.vue";
import AddAgreement from "./components/AddAgreement/index.vue";
import EncryptionDecryption from "./components/EncryptionDecryption/index.vue";

// 导入vue
import {
    onMounted,
    reactive,
    ref,
    inject,
    watch,
    computed,
    nextTick,
} from "vue";
import {
    ElMessage as elMessage,
    ElMessageBox as elMessageBox,
} from "element-plus";

// hooks
import { useTable } from "./hooks/index/useTable";
import { useSearch } from "./hooks/index/useSearch";
import { useEvent } from "./hooks/index/useEvent";

// 定义axios
const $axios = inject("$axios");

// ref
const addOrEditRef = ref(null);
const addAgreementRef = ref(null);
const importRef = ref(null);

// useSearch
const { searchData, reset } = useSearch();

// useTable
const {
    tableData,
    tableParams,
    getTableData,
    handleSizeChange,
    handleCurrentChange,
    indexMethod,
    handleSelectionChange,
    addRefresh,
    editRefresh,
    resetPassword,
    deleteFarmer,
    seeOrder,
} = useTable({ searchData });

// useEvent
const {} = useEvent();

// 导入
function openImportDialog() {
    importRef.value.open();
}
</script>

<style lang="scss" scoped>
.eye-cont {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .eye-icon {
        display: flex;
        align-items: center;
        img {
            width: 14px;
            height: 14px;
            cursor: pointer;
        }
    }
}

.type {
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .normal {
        background-color: #00bf2e;
    }

    .disable {
        background-color: #ff0001;
    }
}

:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<style lang="scss">
.ExitConfirmButton {
    background: #068324 !important;
    border-color: #068324 !important;

    &:hover {
        opacity: 0.8;
    }
}

.ExitCancelButton {
    // background: #068324 !important;
    // border-color: #068324 !important;

    &:hover {
        background-color: rgba(133, 224, 154, 0.2) !important;
        color: #38864a !important;
        border-color: #38864a !important;
    }
}
</style>
