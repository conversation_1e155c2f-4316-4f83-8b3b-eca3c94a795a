<template>
  <nd-dialog
    ref="dialogRef"
    width="1282px"
    height="726px"
    :title="page.title"
    align-center
  >
    <div class="main">
      <!-- ---订单状态--- -->
      <div class="statusMain">
        <div class="mainTop">
          <div class="code">
            <span class="text1"
              >订单编号：
              <span class="text1">{{ page.goodsInfo.orderCode }}</span>
            </span>
            <div class="text2 copy" @click="copy(page.goodsInfo.orderCode)">
              <img src="../../../assets/images/order/copy.svg" alt="" />
            </div>
          </div>
          <div class="code">
            <span class="text1" @click="openxqDetail(page.goodsInfo)"
              >原需求单号：
              <span class="text3">{{ page.goodsInfo.demandCode }}</span>
            </span>
            <div class="text2 copy" @click="copy(page.goodsInfo.demandCode)">
              <img src="../../../assets/images/order/copy.svg" alt="" />
            </div>
          </div>
        </div>
        <div class="mainBox">
          <div class="left">
            <div class="status">
              <!-- 0：待出货，1：待收货，2：已完成，3：已取消 -->
              <span style="color: #ff8a00" v-if="page.goodsInfo.status == 0"
                >待出货</span
              >
              <span
                style="color: #ffbd00"
                v-else-if="page.goodsInfo.status == 1"
                >待收货</span
              >
              <span
                style="color: #068324"
                v-else-if="page.goodsInfo.status == 2"
                >已完成</span
              >
              <span
                style="color: #ff3132"
                v-else-if="page.goodsInfo.status == 3"
                >已取消</span
              >
            </div>
            <div class="dateline" v-if="page.goodsInfo.status == 0">
              待卖家(养殖户)出货
            </div>
            <div class="dateline" v-if="page.goodsInfo.status == 1">
              卖家已出货,待买家(商户)确认收货
            </div>
            <div class="dateline" v-if="page.goodsInfo.status == 2">
              买家已确认收货,订单完成
            </div>
            <div class="dateline" v-if="page.goodsInfo.status == 3">
              平台已取消订单
            </div>

            <div class="btn">
              <div
                class="button"
                v-if="page.goodsInfo.status == 0 && readOnlystatus == false"
                @click="openDeleteDialog(page.goodsInfo)"
              >
                取消订单
              </div>
              <div
                class="button"
                v-if="page.goodsInfo.status == 2 && readOnlystatus == false"
                @click="gotoJs(page.goodsInfo)"
              >
                结算
              </div>
            </div>
          </div>
          <div class="line"></div>
          <div class="right">
            <div class="iconFlow">
              <div class="flow">
                <div class="icon iconAct">
                  <img src="../../../assets/images/order/tjdd.svg" alt="" />
                </div>
                <div class="text">
                  <div>订单生成</div>
                  <div>{{ page.goodsInfo.orderTime }}</div>
                </div>
              </div>
              <div class="arrLine">
                <img src="@/assets/images/order/arrLine.svg" alt="" />
              </div>
              <template v-if="page.goodsInfo.status != 3">
                <div class="flow">
                  <div class="icon" v-if="page.goodsInfo.status == 0">
                    <img src="../../../assets/images/order/sjfh.svg" alt="" />
                  </div>
                  <div class="icon iconAct" v-else>
                    <img src="../../../assets/images/order/sjfh2.svg" alt="" />
                  </div>
                  <div class="text">
                    <div>卖家出货</div>
                    <div>{{ page.goodsInfo.chTime }}</div>
                  </div>
                </div>
                <div class="arrLine">
                  <img src="@/assets/images/order/arrLine.svg" alt="" />
                </div>
                <div class="flow">
                  <div
                    class="icon"
                    v-if="
                      page.goodsInfo.status == 0 || page.goodsInfo.status == 1
                    "
                  >
                    <img src="../../../assets/images/order/qrsh.svg" alt="" />
                  </div>
                  <div class="icon iconAct" v-else>
                    <img src="../../../assets/images/order/qrsh2.svg" alt="" />
                  </div>
                  <div class="text">
                    <div>买家确认收货</div>
                    <div>{{ page.goodsInfo.shTime }}</div>
                  </div>
                </div>
                <div class="arrLine">
                  <img src="@/assets/images/order/arrLine.svg" alt="" />
                </div>
                <div class="flow">
                  <div
                    class="icon"
                    v-if="
                      page.goodsInfo.status == 0 ||
                      page.goodsInfo.status == 1 ||
                      page.goodsInfo.status == 2
                    "
                  >
                    <img src="../../../assets/images/order/mjfk.svg" alt="" />
                  </div>
                  <div class="icon iconAct" v-else>
                    <img src="../../../assets/images/order/mjfk2.svg" alt="" />
                  </div>
                  <div class="text">
                    <div>结算</div>
                    <div>{{ page.goodsInfo.checkoutTime }}</div>
                  </div>
                </div>
              </template>
              <template v-if="page.goodsInfo.status == 3">
                <div class="flow">
                  <div class="icon iconAct">
                    <img src="../../../assets/images/order/gbdd.svg" alt="" />
                  </div>

                  <div class="text">
                    <div>订单取消</div>
                    <div>{{ page.goodsInfo.cancelTime }}</div>
                  </div>
                </div>
                <div class="arrLine"></div>
                <div class="flow"></div>
                <div class="arrLine"></div>
                <div class="flow"></div>
              </template>
            </div>
            <!-- <div class="textarea">
            <span class="label">卖家备注</span>
            <el-input v-model="textarea" class="textareaContent" type="textarea" placeholder="请输入内容" />
          </div> -->
          </div>
        </div>
      </div>
      <!-- ---订单信息--- -->
      <div class="orderMain">
        <div class="title">
          <div class="br"></div>
          <div class="text">订单信息</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>买家信息</span>
            </div>
            <div class="item2">
              <span>卖家信息</span>
            </div>
            <div class="item3">
              <span>订单信息</span>
            </div>
          </div>
          <div class="body">
            <div class="item1">
              <div class="infoItem">
                商户姓名：{{ page.goodsInfo.consumerName }}
              </div>
              <div class="infoItem">
                联系方式：{{ page.goodsInfo.consumerPhone }}
              </div>
              <div class="infoItem" :title="page.goodsInfo.consumerAddress">
                收货地址：{{ page.goodsInfo.consumerAddress }}
              </div>
            </div>
            <div class="item2">
              <div class="infoItem">
                养殖户名称：{{ page.goodsInfo?.producerName }}
              </div>
              <div class="infoItem">
                联系方式：{{ page.goodsInfo?.producerPhone }}
              </div>
              <div class="infoItem" :title="page.goodsInfo?.producerAddress">
                出货地址：{{ page.goodsInfo?.producerAddress }}
              </div>
            </div>
            <div class="item3">
              <div class="infoItem">
                订单编号：
                <span>{{ page.goodsInfo.orderCode }}</span>
                <span
                  class="text2 copy"
                  @click="copy(page.goodsInfo.orderCode)"
                >
                  <img src="../../../assets/images/order/copy.svg" alt=""
                /></span>
              </div>
              <div class="infoItem">
                下单时间：{{ page.goodsInfo.orderTime }}
              </div>
              <div
                class="infoItem"
                v-if="
                  page.goodsInfo.chTime &&
                  (page.goodsInfo.status == 1 || page.goodsInfo.status == 2)
                "
              >
                出货时间：{{ page.goodsInfo.chTime }}
              </div>
              <div
                class="infoItem"
                v-if="
                  page.goodsInfo.producerName &&
                  (page.goodsInfo.status == 1 || page.goodsInfo.status == 2)
                "
              >
                出货人：{{ page.goodsInfo.producerName }}
              </div>
              <div
                class="infoItem infoItemfile"
                v-if="
                  page.goodsInfo?.sourceJczs &&
                  page.goodsInfo?.sourceJczs.length > 0
                "
                @click="
                  showDialogFile(page.goodsInfo?.sourceJczs, 'DELIVERY_JCZS')
                "
              >
                <div>检测证书：</div>
                <!-- <ndb-upload
                  :files="page.goodsInfo?.sourceChpz"
                  fzgs="DELIVERY_JCZS"
                  :limit="15"
                  :disabled="true"
                ></ndb-upload> -->
                <img src="../../../assets/images/order/file.svg" alt="" />
                <span class="text">{{
                  page.goodsInfo?.sourceJczs.length
                }}</span>
              </div>
              <div
                class="infoItem infoItemfile"
                v-if="
                  page.goodsInfo?.sourceChpz &&
                  page.goodsInfo?.sourceChpz.length > 0
                "
                @click="
                  showDialogFile(page.goodsInfo?.sourceChpz, 'DELIVERY_CHPZ')
                "
              >
                <div>出货凭证：</div>
                <!-- <ndb-upload
                  :files="page.goodsInfo?.sourceJczs"
                  fzgs="DELIVERY_CHPZ"
                  :limit="15"
                  :disabled="true"
                ></ndb-upload> -->
                <img src="../../../assets/images/order/file.svg" alt="" />
                <span class="text">{{
                  page.goodsInfo?.sourceChpz.length
                }}</span>
              </div>
              <div
                class="infoItem infoItemfile"
                v-if="
                  page.goodsInfo?.sourceQtcl &&
                  page.goodsInfo?.sourceQtcl.length > 0
                "
                @click="
                  showDialogFile(page.goodsInfo?.sourceQtcl, 'DELIVERY_QTCL')
                "
              >
                <div>其他材料：</div>
                <!-- <ndb-upload
                  :files="page.goodsInfo?.sourceQtcl"
                  fzgs="DELIVERY_QTCL"
                  :limit="15"
                  :disabled="true"
                ></ndb-upload> -->
                <img src="../../../assets/images/order/file.svg" alt="" />
                <span class="text">{{
                  page.goodsInfo?.sourceQtcl.length
                }}</span>
              </div>
              <div class="infoItem" v-if="page.goodsInfo.shTime">
                收货时间：{{ page.goodsInfo.shTime }}
              </div>
              <div class="infoItem">
                订单状态：
                <span style="color: #333" v-if="page.goodsInfo.status == 0"
                  >待出货</span
                >
                <span style="color: #333" v-else-if="page.goodsInfo.status == 1"
                  >待收货</span
                >
                <span style="color: #333" v-else-if="page.goodsInfo.status == 3"
                  >已取消</span
                >
                <span style="color: #333" v-else-if="page.goodsInfo.status == 2"
                  >已完成</span
                >
              </div>
              <div class="infoItem" v-if="page.goodsInfo.status == 3">
                取消原因：{{ page.goodsInfo.cancelReason || "平台取消" }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- ---商品信息--- -->
      <div class="goodMain">
        <div class="title">
          <div class="br"></div>
          <div class="text">商品信息</div>
          <div class="priceAll">
            <div class="text1">订单总金额：</div>
            <div
              v-if="page.goodsInfo.status == 1 || page.goodsInfo.status == 2"
              class="text2"
            >
              ￥{{ page.goodsInfo.ddzje }}
            </div>
            <div v-else>-</div>
          </div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>商品名称</span>
            </div>
            <div class="item2">
              <span>数量(斤)</span>
            </div>
            <div class="item3">
              <span>单价(元/斤)</span>
            </div>
            <div class="item4">
              <span>实际出货量(斤)</span>
            </div>
            <div class="item5">
              <span>实际金额(元)</span>
            </div>
            <div class="item6">
              <span>出货状态</span>
            </div>
          </div>
          <div
            class="body"
            v-for="(good, index) in page.goodsInfo.productVos"
            :key="index"
          >
            <div class="item1">
              <!-- <div class="goodImg">
                <img :src="imgFile + good.productPic" alt="" />
              </div> -->
              <div class="goodInfo">
                <div class="title">
                  {{ good.productName }}
                </div>
                <div class="gg"></div>
              </div>
            </div>
            <div class="item2">
              <div class="infoItem">{{ good.xqAmount }}</div>
            </div>
            <div class="item3">
              <div class="infoItem">{{ good.priceShow }}</div>
            </div>
            <div class="item4">
              <div
                class="infoItem"
                v-if="page.goodsInfo.status == 1 || page.goodsInfo.status == 2"
              >
                {{ good.amount }}
              </div>
              <div class="infoItem" v-else>-</div>
            </div>
            <div class="item5">
              <div
                class="infoItem"
                v-if="page.goodsInfo.status == 1 || page.goodsInfo.status == 2"
              >
                ¥{{ good.sjje }}
              </div>
              <div class="infoItem" v-else>-</div>
            </div>
            <div class="item6">
              <div class="infoItem">
                <span style="color: #333" v-if="page.goodsInfo.status == 0"
                  >待出货</span
                >
                <span style="color: #333" v-else-if="page.goodsInfo.status == 1"
                  >已出货</span
                >
                <span style="color: #333" v-else-if="page.goodsInfo.status == 2"
                  >已出货</span
                >
                <span style="color: #333" v-else-if="page.goodsInfo.status == 3"
                  >待出货</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <nd-button type="" icon="Back" @click="close()">返&nbsp;回</nd-button>
    </template>
  </nd-dialog>

  <!-- 图片弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="附件预览"
    width="500"
    :before-close="handleClose"
  >
    <div class="dialogFiles">
      <ndb-upload
        :files="dialogFiles"
        fzgs="dialogfzgs"
        :limit="15"
        :disabled="true"
      ></ndb-upload>
    </div>
  </el-dialog>
  <!-- 需求详情 -->
  <xqDetail ref="xqdetailRef" @refresh="getTableData" />
</template>

<script setup>
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTooltip from "@/components/ndTooltip.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndbUpload from "@/components/business/ndbUpload/index.vue";
// 需求详情
import xqDetail from "@/views2/purchaseRequestFormView/components/Detail/index.vue";
// 导入vue
import {
  ref,
  inject,
  onMounted,
  reactive,
  nextTick,
  watch,
  onUnmounted,
} from "vue";
import { useRouter, useRoute } from "vue-router";
// 导入element-plus
import {
  ElMessage as elMessage,
  messageEmits,
  ElMessageBox as elMessageBox,
} from "element-plus";
const router = useRouter();
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义ref
const dialogRef = ref(null);
const addFormRef = ref(null);
// 定义page
const page = reactive({
  status: "",
  title: "",
  orderId: "",
  goodsInfo: {},
});

// 表单校验规则
const rules = reactive({
  name: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
});

// 打开弹窗
let readOnlystatus = ref(false);
function open(params, status, readOnly = false) {
  readOnlystatus.value = false;
  if (status === "detail") {
    page.status = "detail";
    page.title = "订单详情";
    readOnlystatus.value = readOnly;
    page.orderId = params.orderId;
    getDetail();
    getTime(page.orderId);
    dialogRef.value.open();
  }
}

// 获得详情
function getDetail() {
  $axios({
    url: "/buy/order/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: page.orderId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      let imgFile =
        window.ipConfig.base2 +
        "/common/download?token=" +
        localStorage.getItem("syToken") +
        "&path=";
      if (res.data.data.sourceChpz && res.data.data.sourceChpz.length > 0) {
        res.data.data.sourceChpz.map((item) => {
          item.fullPath = imgFile + item.sourcePath;
        });
      }
      if (res.data.data.sourceJczs && res.data.data.sourceJczs.length > 0) {
        res.data.data.sourceJczs.map((item) => {
          item.fullPath = imgFile + item.sourcePath;
        });
      }

      if (res.data.data.sourceQtcl && res.data.data.sourceQtcl.length > 0) {
        res.data.data.sourceQtcl.map((item) => {
          item.fullPath = imgFile + item.sourcePath;
        });
      }

      page.goodsInfo = res.data.data;
    }
  });
}

// 关闭
const close = () => {
  emit("before-close");
  dialogRef.value.close();
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 清空表单
  page.goods = {
    id: "",
    name: "",
    files: [],
  };
};

// 复制
const copy = (item) => {
  console.log("🚀 ~ copy ~ item:", item);
  let text = item;

  const tempInput = document.createElement("textarea");
  tempInput.value = text;
  tempInput.style.position = "fixed";
  tempInput.style.opacity = 0;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand("copy");
  tempInput.remove();
  elMessage({
    message: "复制成功",
    type: "success",
  });
};

// 暴露方法给父组件
defineExpose({
  open,
});

//超出省略触发内容
const tooltipShowFn = (flage, type, value1, value2) => {
  console.log(
    "🚀 ~ tooltipShowFn ~ flage, type, value1, value2:",
    flage,
    type,
    value1,
    value2
  );
  // flage 1:物流单号
  let returnText = "-";
  if (flage == "wl") {
    if (type == 1) {
      returnText = value1;
    }
    if (type == 2) {
      returnText = value2;
    }
  }
  console.log("🚀 ~ tooltipShowFn ~ returnText:", returnText);
  return returnText;
};
// 跳转结算
function gotoJs(params) {
  router.push({
    path: "/settlementOrderView",
    query: {
      orderCode: params.orderCode,
    },
  });
}
// new------------------------
const imgFile = ref(""); //图片路径
onMounted(() => {
  imgFile.value =
    window.ipConfig.base2 +
    "/common/download?token=" +
    localStorage.getItem("syToken") +
    "&path=";

  ///
});
// 取消
function openDeleteDialog(data) {
  elMessageBox
    .confirm("确定要取消订单?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      $axios({
        url: "/buy/order/cancel",
        method: "post",
        serverName: "nd-base2",
        data: {
          orderId: data.orderId,
          cancelRole: 3, // 用户类型 1:运营 2:供应商, //取消用户角色 1：商户 3：平台 4：系统
          cancelReason: "平台取消",
        },
      }).then((res) => {
        if (res.data.code === 2000) {
          elMessage({
            message: res.data.message,
            type: "success",
          });
          getDetail();
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    });
}

//日志  获取操作时间
let flowData = ref([]);
function getTime(orderId) {
  $axios({
    url: "/buy/order/log/findAll",
    method: "get",
    serverName: "nd-base2",
    data: {
      orderId: orderId,
      // cancelRole: 3, // 用户类型 1:运营 2:供应商, //取消用户角色 1：商户 3：平台 4：系统
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      let data = res.data.data;
      flowData.value = data;
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

//图片弹窗
const dialogVisible = ref(false);
let dialogFiles = ref([]);
let dialogfzgs = ref("");
const showDialogFile = (files, fzgs) => {
  dialogFiles.value = files;
  dialogfzgs.value = fzgs;
  dialogVisible.value = true;
};
function handleClose() {
  dialogFiles.value = [];
  dialogfzgs.value = "";
  dialogVisible.value = false;
}
// 需求详情
let xqdetailRef = ref(null);
const openxqDetail = (row) => {
  console.log("🚀 ~ openxqDetail ~ row:", row);
  xqdetailRef && xqdetailRef.value.open(row);
};
//new
</script>

<style lang="scss" scoped>
:deep(.nd-dialog-box) {
  padding: 0 !important;
}

:deep(.el-dialog__body) {
  background-color: #f7f7f7;
}

.main {
  width: 100%;
  min-height: 866px;
  background-color: #f7f7f7;
  display: flex;
  align-content: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  padding: 12px;

  // 订单状态
  .statusMain {
    width: 100%;
    min-height: 206px;
    border-radius: 5px;
    padding: 12px 24px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    display: flex;
    align-content: center;
    align-items: center;
    flex-wrap: wrap;
    .mainTop {
      width: 100%;
      height: 24px;
      display: flex;
      justify-content: flex-start;
      .code {
        height: 24px;
        font-size: 14px;
        margin-right: 30px;
        display: flex;
        .text1 {
          color: #999999;
        }

        .text2 {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
        }
        .text3 {
          color: #0098ff;
          cursor: pointer;
        }
        .copy {
          width: 18px;
          height: 18px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .mainBox {
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      align-content: center;
      align-items: center;
      .left {
        width: 284px;
        height: 182px;
        padding: 24px 0;
        box-sizing: border-box;
        .status {
          width: 100%;
          height: 24px;
          font-size: 22px;
          font-weight: 600;
          color: red;
          margin-top: 25px;
        }

        .dateline {
          width: 100%;
          height: 20px;
          line-height: 20px;
          font-size: 13px;
          font-weight: 400;
          color: #333;
          margin-top: 10px;
          letter-spacing: 0px;

          .min {
            color: #ff0001;
          }
        }

        .btn {
          width: 100%;
          height: 32px;
          margin-top: 10px;

          .button {
            width: 80px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            // padding: 4px 16px;
            color: #ffffff;
            gap: 8px;
            background: #068324;
            z-index: 2;
            cursor: pointer;
          }
        }
      }

      .line {
        width: 1px;
        height: 110px;
        background-color: #ebeef5;
        margin: 0 40px;
      }

      .right {
        min-width: 740px;
        height: 182px;
        display: flex;
        flex-wrap: wrap;

        .iconFlow {
          width: 100%;
          display: flex;
          align-content: center;
          align-items: center;
          justify-content: space-between;

          .flow {
            // min-width: 60px;
            height: 66px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            color: #666;
            .icon {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              display: flex;
              align-content: center;
              justify-content: center;
              align-items: center;

              img {
                width: 15px;
                height: 15px;
              }
            }

            .iconAct {
              background-color: #068324;
            }

            .text {
              width: 100%;
              height: 24px;

              div {
                white-space: nowrap;
                text-align: center;
                height: 24px;
                line-height: 24px;
              }
            }
          }

          .arrLine {
            width: 87.69px;
            height: 20px;
            margin: 0 0px 30px;
          }
        }

        .textarea {
          width: 100%;
          display: flex;
          align-items: center;

          .label {
            width: 66px;
            height: 22px;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            text-align: right;
            color: #555555;
            margin-right: 10px;
            z-index: 1;
          }

          .textareaContent {
            width: 658px;
            height: 80px !important; // 强制设置容器高度

            :deep(.el-textarea__inner) {
              height: 80px !important;
              min-height: 80px !important;
            }
          }
        }
      }
    }
  }

  // 订单信息
  .orderMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #303133;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
      }

      .body {
        width: 100%;
        min-height: 112px;
        display: flex;
      }

      .item1 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;

        .edit {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .item3 {
        flex: 1;
        padding: 8px 12px;
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;
        display: flex;
        color: #333;
        // 文字不打断
        white-space: nowrap;
        &:last-child {
          margin-bottom: 0px;
        }
        .copy {
          width: 18px;
          height: 18px;
          display: flex;
          align-content: center;
          align-items: center;
        }
      }
      .infoItemfile {
        display: flex;
        cursor: pointer;
        .text {
          color: #0098ff;
          margin-left: 5px;
        }
      }
    }
  }

  //退款原因
  .tukuanMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #303133;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
      }

      .body {
        width: 100%;
        min-height: 112px;
        display: flex;
      }

      .item1 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;

        .edit {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0px;
        }
      }
      .infoItemfile {
        display: flex;
      }
    }
  }

  // 商品信息
  .goodMain {
    width: 100%;
    min-height: 245px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #303133;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
        text-align: center;
        div {
          display: flex;
          justify-content: center;
          text-align: center;
          align-items: center;
          align-content: center;
        }
      }

      .body {
        width: 100%;
        height: 112px;
        display: flex;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }
      }

      .item1 {
        flex: 4;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        display: flex;

        .goodImg {
          width: 90px;
          height: 90px;
          border-radius: 4px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .goodInfo {
          // width: 100%;
          text-align: left;
          margin-left: 10px;

          .title {
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 10px;
            display: flex;
          }
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item3 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item4 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item5 {
        flex: 1;
        padding: 8px 12px;
        text-align: center;
        border-right: 1px solid #ebeef5;
      }
      .item6 {
        flex: 1;
        padding: 8px 12px;
        text-align: center;
      }
      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;
        color: #333;
        &:last-child {
          margin-bottom: 0px;
        }
      }
      .infoItemfile {
        display: flex;
      }
    }

    .priceAll {
      width: 50%;
      height: 24px;
      display: flex;
      justify-content: flex-end;
      align-content: center;
      align-items: center;
      margin-left: auto;
      .text1 {
        font-size: 14px;
        color: #333333;
        z-index: 0;
      }

      .text2 {
        opacity: 1;
        font-family: Microsoft YaHei;
        font-size: 22px;
        font-weight: bold;
        color: #ff0001;
        margin-left: 30px;
        text-decoration: none;
      }
    }
  }
}
.text2 {
  margin-left: 6px;
  color: #0098ff;
  text-decoration: underline;
  cursor: pointer;
}
.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 2px;
  background: #20b203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;
}

.gg {
  // 省略
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
//超链
.mfAbq {
  color: #0098ff;
  text-decoration: underline;
  cursor: pointer;
}
.dialogFiles {
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
}
</style>
