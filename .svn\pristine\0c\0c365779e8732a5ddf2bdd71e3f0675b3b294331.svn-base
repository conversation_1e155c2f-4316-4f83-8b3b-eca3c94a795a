import { onMounted, reactive } from "vue";
import axios from "@/http/index";
import { SearchData } from "./useSearch";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

/**
 * useTable
 * 
 * @param {Object} param0
 * @param {SearchData} param0.searchData 
 * @returns
 */
export function useTable({ searchData = null }) {
    const router = useRouter();

    const tableData = reactive({
        data: [],
        total: 0,
        tabelSelect: [],
        loading: false,
    })

    const tableParams = reactive({
        page: 1,
        size: 10
    })

    /**
     * 获取表格数据
     * 
     * @returns {void}
     */
    const getTableData = () => {
        tableData.loading = true;

        const params = {
            page: tableParams.page,
            size: tableParams.size,
            // 添加查询参数
            nickname: searchData.nickname,
            account: searchData.account,
            realname: searchData.realname,
            lxdh: searchData.lxdh,
            idCard: searchData.idCard,
        };

        axios({
            url: "/yhgl/yzhgl/findPage",
            method: "get",
            serverName: "nd-base2",
            params,
        }).then((res) => {
            if (res.data.code !== 2000) {
                ElMessage.error(res.data.message);
                return;
            }

            tableData.data = res.data.data?.records || [];
            tableData.total = res.data.data?.total ?? 0;
        }).finally(() => tableData.loading = false);
    }

    /**
     * 计算分页序号
     * 
     * @param {number} index 
     * @returns {number}
     */
    const indexMethod = (index) => {
        return (tableParams.page - 1) * tableParams.size + index + 1;
    }

    /**
     * 表格多选
     * 
     * @param {Array} val
     * @returns {void}
     */
    const handleSelectionChange = (val) => {
        tableData.tabelSelect = val;
    }

    /**
     * 页长变化
     * 
     * @param {number} size - 页长
     * @returns {void}
     */
    const handleSizeChange = (size) => {
        tableParams.size = size;
        tableParams.page = 1;
        getTableData();
    }

    /**
     * 页码变化
     * 
     * @param {number} page - 页码
     * @returns {void}
     */
    const handleCurrentChange = (page) => {
        tableParams.page = page;
        getTableData();
    }

    /**
     * 新增刷新页面
     * 
     * @returns {void}
     */
    const addRefresh = () => {
        tableParams.page = 1;
        getTableData();
    }

    /**
     * 编辑刷新
     * 
     * @returns {void}
     */
    const editRefresh = () => {
        getTableData();
    }

    /**
     * 重置密码
     * 
     * @returns {void}
     */
    const resetPassword = () => {
        if (!tableData.tabelSelect.length) {
            ElMessage.warning("请先勾选用户记录！");
            return;
        }

        ElMessageBox.confirm(`是否对这${tableData.tabelSelect.length}条数据进行密码重置？重置后不可恢复，请确认！`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(async () => {
            if (await resetPasswordApi()) {
                ElMessage.success("重置成功！用户初始化密码为ZHSY@8888");
                getTableData();
            }
        }).catch(() => { })
    }

    /**
     * 重置密码api
     * 
     * @returns {boolean}
     */
    const resetPasswordApi = async () => {
        tableData.loading = true;

        const data = {
            memberIds: tableData.tabelSelect.map(item => item.memberId),
        }

        return axios({
            url: "/yhgl/yzhgl/resetMemberPwd",
            method: "POST",
            serverName: "nd-base2",
            data,
        }).then((res) => {
            if (res.data.code !== 2000) {
                ElMessage.error(res.data.message);
                return false;
            }

            return true;

        }).catch(() => false).finally(() => tableData.loading = false);
    }

    /**
     * 删除养殖户
     * 
     * @returns {void}
     */
    const deleteFarmer = () => {
        if (!tableData.tabelSelect.length) {
            ElMessage.warning("请先勾选用户记录！");
            return;
        }

        ElMessageBox.confirm(`用户删除后将无法再登录小程序？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(async () => {
            if (await deleteFarmerApi()) {
                getTableData();
            }
        }).catch(() => { })

    }

    /**
     * 删除养殖户api
     * 
     * @returns {boolean}
     */
    const deleteFarmerApi = async () => {
        tableData.loading = true;

        const data = {
            memberIds: tableData.tabelSelect.map(item => item.memberId),
        }

        return axios({
            url: "/yhgl/yzhgl/deleteMember",
            method: "POST",
            serverName: "nd-base2",
            data,
        }).then((res) => {
            if (res.data.code !== 2000) {
                ElMessage.error(res.data.message);
                return false;
            }

            return true;

        }).catch(() => false).finally(() => tableData.loading = false);
    }

    /**
     * 查看订单
     * 
     * @param {string} memberId - 用户id
     * @returns {void}
     */
    const seeOrder = (memberId) => {
        console.log(memberId);

        router.push({
            path: "/orderView",
            query: {
                memberId: memberId
            }
        })
    }

    onMounted(() => {
        getTableData();
    })

    return {
        tableData,
        tableParams,
        getTableData,
        handleSizeChange,
        handleCurrentChange,
        indexMethod,
        handleSelectionChange,
        addRefresh,
        editRefresh,
        resetPassword,
        deleteFarmer,
        seeOrder
    }
}

