<template>
  <ndb-page-list>
    <template #tag>
      <nd-tag @click="tagClick(0)" :checked="page.search.data.label === 'pending'">全部</nd-tag>
      <nd-tag @click="tagClick(1)" :checked="page.search.data.label === 'approved'">待付款</nd-tag>
      <nd-tag @click="tagClick(2)" :checked="page.search.data.label === 'initiated'">待发货</nd-tag>
      <nd-tag @click="tagClick(3)" :checked="page.search.data.label === 'received'">已发货</nd-tag>
      <nd-tag @click="tagClick(9)" :checked="page.search.data.label === 'received'">已完成</nd-tag>
    </template>
    <!-- <template #button>
      <nd-button @click="openAddDialog()" type="primary" icon="plus" authKey="DEMO_VIEW_ADD">新建</nd-button>
      <nd-button @click="openImportDialog()" icon="Download" authKey="DEMO_VIEW_IMPORT">导入</nd-button>
      <nd-button @click="openExportDialog()" icon="Upload" authKey="DEMO_VIEW_EXPORT">导出</nd-button>
    </template> -->
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="商品类型" width="120px">
          <nd-select v-model="page.search.data.status" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.statusList" :label="item.label" :key="item.value"
              :value="item.value" />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="日期搜索" width="120px">
          <nd-select v-model="page.search.data.modelId" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.approveltypeList" :label="item.name" :key="item.modelId"
              :value="item.modelId" />
          </nd-select>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-date-picker v-model="page.search.data.cycleArr" range-separator="至" type="datetimerange"
            format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择"
            width="100%"></nd-date-picker>
        </nd-search-more-item>
        <nd-search-more-item title="关键字搜索" width="120px">
          <nd-select v-model="page.search.data.modelId" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.approveltypeList" :label="item.name" :key="item.modelId"
              :value="item.modelId" />
          </nd-select>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-input type="text" v-model="page.search.data.modelId" width="100%" placeholder="请输入" resize="none" />
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="getTableData" authKey=""> 查询 </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <nd-table border class="noPadding" style="height: 100%" :data="page.list.data"
        @selection-change="handleSelectionChange">
        <el-table-column align="center" label="#" type="selection" width="52px">
        </el-table-column>
        <el-table-column align="center" label="订单信息" prop="name" width="520px">
          <template #default="scoped">
            <div class="order_details">
              <div class="top">
                <div class="time"><span>下单时间</span> <span>{{ scoped.row.creationTime }}</span></div>
                &nbsp; &nbsp; &nbsp; &nbsp;
                <div class="code"><span>订单编号</span> <span>{{ scoped.row.code }}</span></div>
                <div class="total">共{{ scoped.row.total }}件商品</div>
                &nbsp; &nbsp; &nbsp; &nbsp;
                <div class="price"><span>总价</span> <span>{{ scoped.row.priceAll }}</span></div>
              </div>
              <div class="good">
                <div class="goodItem" v-for="items in scoped.row.orderList" :key="item">
                  <div class="left">
                    <img :src="items.image" alt="">
                  </div>
                  <div class="mid">
                    <div class="title">{{ items.title }}</div>
                    <div class="spac">{{ items.weight }}</div>
                  </div>
                  <div class="right">
                    <div class="price">{{ items.price }}</div>
                    <div class="number">x{{ items.count }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品类型" prop="originalPrice">
          <template #default="scoped">
            <div class="goodType">
              <div class="top"></div>
              <div v-for="item in scoped.row.orderList" :key="item" class="goodTypeItem">
                <span>优质鱼苗</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="收货信息" prop="discountPrice">
          <template #default="scoped">
            <div class="top2"></div>
            <div class="other">
              <div>收货人</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="联系方式" prop="discountPrice">
          <template #default="scoped">
            <div class="top2"></div>
            <div class="other">
              <div>联系方式</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="250px">
          <template #default="scoped">
            <div class="top2"></div>
            <div style="display: flex; justify-content: space-around">
              <nd-button type="edit" @click="openEditDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_EDIT">订单详情</nd-button>
              <nd-button type="edit" @click="openEditDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_EDIT">联系买家</nd-button>
              <nd-button type="delete" @click="openDeleteDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_DELETE">备注</nd-button>
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination :current-page="page.pager.pageIndex" :page-size="page.pager.pageSize" :total="page.pager.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </template>
  </ndb-page-list>
  <ndb-import ref="importRef" type="PRO_TASK" titles="任务导入" projectId="1" />
  <ndb-export ref="exportRef" />
  <add-dialog ref="addDialogRef" @before-close="getTableData"></add-dialog>
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import ndbExport from "@/components/business/ndbExport/index.vue";

// 导入子组件
import addDialog from "./components/addDialog.vue";

// 导入vue
import { onMounted, reactive, ref, inject, watch, computed, nextTick } from "vue";
import { ElMessage as elMessage, ElMessageBox as elMessageBox } from "element-plus";

// 定义axios
const $axios = inject("$axios");

// 定义ref
const addDialogRef = ref(null);
const exportRef = ref(null);
const importRef = ref(null);

// 定义page
const page = reactive({
  tree: {
    data: [],
    defaultProps: {
      children: "childs",
      label: "deptName",
    },
  },
  search: {
    data: {
      label: "pending",
      modelId: "",
      status: "",
      cycleArr: ["", ""],
    },
    dict: {
      approveltypeList: [], //审批类型
      statusList: [
        { label: "全部", value: "" },
        { label: "审批中", value: "1" },
        { label: "已通过", value: "2" },
        { label: "不通过", value: "3" },
        { label: "已撤回", value: "4" },
      ], //状态
    },
  },
  list: {
    data: [{}],
  },
  pager: {
    pageIndex: 1,
    pageSize: 30,
    total: 0,
  },
});

// 分类改变
function tagClick(index) {
  if (index === 0) {
    page.search.data.label = "pending";
  }
  if (index === 1) {
    page.search.data.label = "approved";
  }
  if (index === 2) {
    page.search.data.label = "initiated";
  }
  if (index === 3) {
    page.search.data.label = "received";
  }
  // 获得表格数据
  getTableData();
}

// 获得审批类型
function getApprovalType() {
  return
  $axios({
    url: "/oa/model/selectItem",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      page.search.dict.approveltypeList = res.data.data;
    }
  });
}

// 获得表格数据
function getTableData() {
  return
  $axios({
    url: "/order/getOrderListData",
    method: "get",
    data: {
      page: page.pager.pageIndex,
      size: page.pager.pageSize,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      console.log("🚀 ~ getTableData ~ res.data:", res.data)
      page.list.data = res.data.data.data;

      // page.pager.total = res.data.data.totalSize; //总页数
      // page.pager.pageIndex = res.data.data.page; //当前页
      // page.pager.pageSize = res.data.data.size; //每页记录数
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 重置
function reset() {
  page.search.data.modelId = "";
  page.search.data.status = "";
  page.search.data.cycleArr = ["", ""];
  getTableData();
}

// 打开新增对话框
function openAddDialog() {
  addDialogRef.value.open("add", null);
}

// 选中框
const selectedPoints = ref([]);
const handleSelectionChange = (val) => {
  selectedPoints.value = val.map((item) => item.buildId);
};

// 打开编辑dialog
function openEditDialog(params) {
  addDialogRef.value.open("edit", params);
}

// 打开详情dialog
function openDetail(params) {
  addDialogRef.value.open(params, "detail");
}

// 打开删除dialog
function openDeleteDialog(data) {
  elMessageBox
    .confirm("确定删除?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      $axios({
        url: "/goods/delete",
        method: "post",
        data: {
          goodsId: data.id,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          elMessage({
            message: "删除成功",
            type: "success",
          });
          getTableData();
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    });
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

// 导入
function openImportDialog() {
  importRef.value.open();
}

// 导出
function openExportDialog() {
  let params = {
    label: "approved",
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    label: page.search.data.label,
    status: page.search.data.status, // 流程状态
    modelId: page.search.data.modelId, // 审批类型
    startTime: page.search.data.cycleArr[0] ? searchData.cycleArrfq[0] + ":00" : "", // 发起开始时间
    endTime: page.search.data.cycleArr[1] ? searchData.cycleArrfq[1] + ":00" : "", // 发起结束时间
  };
  exportRef.value.open(params, "OA_EXAMINE_LIET", "");
}

// onMounted
onMounted(() => {
  // 获得审批类型
  getApprovalType();
  // 获得表格数据
  getTableData();
});
</script>

<style lang="scss" scoped>
.workhour {
  color: #444444;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;

  .workhour-item-line {
    width: 4px;
    height: 15px;
    border-radius: 3px;
    background-color: red;
    margin-right: 12px;
  }

  .workhour-item {
    margin-right: 23px;

    .red {
      color: red;
    }

    .blue {
      color: #10a3e7;
    }
  }
}

:deep(.noPadding .el-table__cell) {
  padding: 0 !important;
}

:deep(.noPadding .cell) {
  padding: 0;
  height: 100%;
}


// 订单详情
.order_details {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .good {
    width: 100%;


    .goodItem {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 3px 5px;
      border-bottom: 1px solid #ebeef5;

      // 左
      .left {
        width: 80px;
        height: 80px;

        img {
          width: 80px;
          height: 80px;
        }
      }

      //中
      .mid {
        width: 240px;
        line-height: 40px;
        text-align: left;
        margin-left: 20px;
      }

      // 右
      .right {
        width: 180px;
        line-height: 40px;
        margin-left: auto;
      }
    }

    :last-of-type {
      border: none;
    }
  }


}

//商品类型
.goodType {

  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;

  // padding-top: 35px;
  .goodTypeItem {
    box-sizing: content-box;
    padding: 3px 5px;
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #ebeef5;
  }

  :last-of-type {
    border: none;
  }
}

//收货信息
.shippingInfo {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}



.top {
  width: 100%;
  height: 30px;
  background-color: #FDEDED !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
}

.top2 {
  width: 100%;
  height: 30px;
  background-color: #FDEDED !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
  position: absolute;
  top: 0;
}
</style>
