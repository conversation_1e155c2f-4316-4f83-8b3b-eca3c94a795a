import { reactive } from "vue"

export class SearchData {
    /** @type {string} 昵称 */
    nickname;
    /** @type {string} 账号 */
    account;
    /** @type {string} 真实姓名 */
    realname;
    /** @type {string} 联系方式 */
    lxdh;
    /** @type {string} 身份证号码 */
    idCard;
}

export function useSearch() {
    const searchData = reactive({
        nickname: "",
        account: "",
        realname: "",
        lxdh: "",
        idCard: "",
    })

    /**
     * 重置
     * 
     * @returns {void}  
     */
    const reset = () => {
        searchData.nickname = "";
        searchData.account = "";
        searchData.realname = "";
        searchData.lxdh = "";
        searchData.idCard = "";
    }

    return {
        searchData,
        reset
    }
}