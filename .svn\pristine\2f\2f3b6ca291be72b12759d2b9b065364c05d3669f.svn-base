<template>
  <nd-dialog ref="dialogRef" title="菜单信息管理" width="60vw" height="60vh">
    <div class="dialogBox" v-loading="loading">
      <div class="borderBox">
        <el-form ref="addFormRef" :model="page.menu" :rules="rules" class="add-box">
          <el-row>
            <el-col :span="12">
              <el-form-item label="菜单名称" label-width="100px" prop="menuName">
                <nd-input v-model.trim="page.menu.menuName" :disabled="page.status == 'detail' ? true : false"
                  placeholder="请输入" width="100%" maxlength="20"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上级菜单" label-width="100px" v-if="page.menu.menuPid > 0">
                {{ page.menu.menuPName }}
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="page.menu.menuPid == 0">
              <el-form-item label="图标" label-width="100px">
                <el-select v-model="page.menu.iconUrl" popper-class="iconSelect" clearable value-on-clear="">
                  <el-option class="iconOption" v-for="item in page.menuIcons" :key="item" :label="item" :value="item">
                    <!-- <span>{{ item }}</span> -->
                    <el-icon size="24">
                      <component :is="item"></component>
                    </el-icon>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="排序" label-width="100px" prop="orderCode">
                <nd-input v-model.trim="page.menu.orderCode" :disabled="page.status == 'detail' ? true : false"
                  placeholder="请输入" width="100%" maxlength="20" type2="number3"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="URL" label-width="100px" prop="menuUrl">
                <nd-input v-model.trim="page.menu.menuUrl" :disabled="page.status == 'detail' ? true : false"
                  placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="描述" label-width="100px" prop="describes">
                <nd-input v-model.trim="page.menu.describes" :disabled="page.status == 'detail' ? true : false"
                  type="textarea" placeholder="请输入" width="100%" maxlength="200" show-word-limit></nd-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <template #footer>
      <template v-if="page.status != 'detail'">
        <nd-button type="primary" icon="FolderChecked" @click="save">保&nbsp;存</nd-button>
        <nd-button type="" icon="Close" @click="close">取&nbsp;消</nd-button>
      </template>
      <template v-else>
        <nd-button type="" icon="Close" @click="close">关&nbsp;闭</nd-button>
      </template>
    </template>
  </nd-dialog>
</template>

<script setup>
import { nextTick, reactive, ref } from "vue";
import ndDialog from "@/components/ndDialog.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";

import axios from "axios";
import { ElMessage } from "element-plus";
let emit = defineEmits(["before-close"]);

const dialogRef = ref(null);
const page = reactive({
  status: "detail",
  menu: {
    menuId: "0",
    menuPid: "0",
    menuName: "",
    iconUrl: "",
    menuPName: "",
    menuUrl: "",
    describes: "",
    orderCode: "",
  },
  menuIcons: ["Money",
    "Collection",
    "DataAnalysis",
    "PieChart",
    "Tickets",
    "User",
    "Memo",
    "Suitcase",
    "Setting",
    "Coin",
    "OfficeBuilding",
    "Document",
  ],
});
let loading = ref(false)

const addFormRef = ref(null);

// 表单校验规则
const rules = reactive({
  menuName: [{ required: true, message: "请输入菜单名称！", trigger: ["blur", "change"] }],
  menuUrl: [{ required: false, message: "请输入URL！", trigger: ["blur", "change"] }],
});

function open(type, menuId, menuPName, parentId) {
  page.status = type;

  Object.keys(page.menu).forEach((key) => {
    page.menu[key] = "";
  });
  page.menu.menuId = menuId;
  page.menu.menuPid = parentId || 0;
  page.menu.menuPName = menuPName || "";
  if (type == 'edit') getmenuDetail()

  if (parentId > 0) {
    rules.menuUrl[0].required = true;
  } else {
    rules.menuUrl[0].required = false;
  }

  dialogRef.value?.open();
  if (addFormRef.value) addFormRef.value.resetFields();
}

function getmenuDetail() {
  axios({
    url: "/menu/find",
    method: "get",
    data: { menuId: page.menu.menuId },
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      page.menu.menuId = res.data.data.menuId
      page.menu.menuPid = res.data.data.menuPid
      page.menu.menuName = res.data.data.menuName
      page.menu.iconUrl = res.data.data.iconUrl
      page.menu.menuPName = res.data.data.menuPName
      page.menu.menuUrl = res.data.data.menuUrl
      page.menu.describes = res.data.data.describes
      page.menu.orderCode = res.data.data.orderCode
    } else {
      ElMessage.error(res.data.message);
    }
  })
}

function iconClick(item) {
  console.log('item', item);
  page.menu.iconUrl = item;
}

function save() {
  // 校验
  addFormRef.value.validate((valid) => {
    if (valid) {
      const postData = {
        menuName: page.menu.menuName,
        menuUrl: page.menu.menuUrl,
        iconUrl: page.menu.iconUrl,
        menuPid: page.menu.menuPid,
        describes: page.menu.describes,
        orderCode: page.menu.orderCode,
      }
      if (page.menu.menuId > 0) {
        editData(postData)
      } else {
        addData(postData)
      }
    }
  });
}

function addData(postData) {
  axios({
    url: "/menu/save",
    method: "post",
    data: postData,
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("保存成功！");
      emit("before-close");
      close();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

function editData(postData) {
  postData.menuId = page.menu.menuId
  axios({
    url: "/menu/update",
    method: "post",
    data: postData,
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("保存成功！");
      emit("before-close");
      close();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

// 关闭
const close = () => {
  // 清空校验
  if (addFormRef.value) addFormRef.value.resetFields();

  dialogRef.value.close();
};

defineExpose({
  open,
});
</script>

<style lang="scss">
.iconSelect .el-select-dropdown__list {
  width: 400px;
  display: flex !important;
  flex-wrap: wrap;
  padding: 6px;
  gap: 6px 0;

  .el-select-dropdown__item.is-selected {
    color: #068324;
  }

  .iconOption {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
  }
}
</style>
<style lang="scss" scoped>
:deep(.hideAllCheck .el-checkbox) {
  display: none;
}

.dialogBox {
  padding: 15px 12px;
  min-height: 100%;
  display: flex;

  :deep(.el-textarea__inner) {
    height: 80px;
  }
}

.borderBox {
  border: 1px solid #EAEAEA;
  background-color: #fff;
  padding: 12px;
  border-radius: 5px;
}

.clickInput {
  cursor: pointer;
  width: 100%;
  border-radius: 4px;
  height: 34px;
  line-height: 34px;
  border: 1px solid #dcdfe6;
  padding: 0px 12px;
  font-size: 14px;
  background-color: #ffffff;
  color: var(--el-input-text-color, var(--el-text-color-regular));
}

.iconList {
  width: 410px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 20px;
}
</style>