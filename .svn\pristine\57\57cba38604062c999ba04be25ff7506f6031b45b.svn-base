<template>
  <ndb-page-list>
    <template #tag>
      <nd-tag @click="tagClick(0)" :checked="page.search.data.label === '0'">全部</nd-tag>
      <nd-tag @click="tagClick(1)" :checked="page.search.data.label === '1'">出售中</nd-tag>
      <nd-tag @click="tagClick(2)" :checked="page.search.data.label === '2'">已售完</nd-tag>
      <nd-tag @click="tagClick(3)" :checked="page.search.data.label === '3'">已下架</nd-tag>
    </template>
    <template #button v-if="page.userType == 1">
      <nd-button @click="openAddDialog()" type="primary" icon="plus" authKey="">添加商品</nd-button>
      <nd-button @click="upOrDownSaleBefore(1, 0, selectedPoints)" authKey="">上架</nd-button>
      <nd-button @click="upOrDownSaleBefore(0, 0, selectedPoints)" authKey="">下架</nd-button>
      <nd-button @click="mulDelete" icon="delete" authKey="">删除</nd-button>
    </template>
    <template #search>
      <nd-search-more arrowMarginLeft="325px">
        <nd-search-more-item title="商品类型">
          <nd-select v-model="page.search.data.type" placeholder="全部" clearable :value-on-clear="''">
            <el-option v-for="item in page.search.dict.categoryList" :label="item.name" :key="item.categoryId"
              :value="item.categoryId" />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="价格区间">
          <nd-input v-model.trim="page.search.data.lowPrice" placeholder="请输入" clearable type2="number02"
            @change="priceChange(1)" />
          <span style="margin: 0 5px">-</span>
          <nd-input v-model.trim="page.search.data.highPrice" placeholder="请输入" clearable type2="number02"
            @change="priceChange(2)" />
        </nd-search-more-item>
        <nd-search-more-item title="创建日期">
          <nd-date-picker v-model="page.search.data.allTime" start-placeholder="开始日期" end-placeholder="结束日期"
            range-separator="至" type="daterange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择"
            width="100%" @change="timeChange"></nd-date-picker>
        </nd-search-more-item>
        <nd-search-more-item title="关键字">
          <nd-input v-model.trim="page.search.data.keyWords" placeholder="请输入" clearable />
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="handleSearch" authKey="">
            查询
          </nd-button>
          <nd-button @click="handleReset"> 清空 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <nd-table style="height: 100%" :data="page.list.data" @selection-change="handleSelectionChange" :rowSpan="true"
        rowSpanId="productId" :span-method="objectSpanMethod">
        <el-table-column align="center" label="#" type="selection" width="52px" />
        <el-table-column align="center" label="商品编号" prop="productCode" show-overflow-tooltip min-width="100px" />
        <el-table-column align="center" label="商品图片" prop="pic" min-width="100px">
          <template #default="{ row }">
            <el-image style="cursor: pointer" :src="row.pic" fit="cover" @click="openImageViewer(row)" />
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" label="商品名称" prop="name" show-overflow-tooltip width="160">
          <template #default="{ row }">
            <span class="curp" @click="openDetailDialog(row)">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" label="商品描述" prop="description" show-overflow-tooltip
          min-width="160px" />
        <el-table-column align="center" label="规格" prop="spData" min-width="150px" show-overflow-tooltip />
        <el-table-column align="center" label="单价" min-width="100px">
          <template #default="{ row }">
            {{ row.agreementPrice ? row.agreementPrice : row.price }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="可售库存" prop="diffStock" min-width="110px" />
        <el-table-column align="center" label="锁定库存" prop="diffLockStock" min-width="110px" />
        <el-table-column align="center" label="商品类型" prop="categoryName" min-width="100px" />
        <el-table-column align="center" label="状态" prop="statusCn">
          <template #default="{ row }">
            <div class="circleIcon" :style="{
              backgroundColor: (row.statusCn === '已下架' ? '#FF0001'
                : row.statusCn == '出售中' ? '#0B8DF1' : '#FF8A00')
            }">
            </div>{{ row.statusCn }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="商城标签" prop="tagKeys" width="96px">
          <template #default="{ row }">
            <template v-for="item in row.tagList" :key="item">
              <div class="tableTags" v-if="item == 'INDEX_JRTJ'" style="background-color: #FF891A;">今日特价</div>
              <div class="tableTags" v-if="item == 'INDEX_XHYM'" style="background-color: #008C72;">鲜活鱼苗</div>
              <div class="tableTags" v-if="item == 'INDEX_YXSL'" style="background-color: #1E79E8;">优选饲料</div>
              <div class="tableTags" v-if="item == 'INDEX_YMSX'" style="background-color: #B71FE1;">鱼苗上新</div>
              <div class="tableTags" v-if="item == 'INDEX_CPSX'" style="background-color: #059527;">产品上新</div>
              <div class="tableTags" v-if="item == 'INDEX_SLSX'" style="background-color: #EB1C98;">饲料上新</div>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" prop="insertTime" width="160" />
        <el-table-column fixed="right" align="center" label="操作" width="140px">
          <template #default="scoped">
            <div class="caozuoBtn">
              <nd-button type="edit" @click="openDetailDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_CHECK">查看</nd-button>
              <nd-button type="edit" v-if="scoped.row.showEditButton" @click="openEditDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_EDIT">编辑</nd-button>
              <nd-button type="approve" v-if="scoped.row.showUpButton"
                @click="upOrDownSaleBefore(1, 1, [scoped.row.productId])" authKey="">上架</nd-button>
              <nd-button type="deal" v-if="scoped.row.showDownButton"
                @click="upOrDownSaleBefore(0, 1, [scoped.row.productId])" authKey="">下架</nd-button>
              <nd-button type="delete" v-if="scoped.row.showDeleteButton" @click="openDeleteDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_DELETE">删除</nd-button>
              <nd-button type="deal" v-if="scoped.row.showAddStockButton" @click="openAddStockDialog(scoped.row)"
                authKey="">编辑库存</nd-button>
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination :current-page="page.pager.pageIndex" :page-size="page.pager.pageSize" :total="page.pager.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </template>
  </ndb-page-list>
  <add-dialog ref="addDialogRef" @before-close="getTableData"></add-dialog>
  <detail-dialog ref="detailDialogRef" @before-close="getTableData"></detail-dialog>
  <add-stock-dialog ref="addStockDialogRef" @before-close="getTableData"></add-stock-dialog>
  <el-image-viewer v-if="page.imageViewer.show" :url-list="page.imageViewer.urlList"
    @close="page.imageViewer.show = false" />
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";

// 导入子组件
import addDialog from "./components/addDialog.vue";
import detailDialog from "./components/detailDialog.vue";
import addStockDialog from "./components/addStockDialog.vue";

// 导入vue
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  computed,
  nextTick,
} from "vue";
import {
  ElMessage as elMessage,
  ElMessageBox as elMessageBox,
} from "element-plus";

// 定义axios
const $axios = inject("$axios");

// 定义ref
const addDialogRef = ref(null);
const detailDialogRef = ref(null);

const addStockDialogRef = ref(null);
// 定义page
const page = reactive({
  userType: 2, // 用户类型 1:运营 2:供应商
  search: {
    data: {
      label: "0", //页签状态 0全部 1出售中 2已售完 3已下架
      type: "0", // 商品类型
      lowPrice: "", //最低价
      highPrice: "", ///最高价
      allTime: [], //完整时间
      startTime: "", //开始时间
      endTime: "", //截至时间
      keyWords: "", //关键字模糊查询
    },
    dict: {
      categoryList: [], //商品类别
      approveltypeList: [], //审批类型
    },
  },
  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  },
  imageViewer: {
    show: false,
    urlList: [],
  },
});

// 获得表格数据
function getTableData() {
  let data = {
    // 搜索
    label: page.search.data.label,
    type: page.search.data.type === "0" ? "" : page.search.data.type,
    lowPrice: quiteCheckPrice() ? page.search.data.lowPrice > 0 ? Math.round(page.search.data.lowPrice * 100) : page.search.data.lowPrice : "",
    highPrice: quiteCheckPrice() ? page.search.data.highPrice > 0 ? Math.round(page.search.data.highPrice * 100) : page.search.data.highPrice : "",
    startTime: page.search.data.startTime,
    endTime: page.search.data.endTime,
    keyWords: page.search.data.keyWords,
    // 分页
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
  };

  $axios({
    url: "/goods/getAll",
    method: "get",
    data,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.dataList;

      page.userType = localStorage.getItem("syUserType") || 2; // 用户类型 1:运营 2:供应商
      console.log("page.userType", page.userType);
      page.list.data = page.list.data.map((item, index) => {
        // 处理标签
        item.tagList = item.tagKeys ? item.tagKeys.split(',') : []
        if (item.publishStatus == 1 && item.diffStock > 0) {
          let btnStatus = [false, false, false, true, false]; // 和下面的状态一致
          if (page.userType != 1) btnStatus[3] = false // 供应商不允许下架
          if (page.userType != 1) btnStatus[4] = true // 只有供应商允许编辑库存
          if (index > 0) setAllSelling(item.productId, index, btnStatus)
          item.status = "selling";
          item.statusCn = "出售中";
          item.showEditButton = false;
          item.showDeleteButton = false;
          item.showUpButton = false;
          item.showDownButton = true;
          item.showAddStockButton = false;
        } else if (item.publishStatus == 1 && item.diffStock <= 0) {
          let type = 2 // 1:出售中 2:已售完
          if (index > 0 && page.list.data[index - 1].productId == item.productId && page.list.data[index - 1].status == "selling") type = 1
          if (type == 1) {
            item.status = "selling";
            item.statusCn = "出售中";
            item.showEditButton = false;
            item.showDeleteButton = false;
            item.showUpButton = false;
            item.showDownButton = true;
            item.showAddStockButton = false;
          } else if (type == 2) {
            item.status = "sellout";
            item.statusCn = "已售完";
            item.showEditButton = false;
            item.showDeleteButton = true;
            item.showUpButton = false;
            item.showDownButton = false;
            item.showAddStockButton = false;
          }
        } else if (item.publishStatus == 0) {
          item.status = "down";
          item.statusCn = "已下架";
          item.showEditButton = true;
          item.showDeleteButton = true;
          item.showUpButton = true;
          item.showDownButton = false;
          item.showAddStockButton = false;
        }
        item.previewPic = [item.pic];
        // 运营不给编辑库存权限
        if (page.userType == 1) {
          item.showAddStockButton = false;
        }
        // 当用户类型为供应商时，仅支持查看当前供应商关联的商品，按钮权限暂仅支持【查看】和【编辑库存】（按钮授权）
        if (page.userType != 1) {
          item.showEditButton = false;
          item.showDeleteButton = false;
          item.showUpButton = false;
          item.showDownButton = false;
          item.showAddStockButton = true;
        }
        return item
      });
      console.log('pageeeee', page.list.data);

      page.pager.total = res.data.data.totalSize; //总页数
      page.pager.pageIndex = res.data.data.page; //当前页
      page.pager.pageSize = res.data.data.size; //每页记录数
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

function setAllSelling(id, index, statusArr) {
  if (index > 0) {
    let lastId = page.list.data[index - 1].productId
    if (id == lastId) {
      page.list.data[index - 1].status = "selling";
      page.list.data[index - 1].statusCn = "出售中";
      page.list.data[index - 1].showEditButton = statusArr[0];
      page.list.data[index - 1].showDeleteButton = statusArr[1];
      page.list.data[index - 1].showUpButton = statusArr[2];
      page.list.data[index - 1].showDownButton = statusArr[3];
      page.list.data[index - 1].showAddStockButton = statusArr[4];
      setAllSelling(id, index - 1, statusArr)
    }
  }
}

// 分类改变
function tagClick(index) {
  if (index === 0) {
    page.search.data.label = "0";
  }
  if (index === 1) {
    page.search.data.label = "1";
  }
  if (index === 2) {
    page.search.data.label = "2";
  }
  if (index === 3) {
    page.search.data.label = "3";
  }
  page.pager.pageIndex = 1
  // 获得表格数据
  getTableData();
}

// 获取商品分类
function getCategoryList() {
  $axios({
    url: "/dict/getCategory",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      let obj = {
        categoryId: "0",
        name: "全部",
      };
      page.search.dict.categoryList = [obj, ...res.data.data];
    }
  });
}

// 校验价格
const checkPrice = () => {
  let isCheck = false;
  if (page.search.data.lowPrice && page.search.data.highPrice) {
    if (
      parseFloat(page.search.data.lowPrice) >=
      parseFloat(page.search.data.highPrice)
    ) {
      elMessage.warning("最高价格需大于最低价格");
    } else {
      isCheck = true;
    }
  } else {
    if (!(!page.search.data.lowPrice && !page.search.data.highPrice)) {
      elMessage.warning("仅支持区间价格查询");
    } else {
      isCheck = true;
    }
  }
  return isCheck;
};

// 校验价格
const quiteCheckPrice = () => {
  let isCheck = false;
  if (page.search.data.lowPrice && page.search.data.highPrice) {
    if (
      parseFloat(page.search.data.lowPrice) <
      parseFloat(page.search.data.highPrice)
    ) {
      isCheck = true;
    }
  } else {
    if (!page.search.data.lowPrice && !page.search.data.highPrice) {
      isCheck = true;
    }
  }
  return isCheck;
};

// 查询
const handleSearch = () => {
  let validateRes = checkPrice();
  if (!validateRes) return;
  page.pager.pageIndex = 1
  getTableData();
};

// 重置
function handleReset() {
  page.search.data.type = "0";
  page.search.data.lowPrice = "";
  page.search.data.highPrice = "";
  page.search.data.allTime = [];
  page.search.data.startTime = "";
  page.search.data.endTime = "";
  page.search.data.keyWords = "";
  page.pager.pageIndex = 1
  getTableData();
}

// 合并单元格
var rowspanGlobal = 0;
var oldRowIndex = 0;
function objectSpanMethod({ column, columnIndex, row, rowIndex }) {
  if (oldRowIndex !== rowIndex) {
    rowspanGlobal = 0;
    oldRowIndex = rowIndex;
  }
  if (columnIndex === 0) {
    // 获取当前单元格的值
    const currentValue = row["productId"];

    // 获取上一行相同列的值
    const preRow = page.list.data[rowIndex - 1];
    const preValue = preRow ? preRow["productId"] : null;

    // 如果当前值和上一行的值相同，则将当前单元格隐藏
    if (currentValue === preValue) {
      return {
        rowspan: 0,
        colspan: 0,
      };
    } else {
      // 否则计算当前单元格应该跨越多少行
      let rowspan = 1;
      for (let i = rowIndex + 1; i < page.list.data.length; i++) {
        const nextRow = page.list.data[i];
        const nextValue = nextRow["productId"];
        if (nextValue === currentValue) {
          rowspan++;
        } else {
          break;
        }
      }
      rowspanGlobal = rowspan;
      return {
        rowspan: rowspan,
        colspan: 1,
      };
    }
  }
  if (
    columnIndex === 1 ||
    columnIndex === 2 ||
    columnIndex === 3 ||
    columnIndex === 4 ||
    columnIndex === 9 ||
    columnIndex === 10 ||
    columnIndex === 11 ||
    columnIndex === 12 ||
    columnIndex === 13
  ) {
    return {
      rowspan: rowspanGlobal,
      colspan: 1,
    };
  }
}

// 打开新增对话框
function openAddDialog() {
  addDialogRef.value.open("add", null);
}

// 选中框
const selectedPoints = ref([]);
const handleSelectionChange = (val) => {
  selectedPoints.value = val.map((item) => item.productId);
};

function openDetailDialog(params) {
  detailDialogRef.value.open(params);
}

// 打开编辑dialog
function openEditDialog(params) {
  // let productId = params.productId
  // let listById = page.list.data.filter(ele => ele.productId === productId)
  // let totalLock = listById.reduce((sum, item) => sum + item.lockStock * 1, 0);
  // if (totalLock > 0) return elMessage.error("有待支付订单，暂不支持编辑")
  addDialogRef.value.open('edit', params);
}

// 打开删除dialog
function openDeleteDialog(data) {
  elMessageBox
    .confirm("确定删除?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: 'ExitConfirmButton',
      cancelButtonClass: 'ExitCancelButton',
      customClass: 'ExitCustomClass'
    })
    .then(() => {
      $axios({
        url: "/goods/delete",
        method: "post",
        data: {
          goodsId: [data.productId],
        },
      }).then((res) => {
        if (res.data.code === 2000) {
          elMessage({
            message: res.data.message,
            type: "success",
          });
          page.pager.pageIndex = 1
          getTableData();
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    });
}

// 打开补库存dialog
function openAddStockDialog(params) {
  // 8个9
  // if (params.diffStock && params.diffStock * 1 >= 99999999) return elMessage.error("当前库存超出允许范围，无法补库存")

  addStockDialogRef.value.open(params);
}

// 批量删除
function mulDelete() {
  if (selectedPoints.value.length < 1) {
    elMessage.warning("请选择");
  } else {
    const idStocksMap = new Map();
    selectedPoints.value.forEach(id => {
      idStocksMap.set(id, { id: id });
    });

    let errorMsg = ""
    page.list.data.forEach(ele => {
      if (errorMsg) return
      if (idStocksMap.has(ele.productId)) {
        if (ele.status === 'selling') return errorMsg = "出售中商品不支持删除！"
      }
    });
    // 检验
    if (errorMsg) {
      elMessage.warning(errorMsg);
    } else {
      let goodsId = selectedPoints.value;
      elMessageBox
        .confirm("确定删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          confirmButtonClass: 'ExitConfirmButton',
          cancelButtonClass: 'ExitCancelButton',
          customClass: 'ExitCustomClass'
        })
        .then(() => {
          $axios({
            url: "/goods/delete",
            method: "post",
            data: {
              goodsId,
            },
          }).then((res) => {
            if (res.data.code === 2000) {
              elMessage({
                message: res.data.message,
                type: "success",
              });
              selectedPoints.value = [];
              page.pager.pageIndex = 1
              getTableData();
            } else {
              elMessage({
                message: res.data.message,
                type: "warning",
              });
            }
          });
        });
    }
  }
}

// 上架/下架
const upOrDownSaleBefore = (status, flag, ids) => {
  if (flag === 0 && selectedPoints.value.length < 1) {
    elMessage.warning("请选择");
  } else {
    let errorMsg = ""
    // 下架
    if (status === 0) {
      const idStocksMap = new Map();
      ids.forEach(id => {
        idStocksMap.set(id, { id: id });
      });

      let errorMsg = ""
      page.list.data.forEach(ele => {
        if (errorMsg) return
        if (idStocksMap.has(ele.productId)) {
          if (ele.status !== 'selling') return errorMsg = "仅出售中商品可下架"
        }
      });
      if (errorMsg) return elMessage.error(errorMsg)

    } else { // 上架
      const idStocksMap = new Map();
      ids.forEach(id => {
        idStocksMap.set(id, { id: id, stock: 0 });
      });

      let errorMsg = ""
      page.list.data.forEach(ele => {
        if (idStocksMap.has(ele.productId)) {
          if (ele.status !== 'down') return errorMsg = "仅已下架商品可上架"
          const stockItem = idStocksMap.get(ele.productId);
          stockItem.stock += ele.diffStock * 1;
        }
      });
      if (errorMsg) return elMessage.error(errorMsg)

      const idStocks = Array.from(idStocksMap.values());
      const noStock = idStocks.filter(ele => ele.stock === 0)
      if (noStock.length > 0) return elMessage.error("商品库存不足，不支持上架")
    }
    upOrDownSale(status, ids)
  }
};

function upOrDownSale(status, ids) {
  let tipMsg = status === 0 ? "确认下架？" : "确认上架？";
  let goodsId = ids;
  elMessageBox
    .confirm(tipMsg, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: 'ExitConfirmButton',
      cancelButtonClass: 'ExitCancelButton',
      customClass: 'ExitCustomClass'
    })
    .then(() => {
      $axios({
        url: "/goods/upOrDown",
        method: "post",
        data: {
          goodsId,
          status,
        },
      }).then((res) => {
        if (res.data.code === 2000) {
          elMessage({
            message: res.data.message,
            type: "success",
          });
          selectedPoints.value = [];
          getTableData();
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    });

}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

// 打开图片预览
function openImageViewer(row) {
  page.imageViewer.urlList = row.previewPic;
  page.imageViewer.show = true;
}

function timeChange(val) {
  if (val) {
    page.search.data.startTime = page.search.data.allTime[0];
    page.search.data.endTime = page.search.data.allTime[1];
  } else {
    page.search.data.startTime = "";
    page.search.data.endTime = "";
  }
}

const priceChange = (flag) => {
  return;
  if (page.search.data.lowPrice && page.search.data.highPrice) {
    if (
      parseFloat(page.search.data.lowPrice) >=
      parseFloat(page.search.data.highPrice)
    ) {
      elMessage.warning("最高价格需大于最低价格");
      if (flag === 1) {
        page.search.data.lowPrice = "";
      } else {
        page.search.data.highPrice = "";
      }
    }
  }
};

// onMounted
onMounted(() => {
  // 获得下拉
  getCategoryList();
  // 获得表格数据
  getTableData();
});
</script>

<style lang="scss">
.ExitConfirmButton {
  background: #068324 !important;
  border-color: #068324 !important;

  &:hover {
    opacity: 0.8;
  }
}

.ExitCancelButton {
  // background: #068324 !important;
  // border-color: #068324 !important;

  &:hover {
    background-color: rgba(133, 224, 154, 0.2) !important;
    color: #38864a !important;
    border-color: #38864a !important;
  }
}

.ExitCustomClass {
  .el-message-box__headerbtn:hover {
    .el-message-box__close {
      color: #068324;
    }
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-table th.el-table__cell) {
  color: #303133 !important;
}

:deep(.el-image) {
  overflow: visible;
}

.curp {
  cursor: pointer;
  color: #0B8DF1;
}

.circleIcon {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  margin-right: 5px;
}

.tableTags {
  padding: 6px;
  border-radius: 2px;
  color: #fff;
  font-size: 14px;
  line-height: 10px;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.caozuoBtn {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 5px 20px;
  line-height: normal;
}
</style>
