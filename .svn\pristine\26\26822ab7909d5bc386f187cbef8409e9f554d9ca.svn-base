import { reactive } from "vue";
import Clipboard from "clipboard"
import { ElMessage } from "element-plus";

export function useCopy() {

    /**
     * copy
     * 
     * @returns {void}
     */
    const copy = (copyData) => {
        let clipboard = new Clipboard(".copy-button", {
            text: () => copyData
        });

        clipboard.on("success", () => {
            ElMessage.success("复制成功");
            clipboard.destroy();
        });
        clipboard.on("error", () => {
            ElMessage.error("复制失败");
            clipboard.destroy();
        });
    }

    return {
        copy
    }
}