<template>
  <ndb-page-list>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="权限名称">
          <nd-input v-model.trim="page.search.rolesNameLike" placeholder="请输入权限名称" clearable :value-on-clear="''" />
        </nd-search-more-item>
        <nd-search-more-item title="权限描述">
          <nd-input v-model.trim="page.search.describesLike" placeholder="请输入权限描述" clearable :value-on-clear="''" />
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="handleSearch"> 查询 </nd-button>
          <nd-button @click="handleReset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #button>
      <nd-button @click="openAddDialog" type="primary" icon="plus" authKey="">新增</nd-button>
      <nd-button @click="mulDelete" icon="delete" authKey="">删除</nd-button>
    </template>
    <template #table>
      <nd-table style="height: 100%" :data="page.list.data" @selection-change="handleSelectionChange">
        <el-table-column type="selection" align="center" width="55" :selectable="selectableFn" />
        <el-table-column type="index" label="序号" align="center" width="80" />
        <el-table-column prop="rolesName" label="权限名称" align="center" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="curp" @click="openDetailDialog(row)">{{ row.rolesName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="describes" label="权限描述" header-align="center" align="left" min-width="150"
          show-overflow-tooltip />
        <el-table-column prop="userNames" label="已授权用户" header-align="center" align="left" min-width="180"
          show-overflow-tooltip />
        <el-table-column prop="createUserName" label="创建人" align="center" min-width="120" show-overflow-tooltip />
        <el-table-column prop="updateTime" label="更新日期" align="center" width="150">
          <template #default="{ row }">
            {{ row.updateTime ? row.updateTime.split(" ")[0] : "" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <div class="caozuoBtn">
              <nd-button type="edit" @click="openEditDialog(scope.row)"
                :disabled="!scope.row.checkUpdate">编辑</nd-button>
              <nd-button type="delete" @click="delRow(scope.row.rolesId)"
                :disabled="!scope.row.checkUpdate">删除</nd-button>
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination :current-page="page.pager.pageIndex" :page-size="page.pager.pageSize" :total="page.pager.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </template>
  </ndb-page-list>

  <detail-dialog ref="detailDialogRef" @before-close="getDataList" />
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";

// 导入子组件
import detailDialog from "./components/detailDialog.vue";

// 导入vue
import { onMounted, reactive, ref } from "vue";
import axios from "axios";
import { ElMessage, ElMessageBox } from "element-plus";

const page = reactive({
  search: {
    rolesNameLike: "",
    describesLike: "",
  },
  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  },
});

onMounted(() => {
  getDataList()
})

function selectableFn(row) {
  return row.checkUpdate;
}

function getDataList() {
  let postData = {
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    rolesNameLike: page.search.rolesNameLike,
    describesLike: page.search.describesLike,
  }
  axios({
    url: "/roles/findPage",
    method: "get",
    data: postData,
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.records;
      page.pager.total = res.data.data.total;
    } else {
      ElMessage.error(res.data.message);
    }
  })
}

const detailDialogRef = ref(null);

// 新增
function openAddDialog() {
  detailDialogRef.value?.open('add');
}
// 编辑
function openEditDialog(row) {
  detailDialogRef.value?.open('edit', row.rolesId);
}
// 详情
function openDetailDialog(row) {
  detailDialogRef.value?.open('detail', row.rolesId);
}

// 选中框
const selectedPoints = ref([]);
function handleSelectionChange(val) {
  selectedPoints.value = val.map((item) => item.rolesId);
}

function delRow(rolesId) {
  ElMessageBox
    .confirm(`删除后，已授权该角色权限的用户同步取消权限，是否确定删除？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: 'ExitConfirmButton',
      cancelButtonClass: 'ExitCancelButton',
      customClass: 'ExitCustomClass'
    })
    .then(() => {
      axios({
        url: "/roles/delete?id=" + rolesId,
        method: "post",
        serverName: "nd-base2"
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success("删除成功！");
          page.pager.pageIndex = 1
          getDataList();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
}

// 批量删除
function mulDelete() {
  if (selectedPoints.value.length < 1) return ElMessage.warning("请先勾选用户记录！");
  ElMessageBox
    .confirm(`删除后，已授权该角色权限的用户同步取消权限，确定将选中的${selectedPoints.value.length}条记录删除?`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: 'ExitConfirmButton',
      cancelButtonClass: 'ExitCancelButton'
    })
    .then(() => {
      axios({
        url: "/roles/deleteAll",
        method: "post",
        data: { idList: selectedPoints.value },
        serverName: "nd-base2"
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success("删除成功！");
          page.pager.pageIndex = 1
          getDataList();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
}

function handleSearch() {
  page.pager.pageIndex = 1
  getDataList();
}
function handleReset() {
  page.search.rolesNameLike = "";
  page.search.describesLike = "";
  page.pager.pageIndex = 1
  getDataList();
}

function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1
  getDataList();
}

function handleCurrentChange(params) {
  page.pager.pageIndex = params
  getDataList();
}
</script>

<style lang="scss" scoped>
:deep(.el-table th.el-table__cell) {
  color: #303133 !important;
}

:deep(.caozuoBtn) {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 5px 20px;
  line-height: normal;

  .is-disabled {
    color: #aaaaaa !important;
  }
}

.curp {
  cursor: pointer;
  color: #0B8DF1;
}
</style>
<style lang="scss">
.ExitConfirmButton {
  background: #068324 !important;
  border-color: #068324 !important;

  &:hover {
    opacity: 0.8;
  }
}

.ExitCancelButton {
  // background: #068324 !important;
  // border-color: #068324 !important;

  &:hover {
    background-color: rgba(133, 224, 154, 0.2) !important;
    color: #38864a !important;
    border-color: #38864a !important;
  }
}

.ExitCustomClass {
  .el-message-box__headerbtn:hover {
    .el-message-box__close {
      color: #068324;
    }
  }
}
</style>