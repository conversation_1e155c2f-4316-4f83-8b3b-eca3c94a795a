import { reactive } from "vue";

export function useAudo() {

    /**
     * 点击播放
     * 
     * @param {object} item
     * @param {array} list
     * @returns {void}
     */
    const handlePlay = (item, list) => {
        list?.forEach(audo => {
            audo.audio.currentTime = 0;
            audo.audio.pause();
        })
        item?.audio?.play();
    }

    /**
     * 点击暂停
     * 
     * @param {object} item
     * @returns {void}
     */
    const handlePause = (item) => {
        item.audio.pause();
        item.audio.currentTime = 0;
    }

    /**
     * 点击语音
     * 
     * @param {object} item
     * @param {array} list
     * @returns {void}
     */
    const handleVoice = (item, list) => {
        console.log(item);
        if (item.play) {
            item.play = false;
            handlePause(item);
        } else {
            item.play = true;
            handlePlay(item, list);
        }
    }

    return {
        handlePlay,
        handlePause,
        handleVoice
    }

}