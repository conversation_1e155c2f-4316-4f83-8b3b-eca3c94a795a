<template>
  <nd-dialog ref="replyRef" :title="page.title" width="40vw">
    <el-form
      ref="replyFormRef"
      :model="page.form"
      label-width="80px"
      :rules="rules"
      style="
        padding: 12px;
        background-color: #fff;
        margin: 12px;
        border-radius: 5px;
        border: 1px solid #EAEAEA;
      "
    >
      <el-form-item label="回复内容" prop="hfnr" required>
        <el-input
          v-model="page.form.hfnr"
          type="textarea"
          maxlength="300"
          placeholder="请输入回复内容"
          :rows="6"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <nd-button
        @click="replySave"
        :loading="isSubmitting"
        :disabled="isSubmitting"
      >
        确定
      </nd-button>
      <nd-button type="primary" @click="close">
        取消
      </nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";

import { ref, reactive, inject } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");

const replyRef = ref(null);
const replyFormRef = ref(null);
const emit = defineEmits(["before-close"]);

const page = reactive({
  title: "",
  opinionId: "",
  form: {
    hfnr: "",
  },
});
const rules = reactive({
  hfnr: [{ required: true, message: "请输入回复内容", trigger: "blur" }],
});

// 防重复
const isSubmitting = ref(false);

//打开
const open = (val) => {
  page.title = "回复";
  page.opinionId = val.opinionId;
  page.hfnr = "";
  replyRef.value.open();
};

//回复
const replySave = async () => {
  try {
    // 验证表单
    await replyFormRef.value.validate((valid) => {
      if (!valid) {
        throw new Error("表单验证失败");
      }
    });
    isSubmitting.value = true;
    let params = {
      opinionId: page.opinionId,
      hfnr: page.form.hfnr,
    };

    // 发送请求
    const res = await $axios({
      url: "/kfzx/saveHf",
      method: "post",
      serverName: "nd-base2",
      data: params,
    });
    if (res.data.code == 2000) {
      ElMessage.success(res.data.message);
      close();
      emit("before-close");
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    isSubmitting.value = false;
  }
};

//关闭弹窗
const close = () => {
  replyRef.value.close();
  page.form.hfnr = "";
  emit("before-close");
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  align-items: center;
}
</style>
