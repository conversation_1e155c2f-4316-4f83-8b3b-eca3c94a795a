<template>
  <nd-dialog
    ref="dialogRef"
    width="700px"
    title="新增协议"
    :before-close="close"
  >
    <div class="box">
      <div class="main">
        <el-form
          :model="formData"
          :rules="rules"
          label-width="120px"
          ref="agreementFormRef"
        >
          <el-form-item label="协议名称" prop="pactName">
            <nd-input
              v-model.trim="formData.pactName"
              placeholder="请输入协议名称"
              maxlength="30"
              clearable
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="开始日期" prop="beginDate">
            <el-date-picker
              v-model="formData.beginDate"
              type="date"
              placeholder="选择开始日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              type="date"
              placeholder="选择结束日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="附件" prop="fileList">
            <nd-upload
              :files="formData.fileList"
              fzgs="product1"
              mime="pdf,PDF"
              tip1=""
              tip2="支持批量上传pdf文件，单个文件最大不得超过15M"
            >
              <nd-button type="primary">上传附件</nd-button>
            </nd-upload>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <nd-button type="primary" @click="submit">提交</nd-button>
      <nd-button @click="close">取消</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";

import { ref, reactive, inject } from "vue";
import { ElMessage } from "element-plus";

// 定义axios
const $axios = inject("$axios");

// emit
const emits = defineEmits(["refresh"]);

// ref
const dialogRef = ref(null);
const agreementFormRef = ref(null);

const formData = reactive({
  pactName: "",
  beginDate: "",
  endDate: "",
  fileList: [],
  memberId: "",
  type: "",
  pactId: "",
});

//打开
const open = (memberId, type, pactId) => {
  resetForm();
  console.log(memberId, type, pactId, "0313123");
  formData.memberId = memberId;
  formData.type = type;
  if (type == "edit") {
    formData.pactId = pactId;
    getDetail();
  }
  dialogRef.value.open();
};

//校验
const rules = reactive({
  pactName: [{ required: true, message: "协议名称不能为空", trigger: "blur" }],
  beginDate: [
    { required: true, message: "开始日期不能为空", trigger: "change" },
  ],
  endDate: [
    { required: true, message: "结束日期不能为空", trigger: "change" },
    {
      validator: (_, value, callback) => {
        if (!formData.beginDate || !value) return callback();

        const start = new Date(formData.beginDate);
        const end = new Date(value);

        if (end <= start) {
          callback(new Error("结束日期必须大于开始日期"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  fileList: [
    { required: true, message: "附件不能为空", trigger: ["change", "blur"] },
  ],
});
//提交
const submit = () => {
  agreementFormRef.value.validate((valid) => {
    if (valid) {
      const data = {
        memberId: formData.memberId,
        pactName: formData.pactName,
        beginDate: formData.beginDate,
        endDate: formData.endDate,
        fileList: formData.fileList,
      };
      if (formData.type === "edit") {
        data.pactId = formData.pactId;
      }
      const url =
        formData.type === "add" ? "/merchant/savePact" : "/merchant/updatePact";

      $axios({
        url,
        method: "POST",
        serverName: "nd-base2",
        data,
      })
        .then((res) => {
          if (res.data.code !== 2000) {
            ElMessage.error(res.data.message);
          } else {
            ElMessage.success(res.data.message);
            close();
            emits("refresh");
          }
        })
        .catch(() => {});
    }
  });
};

//详情
const getDetail = () => {
  $axios({
    url: "/merchant/getPactById",
    method: "GET",
    serverName: "nd-base2",
    params: {
      pactId: formData.pactId,
    },
  }).then((res) => {
    if (res.data.code !== 2000) {
      ElMessage.error(res.data.message);
    }
    formData.pactName = res.data.data.pactName;
    formData.beginDate = res.data.data.beginDate;
    formData.endDate = res.data.data.endDate;
    formData.fileList = res.data.data.fileList;
  });
};
//重置
const resetForm = () => {
  formData.pactName = "";
  formData.beginDate = "";
  formData.endDate = "";
  formData.fileList = [];
};

//关闭
const close = () => {
  dialogRef.value.close();
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
/* 可添加样式 */

.box {
  width: 100%;
  height: 100%;
  padding: 12px;

  .main {
    background: #ffffff;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    padding: 12px;
  }
}
</style>
