import { reactive, watch } from "vue";

export class FormData {
    /** @type {string} */
    pactName;
    /** @type {string} */
    beginDate;
    /** @type {string} */
    endDate;
    /** @type {Array} */
    fileList
}

export function useForm() {
    const formData = reactive({
        pactName: "",
        beginDate: "",
        endDate: "",
        fileList: [],
        startDisabledDate: (time) => {
            if (formData.endDate) {

                // console.log(time, time.getTime() > new Date(formData.endDate).getTime());
                // const timeYear = time.getFullYear();
                // const timeMonth = time.getMonth() + 1;
                // const timeDate = time.getDate();

                // console.log(new Date(timeYear, timeMonth, timeDate));

                // console.log(new Date(timeYear, timeMonth, timeDate).getTime() == new Date(formData.beginDate).getTime());

                return time.getTime() > new Date(formData.endDate).getTime();
            }
            return false;

        },
        endDisabledDate: (time) => {
            if (formData.beginDate) {

                // if (time.getTime() == new Date(formData.beginDate).getTime()) {
                //     console.log(11);

                //     return true
                // }

                // console.log(time.getFullYear());
                // console.log(time.getMonth() + 1);
                // console.log(time.getDate());

                // const timeYear = time.getFullYear();
                // const timeMonth = time.getMonth() + 1;
                // const timeDate = time.getDate();

                // console.log(new Date(timeYear, timeMonth, timeDate));

                // console.log(new Date(2025, 7, 11).getTime() === new Date(formData.beginDate).getTime());

                // console.log(new Date(timeYear, timeMonth, timeDate).getTime() == new Date(formData.beginDate).getTime());


                return time.getTime() < new Date(formData.beginDate).getTime();
            }
            return false;

        }
    })

    /**
     * 自定义附件校验规则
     * 
     * @param {*} rule 
     * @param {*} value 
     * @param {*} callback 
     */
    const validateAttachment = (rule, value, callback) => {
        if (!value) {
            callback(new Error("附件不能为空"));
        } else if (Array.isArray(value) && value.length === 0) {
            callback(new Error("附件不能为空"));
        } else {
            callback();
        }
    };

    const rules = reactive({
        pactName: [
            { required: true, message: "协议名称不能为空", trigger: "blur" },
        ],
        beginDate: [
            { required: true, message: "开始日期不能为空", trigger: "change" },
        ],
        endDate: [
            { required: true, message: "结束日期不能为空", trigger: "change" },
            {
                validator: (rule, value, callback) => {
                    if (new Date(formData.endDate).getTime() <= new Date(formData.beginDate).getTime()) {
                        callback(new Error("结束日期必须大于开始日期"));
                    } else {
                        callback();
                    }
                }, trigger: "change"
            }
        ],
        // 补全 attachment 校验规则
        fileList: [
            { required: true, validator: validateAttachment, trigger: "change" },
        ],
    })

    /**
     * 清除数据方法
     * 
     * @returns {void}
     */
    const clear = () => {
        formData.pactName = "";
        formData.beginDate = "";
        formData.endDate = "";
        formData.fileList = [];
    }

    return {
        formData,
        rules,
        clear
    }
}