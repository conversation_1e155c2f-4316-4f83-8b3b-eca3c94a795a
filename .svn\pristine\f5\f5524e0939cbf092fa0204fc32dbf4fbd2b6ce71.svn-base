<template>
  <div class="nd-checkbox-box" :style="{ width: width }">
    <el-checkbox v-bind="$attrs">
      <slot></slot>
    </el-checkbox>
  </div>
</template>
<script setup>
const props = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px"
  }
})
</script>
<style lang="scss" scoped>
.nd-checkbox-box {
  :deep(.el-checkbox__label) {
    padding-left: 6px;
    font-size: 14px;
  }

  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #555555;
  }
}
</style>
