// 动态加载css文件
export function addCSS(url, isCache = false) {
    let element = document.createElement("link");
    element.setAttribute("rel", "stylesheet");
    element.setAttribute("type", "text/css");
    if (isCache) {
        element.setAttribute("href", url + "?t=" + new Date().getTime());
    } else {
        element.setAttribute("href", url);
    }
    document.head.appendChild(element);
}

// 动态加载js文件
export function addJS(url, isCache = false) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.type = 'text/javascript'
        if (isCache) {
            script.src = script.src + "?t=" + new Date().getTime();
        } else {
            script.src = script.src
        }
        document.body.appendChild(script)
        script.onload = () => {
            resolve(true)
        }
    })
}

// 动态去除js文件
export function removeJS(filename, filetype) {
    var targetelement = (filetype == "js") ? "script" : (filetype == "css") ? "link" : "none"
    var targetattr = (filetype == "js") ? "src" : (filetype == "css") ? "href" : "none"
    var allsuspects = document.getElementsByTagName(targetelement)
    for (var i = allsuspects.length; i >= 0; i--) {
        if (allsuspects[i] && allsuspects[i].getAttribute(targetattr) != null && allsuspects[i].getAttribute(targetattr).indexOf(filename) != -1)
            allsuspects[i].parentNode.removeChild(allsuspects[i])
    }
}