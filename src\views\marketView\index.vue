<template>
  <div class="main-box">
    <div class="title-box">
      <div class="list-icon"></div>
      <div>江苏省{{ productName }}批发均价走势图</div>
      <div class="tips-box">近7天</div>
    </div>
    <div class="underline-box"></div>
    <div class="charts-box box">
      <div class="legend-box">
        <img class="image-box" src="@/assets/images/legendIcon.png" alt="图例">
        <div>{{ productName }}</div>
      </div>
      <div class="unit-box">元/斤</div>
      <div v-if="loading" class="loading">加载中...</div>
      <div ref="priceChart" class="chart"></div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, inject, reactive, nextTick } from 'vue'
import * as echarts from 'echarts'

const priceChart = ref(null)
let priceChartInstance = null
const loading = ref(true)
const $axios = inject('$axios')
const productId = ref(1) // 默认值
const productName = ref('') // 产品名称

// 图表数据
const chartData = reactive({
  categories: [],
  series: [],
  data: [],
  maxPrice: 0
})

// 从URL参数中获取数据
const getUrlParams = () => {
  // 用于存储最终解析的参数
  const result = {
    productId: '',
    productName: '',
    token: ''
  };

  // 1. 解析 search（? 后面的参数）
  const searchParams = new URLSearchParams(window.location.search);
  // 从 search 中提取参数，有值就赋值，没值保持初始空字符串
  result.productId = searchParams.get('productId') || '';
  result.productName = searchParams.get('productName') || '';
  result.token = searchParams.get('token') || '';

  // 2. 解析 hash（# 后面的内容），先去除开头的 #
  const hash = window.location.hash.slice(1);
  // 如果有 hash 值，进一步处理
  if (hash) {
    // 分离出 hash 中可能存在的参数部分（即 ? 后面的内容）
    const hashParamStr = hash.split('?')[1] || '';
    const hashParams = new URLSearchParams(hashParamStr);

    // 用 hash 中的参数覆盖 search 中获取的值（如果有），可根据需求调整优先级
    result.productId = hashParams.get('productId') || result.productId;
    result.productName = hashParams.get('productName') || result.productName;
    result.token = hashParams.get('token') || result.token;
  }

  return result;
};

// 获取价格数据 - 按照原逻辑实现
const fetchPriceData = async () => {
  try {
    loading.value = true

    // 清空数据
    chartData.categories = []
    chartData.series = []
    chartData.data = []
    chartData.maxPrice = 0

    const response = await $axios.get('/shxqdj/getWeekPrice?productId=' + productId.value,
      {
        serverName: "baseUrl2"
      }
    )

    console.log('API响应数据:', response)

    if (response && response.code == 2000) {
      const data = response.data
      let maxPrice = 0

      // 按照原逻辑处理数据
      data.forEach(item => {
        chartData.categories.push(item.rq) // 日期
        chartData.data.push(item.jg) // 价格
        if (item.jg > maxPrice) {
          maxPrice = item.jg
        }
      })

      chartData.maxPrice = maxPrice
      chartData.series = [
        {
          name: productName.value,
          data: chartData.data,
          textColor: "#078325",
        }
      ]

      console.log('处理后的数据:', chartData)
      initPriceChart()
    } else {
      console.error('API返回错误:', response?.message || '未知错误')
      // 使用默认数据
      initPriceChart()
    }
  } catch (error) {
    console.error('获取价格数据失败:', error)
    // 出错时使用默认数据
    initPriceChart()
  }
}

// 初始化价格走势图 - 按照原uCharts样式实现
const initPriceChart = () => {
  priceChartInstance = echarts.init(priceChart.value)

  // 使用实际数据或默认数据
  let xAxisData = chartData.categories.length > 0 ? chartData.categories : ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  let seriesData = chartData.data.length > 0 ? chartData.data : [12, 15, 18, 16, 20, 22, 19]
  let maxPrice = chartData.maxPrice > 0 ? chartData.maxPrice : Math.max(...seriesData)

  const option = {
    backgroundColor: '#fff',
    grid: {
      left: 0,
      right: 18,
      top: 40,
      bottom: 30,
      containLabel: true,
      show: true,
      borderWidth: 0
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#068324',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function (params) {
        const data = params[0]
        return `${data.name}<br/>${productName.value}: ${data.value} 元/斤`
      }
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      // boundaryGap: false, // 让数据点贴近坐标轴边界
                    axisLine: {
        show: true,
        onZero: false,
        lineStyle: {
          color: '#EFEFEF',
          width: 1
        }
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        length: 8,
        inside: true,
        lineStyle: {
          color: '#EFEFEF',
          width: 1
        }
      },
      axisLabel: {
        color: '#98A4B2',
        fontSize: 12,
        margin: 14,
        interval: 0,      // 强制显示所有标签
        // hideOverlap: false, // 如有此属性可加上，防止自动隐藏
      },
      splitLine: {
        show: true,
        alignWithLabel: true, // 网格线与标签对齐
        lineStyle: {
          color: '#EFEFEF',
          type: 'dashed',
          width: 1
        }
      },
      splitArea: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: Math.ceil(maxPrice) + 5,
      axisLine: {
        show: false
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        length: 5,
        lineStyle: {
          color: '#EFEFEF',
          width: 1
        }
      },
      axisLabel: {
        color: '#98A4B2',
        fontSize: 12
      },
      splitLine: {
        show: true,
        alignWithLabel: false, // Y轴网格线不与标签对齐，而是与数据范围对齐
        lineStyle: {
          color: '#EFEFEF',
          type: 'dashed',
          width: 1
        }
      }
    },
    series: [{
      name: productName.value,
      data: seriesData,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      itemStyle: {
        color: '#fff',
        borderColor: '#068324',
        borderWidth: 1
      },
      lineStyle: {
        color: '#068324',
        width: 1
      },
      label: {
        show: true,
        position: 'top',
        distance: 4,
        color: '#078325',
        fontSize: 13,
        fontWeight: 'normal',
        formatter: function (params) {
          return params.value
        }
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(6, 131, 36, 0.22)'
          }, {
            offset: 1,
            color: 'rgba(6, 131, 39, 0)'
          }]
        }
      }
    }]
  }

  priceChartInstance.setOption(option)
  loading.value = false

}


// 窗口大小变化时重新调整图表
const handleResize = () => {
  priceChartInstance?.resize()
}

onMounted(() => {
  // 从URL参数获取数据
  const params = getUrlParams();
  console.log('从URL获取的参数:', params);

  // 处理token
  if (params.token) {
    localStorage.setItem('token', params.token);
    console.log('已保存token到localStorage');
  }

  // 处理productId
  if (params.productId) {
    productId.value = params.productId;
    console.log('更新productId:', productId.value);
  }

  // 处理productName
  if (params.productName) {
    productName.value = params.productName;
    console.log('更新productName:', productName.value);
  }

  // 获取数据
  fetchPriceData();

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  priceChartInstance?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 转换rpx到px: 1rpx = 0.5px  */
.main-box {
  width: 100%;
  /* height: 100vh; */
  background: #FFFFFF;
  padding: 12px;
  /* 24rpx */
  box-sizing: border-box;
  overflow: hidden;
}

.title-box {
  display: flex;
  align-items: center;
  font-family: PingFang SC, sans-serif;
  font-size: 16px;
  /* 32rpx */
  font-weight: 500;
  color: #333;
}

.list-icon {
  width: 3px;
  /* 6rpx */
  height: 15px;
  /* 30rpx */
  background: #068324;
  margin-right: 6px;
  /* 12rpx */
}

.tips-box {
  background: rgba(6, 131, 36, 0.1);
  border-radius: 3px;
  /* 6rpx */
  border: 1px solid rgba(6, 131, 36, 0.5);
  /* 2rpx */
  font-size: 10px;
  /* 20rpx */
  color: #068324;
  padding: 2px 8px;
  /* 4rpx 16rpx */
  margin-left: 6px;
  /* 12rpx */
}

.underline-box {
  width: 100%;
  height: 0.5px;
  /* 1rpx */
  background: #F1F1F1;
  margin-top: 10px;
  /* 20rpx */
}

.charts-box {
  width: 100%;
  height: 230px;
  /* 460rpx */
}

.box {
  position: relative;
}

.legend-box {
  position: absolute;
  top: 8px;
  /* 16rpx */
  right: 9px;
  /* 18rpx */
  display: flex;
  align-items: center;
  color: #666;
  font-family: PingFang SC, sans-serif;
  font-size: 12px;
  /* 24rpx */
  z-index: 10;
}

.image-box {
  width: 16px;
  /* 32rpx */
  height: 4px;
  /* 8rpx */
  margin-right: 3px;
  /* 6rpx */
  /* background: #068324; */
}

.unit-box {
  position: absolute;
  top: 8px;
  /* 16rpx */
  left: 0px;
  /* 10rpx */
  color: #666;
  font-family: PingFang SC, sans-serif;
  font-size: 12px;
  /* 24rpx */
  z-index: 10;
}

.chart {
  width: 100%;
  height: 100%;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 14px;
  z-index: 20;
}

</style>
