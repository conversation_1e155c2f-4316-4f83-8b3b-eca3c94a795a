import { reactive } from "vue";
import axios from "@/http/index";
import { ElMessage, ElMessageBox } from "element-plus";

/**
 * 
 * @returns 
 */
export function useArgeement() {
    const argeementData = reactive({
        list: [],
        loading: false
    })

    const statusMap = new Map([
        [0, "未开始"],
        [1, "正常"],
        [2, "到期"]
    ])

    /**
     * 获取协议列表
     * 
     * @returns {void}
     */
    const getArgeementList = ({ memberId = '' }) => {
        argeementData.loading = true;

        const params = {
            memberId: memberId ?? ""
        }

        axios({
            url: "/yhgl/yzhgl/getPactList",
            method: "GET",
            serverName: "nd-base2",
            params
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return;
            }

            r.data.data?.forEach(item => {
                item.fileList?.forEach(item2 => {
                    item2.sourcePath = window.ipConfig.fileUrl + item2.sourcePath;
                    item2.fullPath = item2.sourcePath;
                })
            })

            argeementData.list = r.data.data || [];
        }).finally(() => argeementData.loading = false)
    }

    /**
     * 删除协议按钮
     * 
     * @param {Object} param0
     * @param {String} param0.pactId
     * @param {Function} param0.callback
     * @returns {void}
     */
    const deleteArgeement = ({ pactId = '', callback = null }) => {
        ElMessageBox.confirm("确定删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(async () => {
            await deleteArgeementApi({ pactId });
            callback && callback();
        }).catch(() => { })
    }

    /**
     * 删除接口通信
     * 
     * @returns {void}
     */
    const deleteArgeementApi = async ({ pactId }) => {
        argeementData.loading = true;

        const data = {
            pactId: pactId ?? ""
        }

        return axios({
            url: "/yhgl/yzhgl/delPact",
            method: "POST",
            serverName: "nd-base2",
            data
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return;
            }
        }).finally(() => argeementData.loading = false)
    }

    /**
     * 附件查看
     * 
     * @returns {void}
     */
    const previewFile = ({ param = null, ref = null }) => {
        ref && ref.open(param);
    }

    return {
        argeementData,
        statusMap,
        getArgeementList,
        deleteArgeement,
        previewFile
    }
}