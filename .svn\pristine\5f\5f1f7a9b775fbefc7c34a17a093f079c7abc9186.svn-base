<template>
    <nd-dialog
        ref="dialogRef"
        :title="dialogData.title"
        width="700px"
        :before-close="close"
    >
        <div class="box">
            <div class="main">
                <el-form
                    :model="formData"
                    :rules="rules"
                    label-width="120px"
                    ref="agreementFormRef"
                >
                    <el-form-item label="协议名称" prop="pactName">
                        <nd-input
                            v-model.trim="formData.pactName"
                            placeholder="请输入协议名称"
                            maxlength="30"
                            clearable
                            style="width: 100%"
                        />
                    </el-form-item>
                    <el-form-item label="开始日期" prop="beginDate">
                        <el-date-picker
                            v-model="formData.beginDate"
                            type="date"
                            placeholder="选择开始日期"
                            style="width: 100%"
                            value-format="YYYY-MM-DD"
                            format="YYYY-MM-DD"
                        />
                    </el-form-item>
                    <el-form-item label="结束日期" prop="endDate">
                        <el-date-picker
                            v-model="formData.endDate"
                            type="date"
                            placeholder="选择结束日期"
                            style="width: 100%"
                            value-format="YYYY-MM-DD"
                            format="YYYY-MM-DD"
                        />
                    </el-form-item>
                    <el-form-item label="附件" prop="fileList">
                        <nd-upload
                            :files="formData.fileList"
                            fzgs="product1"
                            mime="pdf,PDF"
                            tip1=""
                            tip2="支持批量上传pdf文件，单个文件最大不得超过15M"
                        >
                            <nd-button type="primary">上传附件</nd-button>
                        </nd-upload>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <template #footer>
            <nd-button
                type="primary"
                @click="submit({ formRef: agreementFormRef })"
                >提交</nd-button
            >
            <nd-button @click="close">取消</nd-button>
        </template>
    </nd-dialog>
</template>

<script setup>
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";

import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";

// hooks
import { useDialog } from "../../hooks/addAgreement/useDialog";
import { useForm } from "../../hooks/addAgreement/useForm";

// emit
const emits = defineEmits(["refresh"]);

// ref
const dialogRef = ref(null);
const agreementFormRef = ref(null);

// useForm
const { formData, rules, clear } = useForm();

// useDialog
const { dialogData, open, close, submit } = useDialog({
    dialogRef: dialogRef,
    formData,
    clear,
    emits,
});

defineExpose({
    open,
    close,
});
</script>

<style lang="scss" scoped>
/* 可添加样式 */

.box {
    width: 100%;
    height: 100%;
    padding: 12px;

    .main {
        background: #ffffff;
        border: 1px solid #eaeaea;
        border-radius: 5px;
        padding: 12px;
    }
}
</style>
