import { reactive } from "vue";
import axios from "@/http/index";
import { ElMessage } from "element-plus";

export class DialogData {
    /** @type {string} */
    title;
}

export function useDialog({ dialogRef = null, dispatchRef = null }) {

    const dialogData = reactive({
        title: "需求单详情",
        detailData: {},
        loading: false,
        demandId: "",
        status: "",
    })

    /**
     * open
     * 
     * @returns {void}
     */
    const open = (row) => {
        dialogData.demandId = row.demandId ?? "";
        dialogData.status = row.status ?? "";
        getDetail();
        dialogRef && dialogRef.value.open();
    }

    /**
     * close
     * 
     * @returns {void}
     */
    const close = () => {
        // 关闭所有语音
        dialogData.detailData.sourceVos?.forEach(item => {
            item.audio.pause();
            item.audio.currentTime = 0;
        })
        console.log("close");
        dialogData.detailData = {};
        dialogRef && dialogRef.value.close();
    }

    /**
     * dispatch
     * 
     * @returns {void}
     */
    const dispatch = (demandId) => {
        console.log("dispatch");

        dispatchRef && dispatchRef.value.open(demandId);
    }

    /**
     * 获取详情数据
     * 
     * @returns {void}
     */
    const getDetail = () => {

        const params = { demandId: dialogData.demandId };

        dialogData.loading = true;

        axios({
            url: "/buy/demand/find",
            method: "GET",
            serverName: "nd-base2",
            params,
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return;
            }
            r.data.data['orderVoList'] = [r.data.data.orderVo];

            r.data.data?.sourceVos?.forEach(item => {
                console.log(item.sourcePath);
                item.sourcePath = window.ipConfig.fileUrl + "/" + item.sourcePath;
            })

            dialogData.detailData = r.data.data;

            let promiseList = [];

            r.data.data?.sourceVos?.forEach(item => {
                promiseList.push(new Promise((resolve, reject) => {
                    const audio = new Audio(item.sourcePath);
                    audio.addEventListener('loadedmetadata', () => {
                        console.log(`Audio duration: ${audio.duration} seconds`);
                        item["duration"] = Math.round(audio.duration);
                        resolve();
                    });
                    item["audio"] = audio;
                    item["play"] = false;
                }))
            })

            Promise.all(promiseList).then(() => {
                dialogData.detailData = r.data.data;
            }).catch((error) => {
                console.log(error, "!");

            });

        }).finally(() => dialogData.loading = false)
    }

    return {
        dialogData,
        open,
        close,
        dispatch
    }
}