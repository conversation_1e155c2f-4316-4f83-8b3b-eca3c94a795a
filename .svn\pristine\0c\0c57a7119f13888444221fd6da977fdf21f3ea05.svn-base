<template>
  <ndb-page-list>
    <template #button>
      <nd-button @click="openAddDialog" type="primary" icon="plus"
        >新建</nd-button
      >
      <nd-button @click="openImportDialog" icon="Download">导入</nd-button>
      <nd-button @click="batchDelete" icon="Delete">删除</nd-button>
      <nd-button @click="resetPassword" icon="Key">重置密码</nd-button>
    </template>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="账户名称">
          <nd-input
            v-model.trim="page.searchData.nickname"
            placeholder="请输入账户名称"
            clearable
            style="width: 100%"
          />
        </nd-search-more-item>
        <nd-search-more-item title="账号">
          <nd-input
            v-model.trim="page.searchData.account"
            placeholder="请输入账号"
            clearable
            style="width: 100%"
          />
        </nd-search-more-item>
        <nd-search-more-item title="姓名">
          <nd-input
            v-model.trim="page.searchData.realname"
            placeholder="请输入姓名"
            clearable
            style="width: 100%"
          />
        </nd-search-more-item>
        <nd-search-more-item title="联系电话">
          <nd-input
            v-model.trim="page.searchData.lxdh"
            placeholder="请输入联系电话"
            clearable
            style="width: 100%"
          />
        </nd-search-more-item>
        <nd-search-more-item title="状态">
          <nd-select v-model="page.searchData.status" clearable>
            <el-option label="全部" :value="2" />
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </nd-select>
        </nd-search-more-item>
        <!-- <nd-search-more-item title="身份证号">
          <nd-input
            v-model.trim="page.searchData.idCard"
            placeholder="请输入身份证号"
            clearable
            style="width: 100%"
          />
        </nd-search-more-item> -->
        <template #footer>
          <nd-button type="primary" @click="getTableData" authKey="">
            查询
          </nd-button>
          <nd-button @click="reset()"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <nd-table
        style="height: 100%"
        :data="page.tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          align="center"
          label="#"
          type="selection"
          width="52px"
          fixed="left"
        ></el-table-column>
        <el-table-column align="center" label="序号" width="60px" type="index">
        </el-table-column>
        <el-table-column
          align="center"
          label="账户名称"
          prop=""
          min-width="120"
          show-overflow-tooltip
        >
          <template v-slot="scope">
            <div
              @click="openDetailDialog(scope.row)"
              style="
                color: #409eff;
                cursor: pointer;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              "
            >
              {{ scope.row.nickname }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="账号"
          prop="account"
          min-width="100px"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          label="姓名"
          prop="realname"
          min-width="100px"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          align="center"
          label="联系电话"
          prop="lxdh"
          show-overflow-tooltip
          min-width="150"
        >
          <template v-slot="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <div>
                {{
                  scope.row.showPhone
                    ? scope.row.decryptedPhone
                    : scope.row.lxdh
                }}
              </div>
              <div style="margin-left: 10px" @click="clickPhone(scope.row)">
                <img
                  style="width: 14px; height: 14px; cursor: pointer"
                  :src="scope.row.showPhone ? openEyeImg : closeEyeImg"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="身份证号码"
          prop="idCard"
          show-overflow-tooltip
          min-width="200"
        >
          <template v-slot="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <div>
                {{
                  scope.row.showIdCard
                    ? scope.row.decryptedIdCard
                    : scope.row.idCard
                }}
              </div>
              <div style="margin-left: 10px" @click="clickIdCard(scope.row)">
                <img
                  style="width: 14px; height: 14px; cursor: pointer"
                  :src="scope.row.showIdCard ? openEyeImg : closeEyeImg"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="状态"
          prop="status"
          show-overflow-tooltip
          width="110"
        >
          <template v-slot="scope">
            <div class="type">
              <div
                class="icon"
                :class="{
                  normal: scope.row.status === 1,
                  disable: scope.row.status !== 1,
                }"
              ></div>
              <div class="status-tag" v-if="scope.row.status === 1">正常</div>
              <div class="status-tag" v-else>禁用</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="150px"
        >
          <template #default="scoped">
            <div style="display: flex; justify-content: space-around;">
              <nd-button type="edit" @click="openEditDialog(scoped.row)"
                >编辑</nd-button
              >
              <nd-button type="edit" @click="openAgree(scoped.row)"
                >上传协议</nd-button
              >
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <ndPagination
        :current-page="page.pager.pageIndex"
        :page-size="page.pager.pageSize"
        :total="page.pager.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </ndb-page-list>
  <ndb-import
    ref="importRef"
    titles="导入"
    projectId="1"
    modeType="shMember"
    @upbates="refreshData"
  />
  <addDialog ref="addDialogRef" @refreshData="refreshData" />
  <agreeMent ref="agreementRef" />
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import openEyeImg from "@/assets/images/openEye.png";
import closeEyeImg from "@/assets/images/closeEye.png";

// 导入子组件
import addDialog from "./components/addDialog.vue";
import agreeMent from "./components/agreeMent.vue";

// 导入vue
import {
  onMounted,
  reactive,
  ref,
  inject,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
// 定义$axios
const $axios = inject("$axios");
// ref
const addDialogRef = ref(null);
const agreementRef = ref(null);
const importRef = ref(null);


const page = reactive({
  pager: {
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  },
  searchData: {
    nickname: "",
    account: "",
    realname: "",
    lxdh: "",
    idCard: "",
    status: 2,
  },
  tableData: [],
});

onMounted(() => {
  getTableData();
});

//表格
const getTableData = () => {
  const params = {
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    nickname: page.searchData.nickname,
    account: page.searchData.account,
    realname: page.searchData.realname,
    lxdh: page.searchData.lxdh,
    status: page.searchData.status == 2 ? "" : page.searchData.status,
  };
  $axios({
    url: "/merchant/findPage",
    method: "get",
    serverName: "nd-base2",
    params,
  }).then((res) => {
    if (res.data.code !== 2000) {
      ElMessage.error(res.data.message);
      return;
    }
    page.tableData = res.data.data.records.map((row) => ({
      ...row,
      showPhone: false,
      decryptedPhone: "",
      showIdCard: false,
      decryptedIdCard: "",
    }));
    page.pager.total = res.data.data.total;
  });
};

// 切换显示状态
const clickPhone = async (row) => {
  if (!row.showPhone && !row.decryptedPhone) {
    row.decryptedPhone = (await decryptData(row.lxdhJm)) || "--";
  }
  row.showPhone = !row.showPhone;
};

const clickIdCard = async (row) => {
  if (!row.showIdCard && !row.decryptedIdCard) {
    row.decryptedIdCard = (await decryptData(row.idCardJm)) || "--";
  }
  row.showIdCard = !row.showIdCard;
};

//解密
const decryptData = async (content) => {
  const data = {
    content,
  };
  return $axios({
    url: "/common/getPlaintext",
    method: "POST",
    serverName: "nd-base2",
    data,
  }).then((res) => {
    if (res.data.code !== 2000) {
      ElMessage.error(res.data.message);
      return "";
    }
    return res.data.data;
  });
};
const selectRow = ref([]);
//选中
const handleSelectionChange = (val) => {
  selectRow.value = val;
};

//重置密码
const resetPassword = () => {
  if (!selectRow.value.length) {
    ElMessage.warning("请先勾选用户记录!");
    return;
  }
  ElMessageBox.confirm(
    `是否对这${selectRow.value.length}条数据进行密码重置？重置后不可恢复，请确认！`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: "ExitConfirmButton",
      cancelButtonClass: "ExitCancelButton",
      customClass: "ExitCustomClass",
    }
  )
    .then(() => {
      const data = {
        memberIds: selectRow.value.map((item) => item.memberId),
      };
      $axios({
        url: "/merchant/reset",
        method: "post",
        serverName: "nd-base2",
        data,
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success("重置成功！用户初始化密码为SYSH@8899");
          getTableData();
        }
      });
    })
    .catch(() => {});
};

//批量删除
const batchDelete = () => {
  if (!selectRow.value.length) {
    ElMessage.warning("请先勾选用户记录!");
    return;
  }
  ElMessageBox.confirm("用户删除后将无法再登录小程序？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    confirmButtonClass: "ExitConfirmButton",
    cancelButtonClass: "ExitCancelButton",
    customClass: "ExitCustomClass",
  })
    .then(() => {
      const data = {
        memberIds: selectRow.value.map((item) => item.memberId),
      };
      $axios({
        url: "/merchant/deleteMember",
        method: "post",
        serverName: "nd-base2",
        data,
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success("删除成功");
          getTableData();
        }
      });
    })
    .catch(() => {});
};

//新增
const openAddDialog = () => {
  addDialogRef.value.open("add");
};
//编辑
const openEditDialog = (row) => {
  addDialogRef.value.open("edit", row.memberId);
};
//详情
const openDetailDialog = (row) => {
  addDialogRef.value.open("detail", row.memberId);
};

//上传协议
const openAgree = (row) => {
  agreementRef.value.open(row.memberId,"add","");
};

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

//重置
const reset = () => {
  page.searchData.nickname = "";
  page.searchData.account = "";
  page.searchData.realname = "";
  page.searchData.lxdh = "";
  page.searchData.status = 2;
  getTableData();
};
// 刷新
const refreshData = () => {
  getTableData();
};

// 导入
function openImportDialog() {
  importRef.value.open();
}
</script>

<style lang="scss" scoped>
.eye-cont {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .eye-icon {
    display: flex;
    align-items: center;
    img {
      width: 14px;
      height: 14px;
      cursor: pointer;
    }
  }
}

.type {
  display: flex;
  align-items: center;
  justify-content: center;

  .icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
  }

  .normal {
    background-color: #00bf2e;
  }

  .disable {
    background-color: #ff0001;
  }
}
:deep(.nd-table-box .el-table .el-table__cell) {
  font-size: 14px;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<style lang="scss">
.ExitConfirmButton {
  background: #068324 !important;
  border-color: #068324 !important;

  &:hover {
    opacity: 0.8;
  }
}

.ExitCancelButton {
  // background: #068324 !important;
  // border-color: #068324 !important;

  &:hover {
    background-color: rgba(133, 224, 154, 0.2) !important;
    color: #38864a !important;
    border-color: #38864a !important;
  }
}
</style>
