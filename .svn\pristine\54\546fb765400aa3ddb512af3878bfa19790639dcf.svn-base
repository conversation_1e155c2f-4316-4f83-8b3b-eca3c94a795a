<template>
  <nd-dialog
    ref="shipmentDialogRef"
    width="85vw"
    height="38vh"
    :title="title"
    align-center
  >
    <div class="main">
      <div class="shipment">
        <div class="goodstotal">
          共 {{ tableContent?.ddspsl }} 件商品，待发货 {{ dfhNum }} 件
        </div>
        <nd-table
          style="height: 100%"
          :data="tableData"
          @selection-change="handleSelectionChange"
          @select-all="handleSelectAll"
          ref="tableDataRef"
          row-key="detailId"
        >
          <el-table-column
            align="center"
            label="#"
            type="selection"
            width="52px"
            :selectable="checkSelectable"
          />

          <!-- <el-table-column align="center" label="商品信息" prop="productVo.name" show-overflow-tooltip min-width="100px" /> -->
          <el-table-column
            align="center"
            label="商品信息"
            prop="pic"
            min-width="200px"
          >
            <template #default="{ row }">
              <div class="item1">
                <div class="goodImg">
                  <img :src="row.productVo.pic" alt="" />
                </div>
                <div class="goodInfo">
                  <div class="title">
                    <!-- <div class="mallorder-card-center-details-tag">
                  <span v-for="(tag, index) in row.productVo.tagVos" :key="index" class="titleLv"
                    :style="`background-color: ${tag.color}`">
                    {{
                      tag.name }} </span>
                </div> -->
                    {{ row.productVo.name }}
                  </div>
                  <div class="gg">{{ row.productVo.spData }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="数量"
            prop="nums"
            show-overflow-tooltip
            width="160"
          />
          <el-table-column
            align="center"
            label="状态"
            prop="status"
            show-overflow-tooltip
            min-width="100px"
          >
            <template #default="{ row }">
              <span v-if="row.sffh == 1">已发货</span>
              <span v-if="row.sffh == 0">未发货</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="发货方式"
            prop="status"
            show-overflow-tooltip
            min-width="100px"
          >
            <template #default="{ row }">
              <span v-if="row.wlVo.type == 1 && row.sffh == 1"
                >第三方物流发货</span
              >
              <span v-else-if="row.wlVo.type == 2 && row.sffh == 1"
                >商家自行发货</span
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="发货日期" prop="status">
            <template #default="{ row }">
              <span v-if="row.sffh == 1">{{ row.wlVo.fhTime }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="物流公司/司机姓名"
            prop="status"
          >
            <template #default="{ row }">
              <span v-if="row.sffh == 1"
                >{{ row.wlVo.wlgs }}{{ row.wlVo.sjName }}</span
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="物流单号/司机联系方式"
            prop="status"
          >
            <template #default="{ row }">
              <span v-if="row.sffh == 1"
                >{{ row.wlVo.wlbh }}{{ row.wlVo.sjLxfs }}</span
              >
              <span v-else>-</span></template
            >
          </el-table-column>
        </nd-table>
        <!-- <el-form
          ref="addFormRef"
          :model="form"
          :rules="rules"
          class="add-box"
          label-position="left"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="物流公司" label-width="80px" prop="wlgs">
                <nd-select
                  filterable
                  v-model="form.wlgs"
                  placeholder=" "
                  width="90%"
                >
                  <el-option
                    v-for="item in wlgsList"
                    :key="item.dictId"
                    :label="item.dictName"
                    :value="item.dictKey"
                    >{{ item.dictName }}</el-option
                  >
                </nd-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运单号" label-width="80px" prop="wlbh">
                <nd-input
                  type2="number5"
                  v-model="form.wlbh"
                  placeholder=" "
                  width="90%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" label-width="80px" prop="remake">
                <nd-input
                  maxlength="500"
                  type="textarea"
                  v-model="form.remake"
                  placeholder=" "
                  width="95.5%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form> -->
      </div>
    </div>

    <template #footer>
      <!-- <nd-button type="" icon="Pointer" @click="submit('1')"
        >确&nbsp;定</nd-button
      >
      <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button> -->
      <nd-button type="" @click="next">下&nbsp;一&nbsp;步</nd-button>
    </template>
  </nd-dialog>
  <shipmentNext-dialog
    @before-close="close"
    ref="shipmentDialogRefNext"
  ></shipmentNext-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
import shipmentNextDialog from "./shipmentNext.vue";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义属性
const props = defineProps({});
// 定义时间格式化函数
const formatDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const shipmentDialogRef = ref(null);
const shipmentDialogRefNext = ref(null);
// 定义对话框的标题
let title = ref("");
// 定义表单数据
const addFormRef = ref(null);
const tableDataRef = ref(null);
let tableContent = ref(null);
let tableData = ref([]);
let orderId = ref("");
let form = ref({
  wlId: 0,
  orderId: "",
  detailIds: [],
  wlbh: "",
  wlgs: "",
  remark: "",
  status: "",
  fhTime: formatDateTime(),
  czr: localStorage.getItem("syUserName"),
});
// 多选
let detailIds = ref([]);
const handleSelectionChange = (val) => {
  console.log("🚀 ~ handleSelectionChange ~ val:", val);
  form.value.detailIds = val.map((item) => item.detailId);
  detailIds.value = val.map((item) => item.detailId);
};
// 定义校验规则 ==========================================================

// 自定义校验规则 --- 正则 --- 功能菜单名称最长不得超过10个汉字
const validateFunctionName = (rule, value, callback) => {
  const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
  let chineseCharacters = form.value.menuName.match(chineseReg); // 匹配输入字符串中的汉字
  if (chineseCharacters && chineseCharacters.length > 10) {
    return callback(new Error("error"));
  } else {
    return callback();
  }
};

// 表单校验规则
const rules = reactive({
  wlgs: [{ required: true, message: "请选择物流公司" }],
  wlbh: [{ required: true, message: "请输入物流单号" }],
  functionName: [
    { required: true, message: "请输入功能点名称", trigger: "blur" },
    // { min: 1, max: 50, message: "最长不得超过50个汉字", trigger: "blur" },
    {
      validator: validateFunctionName,
      trigger: "blur",
      message: "最长不得超过50个汉字",
    },
  ],
});

// 打开弹窗 ==============================================================
// 打开弹窗
function open(params, status) {
  if (status === "shipment") {
    title.value = "订单发货";
    orderId.value = params.orderId;
    form.value.orderId = params.orderId;
    getDetail();
    // getWlInfo();
    getDictfindList();
    shipmentDialogRef.value.open();
    detailIds.value = [];
  }
}
// 获得详情
let dfhNum = ref(""); //待发货数量
function getDetail() {
  $axios({
    url: "/order/manage/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: orderId.value,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      tableContent.value = res.data.data;
      tableData.value = tableContent.value.detailVos;
      let arr = res.data.data.detailVos.filter((el) => el.sffh == 0);
      console.log("🚀 ~ arr ~ arr:", arr);
      dfhNum.value = arr.length;
      tableData.value.forEach((item) => {
        if (item.sffh == "1") {
          nextTick(() => {
            // 置灰不用勾选
            // tableDataRef.value.toggleRowSelection(item, true);
          });
        }
      });
      tableData.value = tableData.value.sort((a, b) => {
        // 检查a和b是否为空对象
        const isEmptyA =
          Object.keys(a.wlVo).length === 0 && a.wlVo.constructor === Object;
        const isEmptyB =
          Object.keys(b.wlVo).length === 0 && b.wlVo.constructor === Object;

        // 如果a是空对象，b不是，则a排在b之后
        if (isEmptyA && !isEmptyB) return 1;
        // 如果b是空对象，a不是，则a排在b之前
        if (!isEmptyA && isEmptyB) return -1;
        // 如果两者都不是空对象，可以进一步根据其他属性排序
        if (!isEmptyA && !isEmptyB) {
          // 比较时间
          if (a.wlVo.fhTime && b.wlVo.fhTime) {
            return a.wlVo.fhTime === b.wlVo.fhTime
              ? 0
              : a.wlVo.fhTime > b.wlVo.fhTime
              ? -1
              : 1;
          } else {
            return 0;
          }
        }
        // 如果两者都是空对象，保持原有顺序或根据其他逻辑处理
        return 0;
      });
    }
  });
}
//查看物流信息
function getWlInfo() {
  $axios({
    url: "/order/manage/wl/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: orderId.value,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
    }
  });
}
// 获取物流公司字典
let wlgsList = ref([]);
const getDictfindList = () => {
  $axios({
    url: "/dict/findAll",
    serverName: "nd-base2",
    method: "get",
    data: {
      dictMark: "WLGS",
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      wlgsList.value = res.data.data;
      console.log("🚀 ~ getDictfindList ~ res.data:", res.data.data);
    }
  });
};

// 清空表单
const clear = () => {
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
};

// 关闭弹窗 ==============================================================
const close = () => {
  emit("before-close");
  shipmentDialogRef.value.close();
  clear();
};

// 保存提交 ==============================================================
const submit = async () => {
  if (form.value.detailIds.length === 0) {
    ElMessage({
      message: "请选择商品",
      type: "warning",
    });
    return;
  }
  // 发货
  await addFormRef.value.validate((valid, fields) => {
    // delete form.value.testDutyName

    if (valid) {
      let params = form.value;
      $axios({
        url: "/order/manage/send/goods",
        method: "post",
        serverName: "nd-base2",
        data: params,
      }).then((res) => {
        if (res.data.code === 2000) {
          console.log("编辑成功");
          // 轻提示
          ElMessage({
            message: "保存成功",
            type: "success",
          });
          // 关闭弹框
          emit("before-close");
          close();
        } else {
          ElMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    } else {
      console.log("校验失败!", fields);
    }
  });
};
// 下一步----------
const next = () => {
  if (detailIds.value && detailIds.value.length) {
    let params = {
      orderId: orderId.value,
      detailIds: detailIds.value,
    };
    shipmentDialogRefNext.value.open(params, "shipment");
  } else {
    ElMessage({
      message: "请选择商品",
      type: "warning",
    });
  }
};
// 禁用选择
const checkSelectable = (row) => {
  return row.sffh != "1";
};
// 阻止取消不可选行
const handleSelectAll = (selection) => {
  if (selection.length === 0) {
    // 取消全选操作
    tableData.value.forEach((item) => {
      if (item.sffh == "1") {
        nextTick(() => {
          // 置灰不用勾选
          // tableDataRef.value.toggleRowSelection(item, true);
        });
      }
    });
  }
};
// 暴露方法给父组件 =======================================================
defineExpose({
  open,
  clear,
});
</script>

<style lang="scss" scoped>
.main {
  padding: 12px;
  width: 100%;
  background-color: #f7f7f7;
  .shipment {
    padding: 12px;
    background-color: #fff;
    border-radius: 5px;
    border: 1px solid #ebeef5;
  }
}
.goodstotal {
  margin-bottom: 20px;
}

.add-box {
  padding: 0px;
  margin-top: 32px;

  :deep(.el-textarea__inner) {
    height: 80px;
  }
}

.item1 {
  flex: 4;
  padding: 8px 12px;
  display: flex;

  .goodImg {
    width: 90px;
    height: 90px;
    border-radius: 4px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .goodInfo {
    // width: 100%;
    text-align: left;
    margin-left: 10px;

    .title {
      font-size: 14px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 10px;
      display: flex;
    }
  }
}

.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 0 2px;
  background: #20b203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;
}
:deep(.dialog-content-box) {
  padding: 12px !important;
}
</style>
