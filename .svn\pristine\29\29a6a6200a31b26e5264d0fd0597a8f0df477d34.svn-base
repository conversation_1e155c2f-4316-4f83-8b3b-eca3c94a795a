<template>
  <div class="system-name-flipping-box">
    <!-- <div class="system-name1">苏渔后台管理系统</div>
    <div class="system-name2">苏渔后台管理系统</div> -->
    <div class="system-name3">苏渔后台管理系统</div>
    <div class="system-name4">苏渔后台管理系统</div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.system-name-flipping-box {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .system-name1 {
    width: 695px;
    height: 90px;
    background-image: url("@/assets/images/loginView/logo.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: 0px 0px;
    animation: system-name1-out 4s linear 1s infinite;
  }

  .system-name3 {
    width: 695px;
    height: 90px;
    color: #068324;
    font-size: 60px;
    animation: system-name1-out 4s linear 1s infinite;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }

  @keyframes system-name1-out {
    0% {
      transform: translateY(0%);
    }
    10% {
      transform: translateY(-120%);
    }
    100% {
      transform: translateY(-120%);
    }
  }

  .system-name2 {
    width: 695px;
    height: 90px;
    background-image: url("@/assets/images/loginView/logo.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: 0px 0px;
    animation: system-name2-in 4s linear 1s infinite;
  }

  .system-name4 {
    width: 695px;
    height: 90px;
    color: #068324;
    font-size: 60px;
    animation: system-name2-in 4s linear 1s infinite;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }

  @keyframes system-name2-in {
    0% {
      transform: translateY(0px);
    }
    10% {
      transform: translateY(-90px);
    }
    100% {
      transform: translateY(-90px);
    }
  }
}
</style>
