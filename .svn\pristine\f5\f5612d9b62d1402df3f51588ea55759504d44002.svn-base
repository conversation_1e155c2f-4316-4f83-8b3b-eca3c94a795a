<template>
  <ndb-page-list>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="账户名称">
          <nd-input
            v-model.trim="page.search.data.nickname"
            placeholder="请输入账户名称"
          />
        </nd-search-more-item>
        <nd-search-more-item title="账号">
          <nd-input
            v-model.trim="page.search.data.account"
            placeholder="请输入账号"
          />
        </nd-search-more-item>
        <nd-search-more-item title="反馈内容">
          <nd-input
            v-model.trim="page.search.data.fknr"
            placeholder="请输入反馈内容"
          />
        </nd-search-more-item>
        <nd-search-more-item title="提出时间">
          <nd-date-picker
            v-model="page.search.data.cycleArr"
            range-separator="至"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择"
            width="100%"
          ></nd-date-picker>
        </nd-search-more-item>
        <nd-search-more-item title="状态">
          <nd-select
            v-model="page.search.data.sfhf"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in page.search.dict.sfhfList"
              :label="item.label"
              :key="item.value"
              :value="item.value"
            />
          </nd-select>
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="getTableData" authKey="">
            查询
          </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <nd-table style="height: 100%" :data="page.list.data">
        <el-table-column
          align="center"
          label="序号"
          type="index"
          width="60px"
        ></el-table-column>
        <el-table-column
          align="center"
          label="账户名称"
          prop="nickname"
        ></el-table-column>
        <el-table-column align="center" label="账号" prop="account">
        </el-table-column>
        <el-table-column
          align="left"
          label="反馈内容"
          prop="fknr"
          show-overflow-tooltip
          min-width="200px"
        ></el-table-column>
        <el-table-column
          align="center"
          label="图片"
          prop="fileLength"
          width="150px"
        >
          <template #default="scoped">
            <div @click="showImg(scoped.row.fileList)">
              <el-icon style="color: #0b8df1; margin-right: 5px"
                ><Link
              /></el-icon>
              <span style="color: #0b8df1; cursor: pointer">{{
                scoped.row.fileLength
              }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="提出时间"
          prop="tcsj"
        ></el-table-column>
        <el-table-column align="center" label="状态" prop="sfhf" width="120px">
          <template #default="scoped">
            <div class="sty-box">
              <div class="round" v-if="scoped.row.sfhf===0"></div>
              <div class="round1" v-else></div>
              <div>{{ scoped.row.sfhf === 0 ? "待回复" : "已回复" }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="120px"
        >
          <template #default="scoped">
            <div style="display: flex; gap: 8px; justify-content: center">
              <nd-button type="document" @click="openDetail(scoped.row)"
                >详情</nd-button
              >
              <nd-button type="document2" @click="openReply(scoped.row)"
                >回复</nd-button
              >
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination
        :current-page="page.pager.pageIndex"
        :page-size="page.pager.pageSize"
        :total="page.pager.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </ndb-page-list>
  <el-image-viewer
    v-if="showPreview"
    :url-list="srcList"
    show-progress
    :initial-index="0"
    @close="showPreview = false"
  />
  <detail ref="dialogRef" @before-close="getTableData" />
  <reply ref="replyRef" @before-close="getTableData" />
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";

// 导入子组件
import detail from "./components/detail.vue";
import reply from "./components/reply.vue";

// 导入vue
import { onMounted, reactive, ref, inject } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 定义axios
const $axios = inject("$axios");

// 定义ref
const dialogRef = ref(null);
const replyRef = ref(null);

// 定义page
const page = reactive({
  search: {
    data: {
      account: "",
      nickname: "",
      fknr: "",
      tcsjBegin: "",
      tcsjEnd: "",
      sfhf: "2",
      cycleArr: ["", ""],
    },
    dict: {
      //状态
      sfhfList: [
        { label: "全部", value: "2" },
        { label: "待回复", value: "0" },
        { label: "已回复", value: "1" },
      ],
    },
  },
  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  },
});

// onMounted
onMounted(() => {
  imgFile.value =
    window.ipConfig.base2 +
    "/common/download?token=" +
    localStorage.getItem("syToken") +
    "&path=";
  // 获得表格数据
  getTableData();
});

// 获得表格数据
function getTableData() {
  let params = {
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    account: page.search.data.account,
    nickname: page.search.data.nickname,
    fknr: page.search.data.fknr,
    tcsjBegin: page.search.data.cycleArr[0],
    tcsjEnd: page.search.data.cycleArr[1],
    sfhf: page.search.data.sfhf == "2" ? "" : page.search.data.sfhf,
  };
  $axios({
    url: "/kfzx/findPage",
    method: "get",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.records;
      page.list.data.map((item) => {
        item.fileLength = item.fileList.length;
      });
      page.pager.total = res.data.data.total; //总页数
      page.pager.pageIndex = res.data.data.current; //当前页
      page.pager.pageSize = res.data.data.size; //每页记录数
    } else {
      ElMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

const showPreview = ref(false);
const srcList = ref([]);
const imgFile = ref("");
//预览图片
const showImg = (val) => {
  if (val.length === 0) {
    ElMessage({
      message: "图片为空",
      type: "warning",
    });
    return;
  }
  showPreview.value = true;
  let pics = val.map((item) => item.sourcePath);
  srcList.value = pics.map((pic) => imgFile.value + pic);
};

// 重置
function reset() {
  page.search.data.account = "";
  page.search.data.nickname = "";
  page.search.data.fknr = "";
  page.search.data.sfhf = "2";
  page.search.data.cycleArr = ["", ""];
  getTableData();
}

// 打开详情dialog
function openDetail(params) {
  dialogRef.value.open(params);
}

// 打开回复dialog
function openReply(params) {
  replyRef.value.open(params);
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}
</script>

<style lang="scss" scoped>
.workhour {
  color: #444444;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;

  .workhour-item-line {
    width: 4px;
    height: 15px;
    border-radius: 3px;
    background-color: red;
    margin-right: 12px;
  }

  .workhour-item {
    margin-right: 23px;

    .red {
      color: red;
    }

    .blue {
      color: #10a3e7;
    }
  }
}
.sty-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  .round {
    background: #ff8a00;
    width: 8px;
    height: 8px;
    border-radius: 100px;
  }
  .round1 {
    background: #00bf2e;
    width: 8px;
    height: 8px;
    border-radius: 100px;
  }
}
:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<style lang="css"> 
.el-popper {
  max-width: 500px ;
}
 </style>


