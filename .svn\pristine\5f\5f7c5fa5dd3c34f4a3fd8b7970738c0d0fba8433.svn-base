<template>
  <div class="nd-switch-box" :style="{ width: width }">
    <el-switch v-bind="$attrs" ref="switchRef" popper-class="nd-switch-popper" :disabled="_disabled">
      <slot></slot>
    </el-switch>
  </div>
</template>
<script setup>
import { ref, watch, onMounted } from "vue";

// 定义属性
const props = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px",
  },
  // 只读
  disabled: {
    type: Boolean,
    default: false,
  },
  // 权限key
  authKey: {
    type: String,
    default: "",
  },
});

// 系统级别disabled
var _disabled = ref(false);

// 监视disabled
watch(
  () => props.disabled,
  (newValue, oldValue) => {
    _disabled.value = newValue;
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  if (localStorage.getItem("syAuths")) {
    var auth = localStorage.getItem("syAuths").split(",");
    auth.map((item) => {
      if (props.authKey === item) {
        _disabled.value = true;
      }
    });
  }
});
</script>
<style lang="scss" scoped></style>
