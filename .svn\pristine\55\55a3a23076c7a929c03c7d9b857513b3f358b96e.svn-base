export default {
  data() {
    return {
      hoverIndex: -1,
      hoverOption: -1,
    };
  },

  watch: {
    hoverIndex(val) {
      if (typeof val === 'number' && val > -1) {
        this.hoverOption = this.options[val] || {};
      }
      this.options.forEach(option => {
        option.hover = this.hoverOption === option;
      });
    }
  },

  methods: {
    navigateOptions(direction) {
      if (direction === 'next') {
        this.hoverIndex++;
        if (this.hoverIndex === this.options.length) {
          this.hoverIndex = this.options.length - 1; // 不循环
          // this.hoverIndex = 0; // 循环
        }
      } else if (direction === 'prev') {
        this.hoverIndex--;
        if (this.hoverIndex < 0) {
          this.hoverIndex = 0;
          // this.hoverIndex = this.options.length - 1;
        }
      }
      this.$nextTick(() => this.scrollToOption(this.$refs.option[this.hoverIndex]));
      // this.$nextTick(() => this.scrollToOption(this.$refs.option.$children[this.hoverIndex].$children[0].$el));
    }
  }
};