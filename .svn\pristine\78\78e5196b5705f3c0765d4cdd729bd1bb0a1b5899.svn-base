<template>
  <div class="nd-autocomplete-box" :style="{ width: width }">
    <el-autocomplete v-bind="$attrs" v-if="!hasCode"></el-autocomplete>
    <el-autocomplete v-bind="$attrs" v-else popper-class="my-autocomplete">
      <template #default="{ item }">
        <div class="xmName">{{ item.xmName }}</div>
        <span class="xmId">{{ item.xmId }}</span>
      </template>
    </el-autocomplete>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, reactive, nextTick } from 'vue';

var prop = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px"
  },
  hasCode: {
    type: Boolean,
    default: false
  }
})
</script>
<style lang='scss' scoped>
.nd-autocomplete-box {
  height: auto;

  :deep(.el-autocomplete) {
    width: 100%;
  }

  // .el-input {
  //   width: 100%;
  //   height: 30px;
  //   line-height: 30px;
  //   border-radius: 4px;
  //   border: 1px solid #dcdfe6;
  //   padding-left: 11px;
  //   padding-right: 10px;
  //   padding-top: 0px;
  //   padding-bottom: 0px;
  //   font-size: 14px;
  //   background-color: #ffffff;
  // }

  // :deep(.el-input__wrapper) {
  //   box-shadow: none;
  //   padding: 0;
  // }

  // :deep(.el-input__inner) {
  //   color: #606266;
  // }

  :deep(.el-input .el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
}

.my-autocomplete li {
  // padding: 7px 0;
}

.my-autocomplete li .xmName {
  text-overflow: ellipsis;
  line-height: 1;
  overflow: hidden;
  padding-top: 7px;
}

.my-autocomplete li .xmId {
  font-size: 12px;
  color: #b4b4b4;
}
</style>
