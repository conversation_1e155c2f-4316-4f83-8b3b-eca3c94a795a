<template>
  <nd-dialog
    ref="dialogRef"
    width="1282px"
    height="726px"
    :title="page.title"
    align-center
  >
    <div class="main">
      <!-- ---订单状态--- -->
      <div class="statusMain">
        <div class="left">
          <div class="code">
            <span class="text1"
              >订单编号：
              <span v-if="page.goodsInfo.sfcd == 0">{{
                page.goodsInfo.orderNo
              }}</span>
              <span v-if="page.goodsInfo.sfcd == 1">
                <span
                  v-if="
                    page.goodsInfo.status == 0 ||
                    (page.goodsInfo.status == 51 &&
                      page.goodsInfo.cancelType == 1) ||
                    (page.goodsInfo.status == 51 &&
                      page.goodsInfo.cancelType == 4)
                  "
                  >{{ page.goodsInfo.orderNo }}</span
                >
                <span v-else>
                  {{ page.goodsInfo.orderNoChild }}
                </span>
              </span>
            </span>
            <span class="text2 copy" @click="copy(page.goodsInfo)">
              <img src="../../../assets/images/order/copy.svg" alt=""
            /></span>
          </div>
          <div class="status">
            <span
              style="color: #267fff"
              v-if="page.goodsInfo.status == 0 && page.goodsInfo.sfsqsh == 0"
              >待付款</span
            >
            <span
              style="color: #ff8d02"
              v-else-if="page.goodsInfo.status == 1 && page.goodsInfo.sfqx == 0"
              >待发货</span
            >
            <span
              style="color: #999999"
              v-else-if="page.goodsInfo.status == 1 && page.goodsInfo.sfqx == 1"
              >申请取消</span
            >
            <span
              style="color: #068324"
              v-else-if="
                page.goodsInfo.status == 2 && page.goodsInfo.sfsqsh == 0
              "
              >已发货</span
            >
            <span
              style="color: #999999"
              v-else-if="page.goodsInfo.status == 2 && page.goodsInfo.sfqx == 1"
              >申请取消</span
            >
            <span
              style="color: #068324"
              v-else-if="
                page.goodsInfo.status == 3 &&
                page.goodsInfo.sfsqsh == 0 &&
                invoiceStatusShow == ''
              "
              >已完成</span
            >
            <span style="color: #068324" v-else-if="invoiceStatusShow == 'wkp'"
              >未开票</span
            >
            <span style="color: #068324" v-else-if="invoiceStatusShow == 'ykp'"
              >已开票</span
            >
            <span
              style="color: #999999"
              v-else-if="
                (page.goodsInfo.sfsqsh = 1 && page.goodsInfo.shStatue == 1)
              "
              >已处理</span
            >
            <span
              style="color: #999999"
              v-else-if="
                (page.goodsInfo.sfsqsh = 1 && page.goodsInfo.shStatue == 2)
              "
              >售后完成</span
            >
            <span
              style="color: #999999"
              v-else-if="
                (page.goodsInfo.sfsqsh = 1 && page.goodsInfo.shStatue == 3)
              "
              >售后关闭</span
            >
            <span style="color: #999999" v-else-if="page.goodsInfo.status == 51"
              >已关闭</span
            >
            <span style="color: #999999" v-else-if="page.goodsInfo.status == 60"
              >已关闭</span
            >
          </div>
          <div
            class="dateline"
            v-if="page.goodsInfo.status == 0 && page.goodsInfo.sfsqsh == 0"
          >
            如买家未在
            <span class="min"> {{ getOrderTime(page.goodsInfo.xdTime) }}</span>
            内付款，订单将自动关闭
          </div>
          <div
            class="dateline"
            v-if="page.goodsInfo.status == 1 && page.goodsInfo.sfqx == 0"
          >
            买家已付款,请尽快安排发货
          </div>
          <div
            class="dateline"
            v-if="page.goodsInfo.status == 1 && page.goodsInfo.sfqx == 1"
          >
            买家已付款，申请取消订单并退款
          </div>
          <div
            class="dateline"
            v-if="page.goodsInfo.status == 2 && page.goodsInfo.sfqx == 1"
          >
            买家已付款，申请取消订单并退款
          </div>
          <div
            class="dateline"
            v-if="page.goodsInfo.status == 2 && page.goodsInfo.sfqx == 0"
          >
            商品已发货，等待买家确认收货
          </div>
          <div
            class="dateline"
            v-if="page.goodsInfo.status == 3 && page.goodsInfo.sfsqsh == 0"
          >
            买家已确认收货，交易已完成
          </div>
          <div class="dateline" v-if="page.goodsInfo.status == 51">
            {{ page.goodsInfo.statusRemark }}
          </div>
          <div class="dateline" v-if="page.goodsInfo.status == 60">
            {{ page.goodsInfo.statusRemark }}
          </div>
          <div class="btn">
            <div
              class="button"
              v-if="
                page.goodsInfo.status == 1 &&
                page.goodsInfo.sfqx == 0 &&
                page.goodsInfo.sfkcz == 1 &&
                page.goodsInfo.userType == 2
              "
              @click="openShipment(page.goodsInfo)"
            >
              发货
            </div>
            <div
              class="button"
              v-if="
                page.goodsInfo.status == 1 &&
                page.goodsInfo.sfqx == 1 &&
                page.goodsInfo.sfkcz == 1 &&
                page.goodsInfo.userType == 2
              "
              @click="openOrderProcessingRef(page.goodsInfo)"
            >
              去处理
            </div>
            <div
              class="button"
              v-if="
                page.goodsInfo.status == 2 &&
                page.goodsInfo.sfqx == 1 &&
                page.goodsInfo.sfkcz == 1 &&
                page.goodsInfo.userType == 2
              "
              @click="openOrderProcessingRef(page.goodsInfo)"
            >
              去处理
            </div>
            <!-- <div
              class="button"
              v-if="page.goodsInfo.status == 3 && page.goodsInfo.sfsqsh == 0"
              @click="openOrderProcessingRef(page.goodsInfo)"
            >
              联系买家
            </div> -->
          </div>
        </div>
        <div class="line"></div>
        <div class="right">
          <div class="iconFlow">
            <div class="flow">
              <div class="icon iconAct">
                <img src="../../../assets/images/order/tjdd.svg" alt="" />
              </div>
              <div class="text">
                <div>提交订单</div>
                <div>{{ page.goodsInfo.xdTime }}</div>
              </div>
            </div>
            <div class="arrLine">
              <img src="@/assets/images/order/arrLine.svg" alt="" />
            </div>
            <template
              v-if="page.goodsInfo.status != 51 && page.goodsInfo.status != 60"
            >
              <div class="flow">
                <div
                  class="icon"
                  v-if="
                    page.goodsInfo.status == 0 ||
                    (page.goodsInfo.status == 2 && page.goodsInfo.sfsqsh == 1)
                  "
                >
                  <img src="../../../assets/images/order/mjfk.svg" alt="" />
                </div>
                <div class="icon iconAct" v-else>
                  <img src="../../../assets/images/order/mjfk2.svg" alt="" />
                </div>
                <div class="text">
                  <div>买家付款</div>
                  <div>{{ page.goodsInfo.fkTime }}</div>
                </div>
              </div>
              <div class="arrLine">
                <img src="@/assets/images/order/arrLine.svg" alt="" />
              </div>
              <div class="flow">
                <div
                  class="icon"
                  v-if="
                    page.goodsInfo.status == 0 || page.goodsInfo.status == 1
                  "
                >
                  <img src="../../../assets/images/order/sjfh.svg" alt="" />
                </div>
                <div class="icon iconAct" v-else>
                  <img src="../../../assets/images/order/sjfh2.svg" alt="" />
                </div>
                <div class="text">
                  <div>商家发货</div>
                  <div>{{ page.goodsInfo.fhTime }}</div>
                </div>
              </div>
              <div class="arrLine">
                <img src="@/assets/images/order/arrLine.svg" alt="" />
              </div>
              <div class="flow">
                <div
                  class="icon"
                  v-if="
                    page.goodsInfo.status == 0 ||
                    page.goodsInfo.status == 1 ||
                    page.goodsInfo.status == 2
                  "
                >
                  <img src="../../../assets/images/order/qrsh.svg" alt="" />
                </div>
                <div class="icon iconAct" v-else>
                  <img src="../../../assets/images/order/qrsh2.svg" alt="" />
                </div>
                <div class="text">
                  <div>买家确认收货</div>
                  <div>{{ page.goodsInfo.shTime }}</div>
                </div>
              </div>
            </template>
            <template
              v-if="page.goodsInfo.status == 51 || page.goodsInfo.status == 60"
            >
              <div class="flow">
                <div class="icon iconAct">
                  <img src="../../../assets/images/order/gbdd.svg" alt="" />
                </div>

                <div class="text">
                  <div>关闭订单</div>
                  <div></div>
                </div>
              </div>
              <div class="arrLine"></div>
              <div class="flow"></div>
              <div class="arrLine"></div>
              <div class="flow"></div>
            </template>
          </div>
          <!-- <div class="textarea">
            <span class="label">卖家备注</span>
            <el-input v-model="textarea" class="textareaContent" type="textarea" placeholder="请输入内容" />
          </div> -->
        </div>
      </div>
      <!-- ---订单信息--- -->
      <div class="orderMain">
        <div class="title">
          <div class="br"></div>
          <div class="text">订单信息</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>收货人信息</span>
              <span
                class="edit copy"
                @click="showAddress(page.goodsInfo.dzglVo)"
              >
                <img src="../../../assets/images/edit.png" alt=""
              /></span>
            </div>
            <div class="item2">
              <span>配送信息</span>
            </div>
            <div class="item3">
              <span>订单信息</span>
            </div>
          </div>
          <div class="body">
            <div class="item1">
              <div class="infoItem">
                收货人姓名：{{ page.goodsInfo.dzglVo?.name }}
              </div>
              <div class="infoItem">
                联系方式：{{ page.goodsInfo.dzglVo?.phone }}
              </div>
              <div
                class="infoItem"
                :title="
                  page.goodsInfo.dzglVo?.province +
                  page.goodsInfo.dzglVo?.city +
                  page.goodsInfo.dzglVo?.district +
                  page.goodsInfo.dzglVo?.detailAddress
                "
              >
                收货地址：{{ page.goodsInfo.dzglVo?.province
                }}{{ page.goodsInfo.dzglVo?.city
                }}{{ page.goodsInfo.dzglVo?.district
                }}{{ page.goodsInfo.dzglVo?.detailAddress }}
              </div>
            </div>
            <div class="item2">
              <div class="infoItem">配送方式：快递配送</div>
            </div>
            <div class="item3">
              <div class="infoItem">
                订单编号：
                <span v-if="page.goodsInfo.sfcd == 0">{{
                  page.goodsInfo.orderNo
                }}</span>
                <span v-if="page.goodsInfo.sfcd == 1">
                  <span
                    v-if="
                      page.goodsInfo.status == 0 ||
                      (page.goodsInfo.status == 51 &&
                        page.goodsInfo.cancelType == 1) ||
                      (page.goodsInfo.status == 51 &&
                        page.goodsInfo.cancelType == 4)
                    "
                    >{{ page.goodsInfo.orderNo }}</span
                  >
                  <span v-else>
                    {{ page.goodsInfo.orderNoChild }}
                  </span>
                </span>

                <span class="text2 copy" @click="copy(page.goodsInfo)">
                  <img src="../../../assets/images/order/copy.svg" alt=""
                /></span>
              </div>
              <div class="infoItem">
                下单账号：{{ page.goodsInfo.dzglVo?.account }}
              </div>
              <div class="infoItem">
                订单状态：
                <span
                  style="color: #267fff"
                  v-if="
                    page.goodsInfo.status == 0 && page.goodsInfo.sfsqsh == 0
                  "
                  >待付款</span
                >
                <span
                  style="color: #ff8d02"
                  v-else-if="
                    page.goodsInfo.status == 1 && page.goodsInfo.sfqx == 0
                  "
                  >待发货</span
                >
                <span
                  style="color: #999999"
                  v-else-if="
                    page.goodsInfo.status == 1 && page.goodsInfo.sfqx == 1
                  "
                  >申请取消</span
                >
                <span
                  style="color: #068324"
                  v-else-if="
                    page.goodsInfo.status == 2 && page.goodsInfo.sfsqsh == 0
                  "
                  >已发货</span
                >
                <span
                  style="color: #068324"
                  v-else-if="
                    page.goodsInfo.status == 3 && page.goodsInfo.sfsqsh == 0
                  "
                  >已完成</span
                >
                <span
                  style="color: #999999"
                  v-else-if="
                    (page.goodsInfo.sfsqsh = 1 && page.goodsInfo.shStatue == 1)
                  "
                  >已处理</span
                >
                <span
                  style="color: #999999"
                  v-else-if="
                    (page.goodsInfo.sfsqsh = 1 && page.goodsInfo.shStatue == 2)
                  "
                  >售后完成</span
                >
                <span
                  style="color: #999999"
                  v-else-if="
                    (page.goodsInfo.sfsqsh = 1 && page.goodsInfo.shStatue == 3)
                  "
                  >售后关闭</span
                >
                <span
                  style="color: #999999"
                  v-else-if="page.goodsInfo.status == 51"
                  >已关闭</span
                >
                <span
                  style="color: #999999"
                  v-else-if="page.goodsInfo.status == 60"
                  >已关闭</span
                >
              </div>
              <div
                v-if="page.goodsInfo.status == 51 || page.goodsInfo.sfqx != 1"
                class="infoItem"
              >
                下单时间：{{ page.goodsInfo.xdTime }}
              </div>
              <div
                v-if="page.goodsInfo.status != 0 && page.goodsInfo.sfqx != 1"
                class="infoItem"
              >
                付款时间：{{ page.goodsInfo.fkTime }}
              </div>
              <div
                v-if="
                  page.goodsInfo.status != 0 &&
                  page.goodsInfo.status != 1 &&
                  page.goodsInfo.sfqx != 1
                "
                class="infoItem"
              >
                发货时间：{{ page.goodsInfo.fhTime }}
              </div>
              <div
                v-if="page.goodsInfo.status != 0 && page.goodsInfo.sfqx != 1"
                class="infoItem"
              >
                付款金额：{{ page.goodsInfo.amount }}
              </div>
              <div
                v-if="page.goodsInfo.status != 0 && page.goodsInfo.sfqx != 1"
                class="infoItem"
              >
                支付方式：
                <span v-if="page.goodsInfo.type == 1">微信支付</span>
                <span v-else-if="page.goodsInfo.type == 2"
                  >额度支付-邮储银行定向支付</span
                >
                <span v-else-if="page.goodsInfo.type == 3">银联支付</span>
              </div>
              <div
                v-if="page.goodsInfo.status == 1 && page.goodsInfo.sfqx == 1"
                class="infoItem"
              >
                申请取消原因：{{ page.goodsInfo.qxRemark }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 退款原因 -->
      <div class="tukuanMain" v-if="page.goodsInfo.sfsqsh == 1">
        <div class="title">
          <div class="br"></div>
          <div class="text">退款原因</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>申请原因</span>
            </div>
            <div class="item2">
              <span>申请描述</span>
            </div>
          </div>
          <div
            class="body"
            v-for="(reason, index) in page.goodsInfo?.shhVo?.shhDetailVos"
            :key="index"
          >
            <div class="item1">
              {{ reason.remark }}
            </div>
            <div class="item2">
              {{ reason.remark }}
            </div>
          </div>
        </div>
      </div>
      <!-- 物流信息 -->
      <div
        class="wuliuMain"
        v-if="
          (page.goodsInfo.status == 2 && page.goodsInfo.sfsqsh == 0) ||
          page.goodsInfo.status == 3 ||
          wlDateInfo?.length > 0
        "
      >
        <div class="title">
          <div class="br"></div>
          <div class="text">物流信息</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>序号</span>
            </div>
            <div class="item2">
              <span>物流单号/司机联系方式</span>
            </div>
            <div class="item3">
              <span>商品信息</span>
            </div>
            <div class="item4">
              <span>数量</span>
            </div>
            <div class="item5">
              <span>状态</span>
            </div>
            <div class="item6">
              <span>发货方式</span>
            </div>
            <div class="item7">
              <span>发货日期</span>
            </div>
            <div class="item8">
              <span>物流公司/司机姓名</span>
            </div>
            <!-- <div class="item9">
              <span>操作人</span>
            </div> -->
            <div class="item10">
              <span>操作</span>
            </div>
          </div>
          <div class="body" v-for="(wl, index) in wlDateInfo" :key="index">
            <div class="item1">
              <span>{{ index + 1 }}</span>
            </div>
            <div class="item2">
              <el-tooltip
                :content="
                  tooltipShowFn(
                    'wl',
                    wl.wlVo.type,
                    wl.wlVo.wlbh,
                    wl.wlVo.sjLxfs
                  )
                "
                placement="top-start"
              >
                <span
                  @click="viewShipmentRequest"
                  class="mfAbq"
                  v-if="wl.sffh == 1 && wl.wlVo.type == 1"
                >
                  {{ wl.wlVo.wlbh }}{{ wl.wlVo.sjLxfs }}
                </span>
                <span v-if="wl.sffh == 1 && wl.wlVo.type == 2">
                  {{ wl.wlVo.wlbh }}{{ wl.wlVo.sjLxfs }}
                </span>
                <span v-else>-</span>
              </el-tooltip>
            </div>
            <div class="item3">
              <div>{{ wl.productVo.name }}</div>
              <div>{{ wl.productVo.spData }}</div>
            </div>
            <div class="item4">
              <span>{{ wl.nums }}</span>
            </div>
            <div class="item5">
              <!-- <span v-if="wlDateInfo.status == 0">待收件</span>
              <span v-if="wlDateInfo.status == 1">已收件</span>
              <span v-if="wlDateInfo.status == 2">已发出</span>
              <span v-if="wlDateInfo.status == 3">已签收</span>
              <span v-if="wlDateInfo.status == 10">丢件</span>
              <span v-if="wlDateInfo.status == 20">无法联系收货人</span> -->
              <span v-if="wl.sffh == 1">已发货</span>
              <span v-if="wl.sffh == 0">未发货</span>
            </div>
            <div class="item6">
              <span v-if="wl.wlVo.type == 1 && wl.sffh == 1"
                >第三方物流发货</span
              >
              <span v-else-if="wl.wlVo.type == 2 && wl.sffh == 1"
                >商家自行发货</span
              >
              <span v-else>-</span>
            </div>
            <div class="item7">
              <span v-if="wl.sffh == 1">{{ wl.wlVo.fhTime }}</span>
              <span v-else>-</span>
            </div>
            <div class="item8">
              <span v-if="wl.sffh == 1"
                >{{ wl.wlVo.wlgs }}{{ wl.wlVo.sjName }}</span
              >
              <span v-else>-</span>
            </div>
            <!-- <div class="item9">
              <span>{{ wlDateInfo.czr }}</span>
            </div> -->
            <div class="item10">
              <span
                v-if="wl.wlVo.type == 1 && wl.sffh == 1"
                class="edit"
                @click="viewShipmentRequest"
                >查看物流</span
              >
              <span v-else>-</span>
            </div>
          </div>
        </div>
      </div>
      <!-- ---商品信息--- -->
      <div class="goodMain">
        <div class="title">
          <div class="br"></div>
          <div class="text">商品信息</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>商品信息</span>
            </div>
            <div class="item2">
              <span>单价</span>
            </div>
            <div class="item3">
              <span>数量</span>
            </div>
            <div class="item4">
              <span>小计</span>
            </div>
            <div class="item5">
              <span>发货状态</span>
            </div>
          </div>
          <div
            class="body"
            v-for="(good, index) in page.goodsInfo.detailVos"
            :key="index"
          >
            <div class="item1">
              <div class="goodImg">
                <img :src="good.productVo.pic" alt="" />
              </div>
              <div class="goodInfo">
                <div class="title">
                  <!-- <div class="mallorder-card-center-details-tag">
                    <span v-for="(tag, index) in good.productVo.tagVos" :key="index" class="titleLv"
                      :style="`background-color: ${tag.color}`">
                      {{
                        tag.name }} </span>
                  </div> -->
                  {{ good.productVo.name }}
                </div>
                <div class="gg">{{ good.productVo.spData }}</div>
              </div>
            </div>
            <div class="item2">
              <div class="infoItem">¥{{ good.amount }}</div>
            </div>
            <div class="item3">
              <div class="infoItem">{{ good.nums }}</div>
            </div>
            <div class="item4">
              <div class="infoItem">¥{{ good.xiaoji }}</div>
            </div>
            <div class="item5">
              <div class="infoItem">
                <span style="color: #ff8d02" v-if="good.sffh == 0">未发货</span>
                <span style="color: #068324" v-else>已发货</span>
              </div>
            </div>
          </div>
        </div>
        <div class="priceAll">
          <div class="text1">订单总金额：</div>
          <div class="text2">￥{{ page.goodsInfo.amount }}</div>
        </div>
      </div>
      <!-- 开票信息 -->
      <div class="kpMain" v-if="invoiceStatusShow == 'ykp'">
        <div class="title">
          <div class="br"></div>
          <div class="text">开票信息</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>发票抬头</span>
            </div>
            <div class="item2">
              <span>发票类型</span>
            </div>
            <div class="item3">
              <span>订单金额</span>
            </div>
            <div class="item4">
              <span>开票金额</span>
            </div>
            <div class="item5">
              <span>操作</span>
            </div>
          </div>
          <div class="body">
            <div class="item1 gg">
              <el-tooltip :content="fpDetail?.ttmc" placement="top-start">
                <span>{{ fpDetail?.ttmc }}</span>
              </el-tooltip>
            </div>
            <div class="item2 gg">
              <span v-if="fpDetail?.fplx == 1">电子普通发票</span>
              <span v-if="fpDetail?.fplx == 2">专用发票</span>
            </div>
            <div class="item3 gg">
              <span>￥{{ fpDetail?.amount }}</span>
            </div>
            <div class="item4 gg">
              <span>￥{{ fpDetail?.kpje }}</span>
            </div>
            <div class="item5 gg">
              <span class="edit" @click="viewInvoice(fpDetail)">查看发票</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <!-- <nd-button type="primary" icon="FolderChecked" @click="save()">保&nbsp;存</nd-button> -->
      <nd-button type="" icon="Back" @click="close()">返&nbsp;回</nd-button>
    </template>
    <EditAddress ref="editAddressRef" @before-close="getDetail"> </EditAddress>
    <shipment-dialog
      ref="shipmentDialogRef"
      @before-close="getDetail"
    ></shipment-dialog>
    <OrderProcessing ref="orderProcessingRef"> </OrderProcessing>
  </nd-dialog>
</template>

<script setup>
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTooltip from "@/components/ndTooltip.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndbUpload from "@/components/business/ndbUpload/index.vue";
// 导入vue
import {
  ref,
  inject,
  onMounted,
  reactive,
  nextTick,
  watch,
  onUnmounted,
} from "vue";
// 导入element-plus
import { ElMessage as elMessage, messageEmits } from "element-plus";
import EditAddress from "./editAddress.vue";
import shipmentDialog from "./shipment.vue";
import OrderProcessing from "./orderProcessing.vue";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义ref
const dialogRef = ref(null);
const addFormRef = ref(null);
const editAddressRef = ref(null);
const shipmentDialogRef = ref(null);
const orderProcessingRef = ref(null);
// 打开发货dialog
function openShipment(params) {
  shipmentDialogRef.value.open(params, "shipment");
}
// 打开订单处理
function openOrderProcessingRef(params) {
  orderProcessingRef.value.open(params, "shipment");
}
// 定义page
const page = reactive({
  status: "",
  title: "",
  orderId: "",
  goodsInfo: {},
});

// 表单校验规则
const rules = reactive({
  name: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
});

// 打开弹窗
let invoiceStatusShow = ref("");
function open(params, status, invoiceStatus = "") {
  console.log("🚀 ~ open ~ params:", invoiceStatus);
  if (status === "detail") {
    page.status = "detail";
    page.title = "订单详情";
    invoiceStatusShow.value = invoiceStatus;
    page.orderId = params.orderId;

    getDetail();
    getWlInfo(); //物流
    if (invoiceStatus === "ykp") {
      getInvoiceDetail(params.fpId);
    }
    dialogRef.value.open();
  }
}
// 查看发票
const viewInvoice = (row) => {
  console.log("🚀 ~ viewInvoice ~ row:", row);
  const imgUrl = row.fpdz;
  const previewWindow = window.open("", "_blank");
  previewWindow.document.write(`<!DOCTYPE html>
          <html>
            <body style="background:black;display: flex;justify-content: center;align-items: center;">
                <div style="width:90vw;height:90vh;display: flex;justify-content: center;align-items: center;">
                  <img style="max-height: 50%; width: auto;" src='${imgUrl}'/>
                </div>
            </body>
          </html>
`);
};
//获取开票详情
let fpDetail = ref(null);
const getInvoiceDetail = (fpId) => {
  $axios({
    url: "/invoice/invoiceDetail",
    method: "get",
    serverName: "nd-base2",
    params: { fpId },
  }).then((res) => {
    fpDetail.value = res.data.data;
  });
};
//查看物流信息
let wlDateInfo = ref([]);
function getWlInfo() {
  return;
  $axios({
    url: "/order/manage/wl/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: page.orderId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      // wlDateInfo.value = res.data.data;
    }
  });
}
// 获得详情
function getDetail() {
  $axios({
    url: "/order/manage/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: page.orderId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.goodsInfo = res.data.data;
      wlDateInfo.value = res.data.data.detailVos.filter((el) => el.sffh == 1);
      console.log("🚀 ~ getDetail ~ wlDateInfo.value:", wlDateInfo.value);
    }
  });
}

// 关闭
const close = () => {
  emit("before-close");
  dialogRef.value.close();
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 清空表单
  page.goods = {
    id: "",
    name: "",
    files: [],
  };
};

// 保存
const save = async () => {
  await addFormRef.value.validate((valid, fields) => {
    if (valid) {
      // 新增
      if (page.status === "add") {
        add();
      }
      // 编辑
      if (page.status === "edit") {
        edit();
      }
    } else {
      console.log("校验失败!", fields);
    }
  });
};

// 新增
function add() {
  $axios({
    url: "/goods/add",
    method: "post",
    data: page.goods,
  }).then((res) => {
    if (res.data.code === 200) {
      elMessage({
        message: "保存成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 编辑
function edit() {
  $axios({
    url: "/goods/update",
    method: "post",
    data: page.goods,
  }).then((res) => {
    if (res.data.code === 200) {
      elMessage({
        message: "保存成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}
// 复制
const copy = (item) => {
  let text = "";
  // 根据 item 的不同属性判断 text 的值
  if (item.sfcd === 0) {
    text = item.orderNo;
  } else if (item.sfcd === 1) {
    if (
      item.status === 0 ||
      (item.status === 51 && item.cancelType === 1) ||
      (item.status === 51 && item.cancelType === 4)
    ) {
      text = item.orderNo;
    } else {
      text = item.orderNoChild;
    }
  }
  const tempInput = document.createElement("textarea");
  tempInput.value = text;
  tempInput.style.position = "fixed";
  tempInput.style.opacity = 0;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand("copy");
  tempInput.remove();
  elMessage({
    message: "复制成功",
    type: "success",
  });
};
// 定义倒计时的 ref
const countdown = ref("");
let timer = null;
// 获取订单待支付时间
const getOrderTime = (time) => {
  if (timer) {
    clearInterval(timer);
  }
  // 假设订单在下单后 30 分钟内未支付则关闭，可根据实际情况修改
  const orderCreateTime = new Date(time).getTime();
  const expireTime = orderCreateTime + 30 * 60 * 1000;
  const updateCountdown = () => {
    const now = new Date().getTime();
    const remainingTime = expireTime - now;
    if (remainingTime <= 0) {
      clearInterval(timer);
      // countdown.value = '00:00';
      countdown.value = "0 分 0 秒";
      return;
    }
    const minutes = Math.floor(
      (remainingTime % (1000 * 60 * 60)) / (1000 * 60)
    );
    const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);
    // countdown.value = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    countdown.value = `${minutes} 分 ${seconds} 秒`;
  };
  // 立即更新一次倒计时
  updateCountdown();
  // 每秒更新一次倒计时
  timer = setInterval(updateCountdown, 1000);
  return countdown.value;
};
//弹出地址showAddress
const showAddress = (dzglVo) => {
  editAddressRef.value.open(dzglVo);
};
// 暴露方法给父组件
defineExpose({
  open,
});
// 查看物流
const viewShipmentRequest = () => {
  elMessage({
    message: "功能开发中...",
    type: "warning",
  });
};

// 在组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
//超出省略触发内容
const tooltipShowFn = (flage, type, value1, value2) => {
  console.log(
    "🚀 ~ tooltipShowFn ~ flage, type, value1, value2:",
    flage,
    type,
    value1,
    value2
  );
  // flage 1:物流单号
  let returnText = "-";
  if (flage == "wl") {
    if (type == 1) {
      returnText = value1;
    }
    if (type == 2) {
      returnText = value2;
    }
  }
  console.log("🚀 ~ tooltipShowFn ~ returnText:", returnText);
  return returnText;
};
</script>

<style lang="scss" scoped>
:deep(.nd-dialog-box) {
  padding: 0 !important;
}

:deep(.el-dialog__body) {
  background-color: #f7f7f7;
}

.main {
  width: 100%;
  min-height: 866px;
  background-color: #f7f7f7;
  display: flex;
  align-content: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  padding: 12px;

  // 订单状态
  .statusMain {
    width: 100%;
    min-height: 206px;
    border-radius: 5px;
    padding: 12px 24px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    display: flex;
    align-content: center;
    align-items: center;

    .left {
      width: 304px;
      height: 182px;

      .code {
        width: 100%;
        height: 24px;
        font-size: 14px;
        display: flex;
        .text1 {
          color: #999999;
        }

        .text2 {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
        }
        .copy {
          width: 18px;
          height: 18px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .status {
        width: 100%;
        height: 24px;
        font-size: 22px;
        font-weight: 600;
        color: red;
        margin-top: 20px;
      }

      .dateline {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 13px;
        font-weight: 400;
        color: #333;
        margin-top: 10px;
        letter-spacing: 0px;

        .min {
          color: #ff0001;
        }
      }

      .btn {
        width: 100%;
        height: 32px;
        margin-top: 10px;

        .button {
          width: 80px;
          height: 32px;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          // padding: 4px 16px;
          color: #ffffff;
          gap: 8px;
          background: #068324;
          z-index: 2;
          cursor: pointer;
        }
      }
    }

    .line {
      width: 1px;
      height: 110px;
      background-color: #ebeef5;
      margin: 0 40px;
    }

    .right {
      min-width: 740px;
      height: 182px;
      display: flex;
      flex-wrap: wrap;

      .iconFlow {
        width: 100%;
        display: flex;
        align-content: center;
        align-items: center;
        justify-content: space-between;

        .flow {
          // min-width: 60px;
          height: 66px;
          display: flex;
          justify-content: center;
          flex-wrap: wrap;

          .icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-content: center;
            justify-content: center;
            align-items: center;

            img {
              width: 15px;
              height: 15px;
            }
          }

          .iconAct {
            background-color: #068324;
          }

          .text {
            width: 100%;
            height: 24px;
            line-height: 24px;
            color: #666;
            div {
              white-space: nowrap;
              text-align: center;
            }
          }
        }

        .arrLine {
          width: 87.69px;
          height: 20px;
          margin: 0 0px 30px;
        }
      }

      .textarea {
        width: 100%;
        display: flex;
        align-items: center;

        .label {
          width: 66px;
          height: 22px;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          text-align: right;
          color: #555555;
          margin-right: 10px;
          z-index: 1;
        }

        .textareaContent {
          width: 658px;
          height: 80px !important; // 强制设置容器高度

          :deep(.el-textarea__inner) {
            height: 80px !important;
            min-height: 80px !important;
          }
        }
      }
    }
  }

  // 订单信息
  .orderMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;
    color: #333;
    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #333;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
      }

      .body {
        width: 100%;
        min-height: 112px;
        display: flex;
      }

      .item1 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;

        .edit {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
          width: 16px;
          height: 16px;
          img {
            width: 16px;
            height: 16px;
          }
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .item3 {
        flex: 1;
        padding: 8px 12px;
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
  }

  //退款原因
  .tukuanMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #333;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
      }

      .body {
        width: 100%;
        min-height: 112px;
        display: flex;
      }

      .item1 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;

        .edit {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
  }

  //退款原因
  .wuliuMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #333;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
        div {
          display: flex;
          justify-content: center;
          text-align: center;
          align-items: center;
          align-content: center;
        }
      }

      .body {
        width: 100%;
        // height: 112px;
        min-height: 42px;
        display: flex;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }
      }

      .item1 {
        flex: 2;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
        .edit {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .item2 {
        flex: 8;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
        overflow: hidden;
        //文字省略
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .item3 {
        flex: 12;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .item4 {
        flex: 2;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item5 {
        flex: 4;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item6 {
        flex: 8;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item7 {
        flex: 8;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item8 {
        flex: 8;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item9 {
        flex: 4;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item10 {
        flex: 4;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;

        .edit {
          margin-left: 6px;
          color: #0098ff;
          // text-decoration: underline;
          cursor: pointer;
        }
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
  }

  // 商品信息
  .goodMain {
    width: 100%;
    min-height: 245px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #333;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
        text-align: center;
        div {
          display: flex;
          justify-content: center;
          text-align: center;
          align-items: center;
          align-content: center;
        }
      }

      .body {
        width: 100%;
        height: 112px;
        display: flex;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }
      }

      .item1 {
        flex: 4;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        display: flex;

        .goodImg {
          width: 90px;
          height: 90px;
          border-radius: 4px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .goodInfo {
          // width: 100%;
          text-align: left;
          margin-left: 10px;

          .title {
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 10px;
            display: flex;
          }
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item3 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item4 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        text-align: center;
      }

      .item5 {
        flex: 1;
        padding: 8px 12px;
        text-align: center;
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0px;
        }
      }
    }

    .priceAll {
      width: 100%;
      height: 24px;
      display: flex;
      justify-content: flex-end;
      align-content: center;
      align-items: center;

      .text1 {
        font-size: 14px;
        color: #333333;
        z-index: 0;
      }

      .text2 {
        opacity: 1;
        font-family: Microsoft YaHei;
        font-size: 22px;
        font-weight: bold;
        color: #ff0001;
        margin-left: 30px;
        text-decoration: none;
      }
    }
  }
  //开票信息
  .kpMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;
    // 省略号
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #333;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #f9f9f9;
      }

      .body {
        width: 100%;
        min-height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }
      }

      .item1 {
        flex: 2;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        .edit {
          margin-left: 6px;
          color: #0098ff;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .item2 {
        flex: 4;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .item3 {
        flex: 7;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .item4 {
        flex: 7;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }
      .item5 {
        flex: 5;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;

        .edit {
          margin-left: 6px;
          color: #0098ff;
          // text-decoration: underline;
          cursor: pointer;
        }
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
  }
}
.text2 {
  margin-left: 6px;
  color: #0098ff;
  text-decoration: underline;
  cursor: pointer;
}

.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 2px;
  background: #20b203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;
}

.gg {
  // 省略
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
//超链
.mfAbq {
  color: #0098ff;
  text-decoration: underline;
  cursor: pointer;
}
</style>
