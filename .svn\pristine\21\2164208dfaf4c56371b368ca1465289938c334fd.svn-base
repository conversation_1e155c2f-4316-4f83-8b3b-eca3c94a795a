<template>
  <ndb-page-list>
    <template #tag>
      <nd-tag @click="tagClick('')" :checked="page.search.data.label === 'pending'">全部</nd-tag>
      <nd-tag @click="tagClick(0)" :checked="page.search.data.label === 'approved'">待付款</nd-tag>
      <nd-tag @click="tagClick(1)" :checked="page.search.data.label === 'initiated'">待发货</nd-tag>
      <nd-tag @click="tagClick(50)" :checked="page.search.data.label === 'cancellationRequest'">申请取消</nd-tag>
      <nd-tag @click="tagClick(2)" :checked="page.search.data.label === 'received'">已发货</nd-tag>
      <nd-tag @click="tagClick(3)" :checked="page.search.data.label === 'completed'">已完成</nd-tag>
      <nd-tag @click="tagClick(51)" :checked="page.search.data.label === 'closed'">已关闭</nd-tag>
    </template>
    <!-- <template #button>
      <nd-button @click="openAddDialog()" type="primary" icon="plus" authKey="DEMO_VIEW_ADD">新建</nd-button>
      <nd-button @click="openImportDialog()" icon="Download" authKey="DEMO_VIEW_IMPORT">导入</nd-button>
      <nd-button @click="openExportDialog()" icon="Upload" authKey="DEMO_VIEW_EXPORT">导出</nd-button>
    </template> -->
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="商品类型" width="120px">
          <nd-select  :empty-values="[]"
          :value-on-clear="''" v-model="page.search.data.splx" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.splxList" :label="item.name" :key="item.categoryId"
              :value="item.categoryId" />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="日期搜索" width="120px">
          <nd-select :empty-values="[]"
          :value-on-clear="''" v-model="page.search.data.rqlx" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.datetypeList" :label="item.name" :key="item.modelId"
              :value="item.modelId" />
          </nd-select>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-date-picker v-model="page.search.data.cycleArr" range-separator="至" type="daterange" format="YYYY-MM-DD "
            value-format="YYYY-MM-DD" placeholder="请选择" width="100%"></nd-date-picker>
        </nd-search-more-item>
        <nd-search-more-item title="关键字搜索" width="120px">
          <!-- <nd-select v-model="page.search.data.modelId" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.approveltypeList" :label="item.name" :key="item.modelId"
              :value="item.modelId" />
          </nd-select> -->
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-input type="text" v-model="page.search.data.gjzValue" width="100%" placeholder="请输入" resize="none" />
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="getTableData" authKey=""> 查询 </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <!-- <el-card style="max-width: 1696px"> -->
      <div class="tableMock">
        <!-- 表头 -->
        <div class="tableHeader  borderBottom">
          <div class="select  borderRight">
            <el-checkbox @change="selectAll" v-model="checkedAll" size="large" />
          </div>
          <div class="orderInfo  borderRight">订单信息</div>
          <div class="productType  borderRight">商品类型</div>
          <div class="tabelshippingInfo  borderRight">收货信息</div>
          <div class="tableStatus  borderRight">状态</div>
          <div class="tableOperation ">操作</div>
        </div>
        <!-- 内容 -->
        <div v-if="page.list.data.length" class="tableContent">
          <div v-for="(item, index) in page.list.data" :key="index" class="tableItem">
            <div class="tableItemTop borderTop borderBottom">
              <div class="select  borderRight">
                <el-checkbox @change="selectItem(item)" v-model="item.checked" size="large" />
              </div>
              <div class="tableItemTopOther">
                <div class="otherLeft">
                  <span> 下单时间：{{ item.xdTime }} </span>
                  <span> 订单编号：
                    <span v-if="item.sfcd == 0">{{ item.orderNo }}</span>
                    <span v-if="item.sfcd == 1">
                      <span
                        v-if="item.status == 0 || (item.status == 51 && item.cancelType == 1) || (item.status == 51 && item.cancelType == 4)">{{
                          item.orderNo }}</span>
                      <span v-else>
                        {{ item.orderNoChild }}
                      </span>
                    </span>
                  </span>
                </div>
                <div class="otherRight">
                  <span> 共{{ numberToChinese(item.ddspsl) }}件商品，</span>
                  <span> 合计：¥{{ item.amount }}</span>
                </div>
              </div>
            </div>
            <div class="tableItemContent borderBottom">
              <div class="goodsItem">
                <div class="padding orderInfo orderInfoFlex orderInfoaddselect borderRight">
                  <div v-for="(good, index) in item.detailVos" :key="index" class="goodsItemList">
                    <div class="goodImg"> <img :src="good.productVo.pic" alt=""></div>
                    <div class="goodInfo">
                      <div class="goodTitle">
                        <div class="left">
                          <!-- <div class="mallorder-card-center-details-tag">
                            <span v-for="(tag, index) in good.productVo.tagVos" :key="index" class="titleLv"
                              :style="`background-color: ${tag.color}`">
                              {{
                                tag.name }} </span>
                          </div> -->

                          {{ good.productVo.name }}
                        </div>
                        <div class="right">¥{{ good.amount }}</div>
                      </div>
                      <div class="goodNum">
                        <div class="left">{{ good.productVo.spData }}</div>
                        <div class="right">x{{ good.nums }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class=" productType borderRight">
                  <div class="productTypeItem" v-for="(productType, index) in item.categoryNameList" :key="index">
                    {{ productType }}
                  </div>
                </div>
                <div class="padding tabelshippingInfo borderRight">
                  <div class="maijia">
                    买家：{{ item.dzglVo.name }}
                  </div>
                  <div class="lxfs">
                    联系方式：{{ item.dzglVo.phone }}
                  </div>
                </div>
                <div class="padding tableStatus borderRight">

                  <div class="dot" style="background-color: #267FFF;" v-if="item.status == 0 && item.sfsqsh == 0">
                  </div>
                  <div class="dot" style="background-color: #FF8D02;" v-else-if="item.status == 1 && item.sfqx == 0">
                  </div>
                  <div class="dot" style="background-color: #999999;" v-else-if="item.status == 1 && item.sfqx == 1">
                  </div>
                  <div class="dot" style="background-color: #068324;" v-else-if="item.status == 2 && item.sfsqsh == 0">
                  </div>
                  <div class="dot" style="background-color: #068324;" v-else-if="item.status == 3 && item.sfsqsh == 0">
                  </div>
                  <div class="dot" style="background-color: #999999;" v-else-if="item.sfsqsh = 1 && item.shStatue == 1">
                  </div>
                  <div class="dot" style="background-color: #999999;" v-else-if="item.sfsqsh = 1 && item.shStatue == 2">
                  </div>
                  <div class="dot" style="background-color: #999999;" v-else-if="item.sfsqsh = 1 && item.shStatue == 3">
                  </div>
                  <span class="dot" style="background-color: #999999;" v-else-if="item.status == 51"></span>
                  <div class="text">
                    <span v-if="item.status == 0 && item.sfsqsh == 0">待付款</span>
                    <span v-else-if="item.status == 1 && item.sfqx == 0">待发货</span>
                    <span v-else-if="item.status == 1 && item.sfqx == 1">申请取消</span>
                    <span v-else-if="item.status == 2 && item.sfsqsh == 0">已发货</span>
                    <span v-else-if="item.status == 3 && item.sfsqsh == 0">已完成</span>
                    <span v-else-if="item.sfsqsh = 1 && item.shStatue == 1">处理中</span>
                    <span v-else-if="item.sfsqsh = 1 && item.shStatue == 2">售后完成</span>
                    <span v-else-if="item.sfsqsh = 1 && item.shStatue == 3">售后关闭</span>
                    <span v-else-if="item.status == 51">已关闭</span>
                    <span v-else-if="item.status == 60">已关闭</span>
                  </div>
                </div>
                <div class="padding tableOperation">
                  <div class="operationItem" @click="openDetail(item)">订单详情
                  </div>
                  <div v-if="item.status == 1&&item.sfqx == 0" class="operationItem" @click="openShipment(item)">发货</div>
                  <!-- <div class="operationItem" v-if="item.sfqx != 1||item.status == 51">联系买家</div> -->
                  <div v-if="item.status == 2 || item.status == 3" class="operationItem">查看物流</div>
                  <div @click="openOrderProcessingRef(item)" v-if="item.status == 1&&item.sfqx == 1" class="operationItem">去处理</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <el-empty :image-size="100" v-else description="暂无数据" />
      </div>

      <!-- </el-card> -->
      <!-- <nd-table  :show-header="false"  border class="noPadding" style="height: 100%" :data="page.list.data"
        @selection-change="handleSelectionChange">
        <el-table-column >
          <template #default="scoped">
            <div style="width: 100%;">
    
              <div class="header" style="display: flex;" v-if="scoped.row.index==1">
                <div style="width: 180px;">属性1</div>
                <div style="width: 180px;">属性2</div>
                <div style="width: 180px;">属性3</div>
              </div>
              <div style="width: 100%;height: 30px;background-color: #F8FFFA;display: flex;align-items: center;">
                <el-checkbox v-model="checked1" />
                下单时间
              </div>

              <div style="display: flex;">
                <div style="width: 180px;">{{ scoped.row.date }}</div>
                <div style="width: 180px;">{{ scoped.row.name }}</div>
                <div style="width: 180px;">{{ scoped.row.address }}</div>
              </div>
            </div>
          </template>
</el-table-column>
<el-table-column align="center" label="订单信息" prop="name" width="520px">
  <template #default="scoped">
            <div class="order_details">
              <div class="top">
                <div class="time"><span>下单时间</span> <span>{{ scoped.row.creationTime }}</span></div>
                &nbsp; &nbsp; &nbsp; &nbsp;
                <div class="code"><span>订单编号</span> <span>{{ scoped.row.code }}</span></div>
                <div class="total">共{{ scoped.row.total }}件商品</div>
                &nbsp; &nbsp; &nbsp; &nbsp;
                <div class="price"><span>总价</span> <span>{{ scoped.row.priceAll }}</span></div>
              </div>
              <div class="good">
                <div class="goodItem" v-for="items in scoped.row.orderList" :key="item">
                  <div class="left">
                    <img :src="items.image" alt="">
                  </div>
                  <div class="mid">
                    <div class="title">{{ items.title }}</div>
                    <div class="spac">{{ items.weight }}</div>
                  </div>
                  <div class="right">
                    <div class="price">{{ items.price }}</div>
                    <div class="number">x{{ items.count }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
</el-table-column>
<el-table-column align="center" label="商品类型" prop="originalPrice">
  <template #default="scoped">
            <div class="goodType">
              <div class="top"></div>
              <div v-for="item in scoped.row.orderList" :key="item" class="goodTypeItem">
                <span>优质鱼苗</span>
              </div>
            </div>
          </template>
</el-table-column>
<el-table-column align="center" label="收货信息" prop="discountPrice">
  <template #default="scoped">
            <div class="top2"></div>
            <div class="other">
              <div>收货人</div>
            </div>
          </template>
</el-table-column>
<el-table-column align="center" label="联系方式" prop="discountPrice">
  <template #default="scoped">
            <div class="top2"></div>
            <div class="other">
              <div>联系方式</div>
            </div>
          </template>
</el-table-column>
<el-table-column fixed="right" align="center" label="操作" width="250px">
  <template #default="scoped">
            <div class="top2"></div>
            <div style="display: flex; justify-content: space-around">
              <nd-button type="edit" @click="openEditDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_EDIT">订单详情</nd-button>
              <nd-button type="edit" @click="openEditDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_EDIT">联系买家</nd-button>
              <nd-button type="delete" @click="openDeleteDialog(scoped.row)"
                authKey="APPROVE_SETUP_FORM_DELETE">备注</nd-button>
            </div>
          </template>
</el-table-column>
</nd-table> -->
    </template>
    <template #page>
      <nd-pagination :current-page="page.pager.pageIndex" :page-size="page.pager.pageSize" :total="page.pager.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </template>
  </ndb-page-list>
  <ndb-import ref="importRef" type="PRO_TASK" titles="任务导入" projectId="1" />
  <ndb-export ref="exportRef" />
  <add-dialog ref="addDialogRef" @before-close="getTableData"></add-dialog>
  <shipment-dialog ref="shipmentDialogRef" @before-close="getTableData"></shipment-dialog>
  <OrderProcessing ref="orderProcessingRef" @before-close="getTableData"> </OrderProcessing>
  
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import ndbExport from "@/components/business/ndbExport/index.vue";
import OrderProcessing from "./components/orderProcessing.vue";
// 导入子组件
import addDialog from "./components/addDialog.vue";
import shipmentDialog from "./components/shipment.vue";
// 导入vue
import { onMounted, reactive, ref, inject, watch, computed, nextTick } from "vue";
import { ElMessage as elMessage, ElMessageBox as elMessageBox } from "element-plus";

// 定义axios
const $axios = inject("$axios");

// 定义ref
const addDialogRef = ref(null);
const shipmentDialogRef = ref(null);
const exportRef = ref(null);
const importRef = ref(null);
const orderProcessingRef = ref(null)
// 定义page
const page = reactive({
  tree: {
    data: [],
    defaultProps: {
      children: "childs",
      label: "deptName",
    },
  },
  search: {
    data: {
      label: "pending",
      rqlx: "",//日期类型 XD:下单时间 FK:付款时间 FH:发货时间 SH:收货时间 
      status: "",
      splx: '',//商品类型
      gjzValue: '',//关键字-过滤值
      cycleArr: ["", ""],
    },
    dict: {
      approveltypeList: [], //审批类型
      statusList: [

      ], //状态
      splxList: [
      ],
      datetypeList: [
        { modelId: "XD", name: "下单时间" },
        { modelId: "FK", name: "付款时间" },
        { modelId: "FH", name: "发货时间" },
        { modelId: "SH", name: "确认收货时间" },
      ],
    },
  },
  list: {
    data: [

    ],
  },
  pager: {
    pageIndex: 1,
    pageSize: 30,
    total: 0,
  },
});

// 分类改变
function tagClick(index) {
  if (index === '') {
    page.search.data.label = "pending";
  }
  if (index === 0) {
    page.search.data.label = "approved";
  }
  if (index === 1) {
    page.search.data.label = "initiated";
  }
  if (index === 2) {
    page.search.data.label = "received";
  }
  if (index === 3) {
    page.search.data.label = "completed";
  }
  if (index === 50) {
    page.search.data.label = "cancellationRequest";
  }
  if (index === 51) {
    page.search.data.label = "closed";
  }
  page.search.data.status = index;
  // 获得表格数据
  getTableData();
}

// 获得审批类型
function getApprovalType() {
  return
  $axios({
    url: "/oa/model/selectItem",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      page.search.dict.approveltypeList = res.data.data;
    }
  });
}
// 获得商品分类
const getGoodsType = () => {
  $axios({
    url: "/dict/getCategory",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      page.search.dict.splxList = res.data.data;
    }
  });
}

// 获得表格数据
function getTableData() {
  $axios({
    url: "/order/manage/page/findAll",
    serverName: 'nd-base2',
    method: "get",
    data: {
      page: page.pager.pageIndex,
      size: page.pager.pageSize,
      splx: page.search.data.splx,
      status: page.search.data.status,//商品状态 0待付款 1:待发货 2:待收货 3:已完成 50:订单取消中 51:订单已取消 20:已发货
      rqlx: page.search.data.rqlx,//日期类型 XD:下单时间 FK:付款时间 FH:发货时间 SH:收货时间
      beginDate: page.search.data.cycleArr ? page.search.data.cycleArr[0] : "", // 开始时间
      endDate: page.search.data.cycleArr ? page.search.data.cycleArr[1] : "", // 结束时间
      gjzLb: '',
      gjzValue: page.search.data.gjzValue,//关键字-过滤值
      del: ''
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.records.map((item, index) => {
        return {
          ...item,
          checked: false, // 选中框
        }
      });
      page.pager.total = res.data.data.total; //总页数
      page.pager.pageIndex = res.data.data.current; //当前页
      page.pager.pageSize = res.data.data.size; //每页记录数
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 重置
function reset() {
  page.search.data.splx = "";
  page.search.data.rqlx = "";
  page.search.data.cycleArr = ["", ""];
  page.search.data.gjzValue = "";
  getTableData();
}

// 打开新增对话框
function openAddDialog() {
  addDialogRef.value.open("add", null);
}

// 选中框
const selectedPoints = ref([]);
const handleSelectionChange = (val) => {
  selectedPoints.value = val.map((item) => item.buildId);
};

// // 打开编辑dialog
// function openEditDialog(params) {
//   addDialogRef.value.open("detail", params);
// }
// 打开详情dialog
function openDetail(params) {
  addDialogRef.value.open(params, "detail");
}
// 打开发货dialog
function openShipment(params) {
  shipmentDialogRef.value.open(params, "shipment");
}
// 打开删除dialog
function openDeleteDialog(data) {
  elMessageBox
    .confirm("确定删除?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      $axios({
        url: "/goods/delete",
        method: "post",
        data: {
          goodsId: data.id,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          elMessage({
            message: "删除成功",
            type: "success",
          });
          getTableData();
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    });
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

// 导入
function openImportDialog() {
  importRef.value.open();
}

// 导出
function openExportDialog() {
  let params = {
    label: "approved",
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    label: page.search.data.label,
    status: page.search.data.status, // 流程状态
    modelId: page.search.data.modelId, // 审批类型
    startTime: page.search.data.cycleArr ? searchData.cycleArrfq[0] + ":00" : "", // 发起开始时间
    endTime: page.search.data.cycleArr ? searchData.cycleArrfq[1] + ":00" : "", // 发起结束时间
  };
  exportRef.value.open(params, "OA_EXAMINE_LIET", "");
}
// 数字转为中文
function numberToChinese(num) {
  if (num === 0) return '零';
  const chineseNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const chineseUnit = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千'];
  const str = num.toString();
  let result = '';
  let zeroFlag = false;
  const len = str.length;
  for (let i = 0; i < len; i++) {
    const digit = parseInt(str[i], 10);
    const unitIndex = len - i - 1;

    if (digit === 0) {
      zeroFlag = true;
      if (unitIndex % 4 === 0) { // 万、亿位
        result += chineseUnit[unitIndex];
        zeroFlag = false;
      }
    } else {
      if (zeroFlag) {
        result += '零';
        zeroFlag = false;
      }
      result += chineseNum[digit] + chineseUnit[unitIndex];
    }
  }
  // 处理以零结尾的情况
  while (result.endsWith('零')) {
    result = result.slice(0, -1);
  }
  // 处理一十开头的情况
  if (result.startsWith('一十')) {
    result = result.slice(1);
  }
  return result;
}
// 全选反选
let checkedAll = ref(false);
// 全选/反选
const selectAll = (checked) => {
  page.list.data.forEach(item => {
    item.checked = checked;
  });
};

// 单个选择
const selectItem = () => {
  checkedAll.value = page.list.data.every(item => item.checked);
};
// 打开订单处理
function openOrderProcessingRef(params) {
  orderProcessingRef.value.open(params, "shipment");
}
// onMounted
onMounted(() => {
  // 获得审批类型
  getApprovalType();
  // 获得表格数据
  getTableData();
  //获得商品分类
  getGoodsType()

  ///
});
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.workhour {
  color: #444444;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;

  .workhour-item-line {
    width: 4px;
    height: 15px;
    border-radius: 3px;
    background-color: red;
    margin-right: 12px;
  }

  .workhour-item {
    margin-right: 23px;

    .red {
      color: red;
    }

    .blue {
      color: #10a3e7;
    }
  }
}

:deep(.noPadding .el-table__cell) {
  padding: 0 !important;
}

:deep(.noPadding .cell) {
  padding: 0;
  height: 100%;
}


// 订单详情
.order_details {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .good {
    width: 100%;


    .goodItem {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 3px 5px;
      border-bottom: 1px solid #ebeef5;

      // 左
      .left {
        width: 80px;
        height: 80px;

        img {
          width: 80px;
          height: 80px;
        }
      }

      //中
      .mid {
        width: 240px;
        line-height: 40px;
        text-align: left;
        margin-left: 20px;
      }

      // 右
      .right {
        width: 180px;
        line-height: 40px;
        margin-left: auto;
      }
    }

    :last-of-type {
      border: none;
    }
  }


}

//商品类型
.goodType {

  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;

  // padding-top: 35px;
  .goodTypeItem {
    box-sizing: content-box;
    padding: 3px 5px;
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #ebeef5;
  }

  :last-of-type {
    border: none;
  }
}

//收货信息
.shippingInfo {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}



.top {
  width: 100%;
  height: 30px;
  background-color: #FDEDED !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
}

.top2 {
  width: 100%;
  height: 30px;
  background-color: #FDEDED !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
  position: absolute;
  top: 0;
}

//内容仿表格
:deep(.el-card__body) {
  border-left: 1px solid #EDEDED;
}

.tableMock {
  border: 1px solid #EDEDED;
  max-width: 1658px;
  background-color: #fff;
  height: 100%;
  overflow: hidden;
  position: relative;
  padding-top: 38px;
  display: flex;
  align-content: center;
  justify-content: center;
  align-content: center;
}

.tableHeader {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
  line-height: 38px;
  height: 38px;
  background: #F9F9F9;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  padding-right: 6px;
  box-sizing: border-box;
}

.tableHeader div {
  text-align: center;
  font-family: Microsoft YaHei UI;
  font-size: 14px;
  color: #303133;
  z-index: 0;
}

.tableContent {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  overflow: auto;
  height: 100%;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  /* 滚动条轨道背景颜色 */
}

::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  /* 滚动条滑块颜色 */
  border-radius: 3px;
  /* 滚动条滑块圆角 */
}

::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
  /* 鼠标悬停时滚动条滑块颜色 */
}

.borderLeft {
  border-left: 1px solid #EDEDED;
}

.borderRight {
  border-right: 1px solid #EDEDED;
}

.borderTop {
  border-top: 1px solid #EDEDED;
}

.borderBottom {
  border-bottom: 1px solid #EDEDED;
}

.select {
  width: 60px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.orderInfo {
  width: 890px;
}

.orderInfoFlex {
  display: flex;
  justify-content: space-between;
}

.goodImg {
  width: 90px;
  height: 90px;
  border-radius: 4px;
  opacity: 1;
  padding: 0 !important;
}

.goodImg img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.goodInfo {
  width: 826px;
  height: 94px;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  flex-wrap: wrap;
  align-items: flex-start;
  align-content: flex-start;

}

.goodInfo .goodTitle {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.goodInfo .goodTitle .left {
  // width: 245px;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  z-index: 0;
  text-align: left;
  display: flex;
}

.goodInfo .goodTitle .right {
  width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: right;

}

.goodInfo .goodNum {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;

}

.goodInfo .goodNum .left {
  min-width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: left;
  // 省略
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goodInfo .goodNum .right {
  min-width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: right;
}

.orderInfoaddselect {
  width: 950px;

}

.productType {
  width: 180px;
  font-size: 14px;
  color: #555;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  display: grid;
  grid-template-rows: repeat(auto-fill, 1fr);
}

.productTypeItem {
  width: 100%;
  height: auto;
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
}

.productTypeItem:last-of-type {
  border: none;
}

.tabelshippingInfo {
  width: 300px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.tabelshippingInfo .maijia,
.tabelshippingInfo .lxfs {
  width: 100%;
  height: 25px;
  line-height: 25px;
  margin-bottom: 10px;
}

.tableStatus {
  width: 106px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
}

.tableStatus .dot {
  width: 8px;
  height: 8px;
  border-radius: 100px;
  background: #FF0001;
  margin-right: 5px;
}

.tableOperation {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.tableOperation .operationItem {
  width: 100%;
  height: 19px;
  font-size: 14px;
  display: flex;
  align-items: center;

  color: #0B8DF1;
  margin-bottom: 6px;
  cursor: pointer;
}

.tableItem {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  margin-top: 20px;
}

.tableItemTop {
  width: 100%;
  height: 38px;
  background: #F8FFFA;
  display: flex;
  flex-wrap: nowrap;
}

.tableItemTopOther {
  width: 1536px;
  font-size: 14px;
  line-height: 38px;
  color: #303133;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.tableItemContent {
  width: 100%;
  // width: 1596px;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
}

.goodsItem {
  width: 100%;
  box-sizing: border-box;
  min-height: 110px;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
}

.goodsItem .padding {
  padding: 10px 12px;
  text-align: center;
  font-size: 14px;
  color: #555;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  box-sizing: border-box;
  flex-wrap: wrap;
}

.goodsItemList {
  width: 100%;
  display: flex;
  // flex-wrap: wrap;
}

.mallorder-card-center-details-tag {
  display: flex;

}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 2px;
  background: #20B203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;

}

.u-empty .uni-image {
  width: 400rpx !important;
  height: 400rpx !important;
}
</style>
