<template>
  <div
    class="normal"
    v-if="
      type !== 'search' &&
      type !== 'edit' &&
      type !== 'delete' &&
      type !== 'document' &&
      type !== 'document2' &&
      type !== 'refreshLeft' &&
      type !== 'fileSupply' &&
      type !== 'fileCheck' &&
      type !== 'audit' &&
      type !== 'againAdd' &&
      type !== 'approve' &&
      type !== 'deal' &&
      type !== 'writeOff' &&
      type !== 'ysgl' &&
      type !== 'jyqr' &&
      type !== 'qr'&&
      type !== 'ck'&&
      type !== 'fp'&&
      type !== 'projectPayOut' &&
      type !== 'projectDetail' &&
      type !== 'announce'
    "
  >
    <el-button :type="type" @click="clickHandler" :disabled="_disabled" v-bind="$attrs" :class="type === 'primary' ? 'primary-button' : type === 'disable' ? 'disable-button' : 'normal-button'">
      <slot></slot>
    </el-button>
  </div>
  <!-- 衔接资金 -->
  <div class="search" v-else-if="type === 'search'">
    <el-button type="default" @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <el-icon :size="14" :color="'#068324'" style="margin-right: 2px">
        <Search />
      </el-icon>
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'edit'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="编辑" class="image" src="@/assets/images/button/edit.png" v-if="!disabled" /> -->
      <!-- <img title="编辑" class="image" src="@/assets/images/button/editDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'delete'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="删除" class="image" src="@/assets/images/button/delete.png" v-if="!disabled" /> -->
      <!-- <img title="删除" class="image" src="@/assets/images/button/deleteDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'document'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="详情" class="image" src="@/assets/images/button/detail.png" v-if="!disabled" /> -->
      <!-- <img title="详情" class="image" src="@/assets/images/button/detailDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'document2'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="查看" class="image" src="@/assets/images/button/detail.png" v-if="!disabled" /> -->
      <!-- <img title="查看" class="image" src="@/assets/images/button/detailDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'refreshLeft'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="撤销" class="image" src="@/assets/images/button/cancel.png" v-if="!disabled" /> -->
      <!-- <img title="撤销" class="image" src="@/assets/images/button/cancelDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'fileSupply'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="附件补充" class="image" src="@/assets/images/button/fileSupply.png" v-if="!disabled" /> -->
      <!-- <img title="附件补充" class="image" src="@/assets/images/button/fileSupplyDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'fileCheck'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="附件查看" class="image" src="@/assets/images/button/fileCheck.png" v-if="!disabled" /> -->
      <!-- <img title="附件查看" class="image" src="@/assets/images/button/fileCheckDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'audit'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="审核" class="image" src="@/assets/images/button/audit.png" v-if="!disabled" /> -->
      <!-- <img title="审核" class="image" src="@/assets/images/button/auditDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'againAdd'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="重新申请" class="image" src="@/assets/images/button/againAdd.png" v-if="!disabled" /> -->
      <!-- <img title="重新申请" class="image" src="@/assets/images/button/againAddDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'approve'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="审批" class="image" src="@/assets/images/button/approve.png" v-if="!disabled" /> -->
      <!-- <img title="审批" class="image" src="@/assets/images/button/approveDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'deal'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="处理" class="image" src="@/assets/images/button/deal.png" v-if="!disabled" /> -->
      <!-- <img title="处理" class="image" src="@/assets/images/button/dealDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'writeOff'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="核销" class="image" src="@/assets/images/button/writeOff.png" v-if="!disabled" /> -->
      <!-- <img title="核销" class="image" src="@/assets/images/button/writeOffDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'ysgl'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="预算管理" class="image" src="@/assets/images/button/ysgl.png" v-if="!disabled" /> -->
      <!-- <img title="预算管理" class="image" src="@/assets/images/button/ysglDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'jyqr'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="结余确认" class="image" src="@/assets/images/button/jyqr.png" v-if="!disabled" /> -->
      <!-- <img title="结余确认" class="image" src="@/assets/images/button/jyqrDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'qr'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="确认" class="image" src="@/assets/images/button/qr.png" v-if="!disabled" /> -->
      <!-- <img title="确认" class="image" src="@/assets/images/button/qrDisable.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'ck'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="查看" class="image" src="@/assets/images/button/ck.png" v-if="!disabled" /> -->
      <!-- <img title="查看" class="image" src="@/assets/images/button/ckdisable.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'fp'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="项目资金分配" class="image" src="@/assets/images/button/fp.png" v-if="!disabled" /> -->
      <!-- <img title="项目资金分配" class="image" src="@/assets/images/button/fpDisable.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'projectPayOut'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="项目支出" class="image" src="@/assets/images/button/projectPayOut.png" v-if="!disabled" /> -->
      <!-- <img title="项目支出" class="image" src="@/assets/images/button/projectPayOutDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'projectDetail'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="项目详情" class="image" src="@/assets/images/button/projectDetail.png" v-if="!disabled" /> -->
      <!-- <img title="项目详情" class="image" src="@/assets/images/button/projectDetailDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
  <div class="table" v-else-if="type === 'announce'">
    <el-button @click="clickHandler" :disabled="_disabled" v-bind="$attrs">
      <!-- <img title="公示" class="image" src="@/assets/images/button/announce.png" v-if="!disabled" /> -->
      <!-- <img title="公示" class="image" src="@/assets/images/button/announceDisabled.png" v-if="disabled" /> -->
      <slot></slot>
    </el-button>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";

const emit = defineEmits(["click"]);

const props = defineProps({
  // 类型
  type: {
    type: String,
    default: "default",
  },
  // 只读
  disabled: {
    type: Boolean,
    default: false,
  },
  // 权限key
  authKey: {
    type: String,
    default: "",
  },
});

// disabled
var _disabled = ref(false);

// watch disabled
watch(
  () => props.disabled,
  (newValue, oldValue) => {
    _disabled.value = newValue;
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  if (localStorage.getItem("syAuths")) {
    var auth = localStorage.getItem("syAuths").split(",");
    auth.map((item) => {
      if (props.authKey === item) {
        _disabled.value = true;
      }
    });
  }
});

// 按钮点击
function clickHandler(evt) {
  // 修复点击按钮，弹出dialog对话框后，按钮不恢复原来颜色的BUG
  let target = evt.target;
  if (target.nodeName == "SPAN") {
    target = evt.target.parentNode;
  }
  target.blur();
  emit("click", evt);
}
</script>

<style lang="scss" scoped>
.normal {
  width: auto;
  height: auto;

  .normal-button {
    color: #068324;
    border: 1px solid #068324;
    &:focus,
    &:hover {
      background: transparent;
    }
  }

  .primary-button {
    color: #ffffff;
    // background-color: #0b8df1;
    background: #068324;
    border: none;
  }

  .disable-button {
    color: #ffffff;
    background: #dedfe0;
  }

  .el-button.is-disabled,
  .el-button.is-disabled:focus,
  .el-button.is-disabled:hover {
    color: #ffffff;
    background: #dedfe0;
    border: none;
  }

  .el-button--normal {
    border-color: #068324;
    color: #068324;

    :deep(.el-icon) {
      color: #068324;
      font-size: 14px;
    }
  }

  .el-button--primary {
    :deep(.el-icon) {
      color: #ffffff;
      font-size: 14px;
    }
  }

  .el-button--small {
    --el-button-size: 28px;
  }
}

.normal + .normal {
  margin-left: 10px;
}

.nd-dropdown-box + .normal {
  margin-left: 10px;
}

.search {
  width: auto;
  height: auto;

  .el-button {
    width: auto;
    height: 28px;
    line-height: 28px;
    font-size: 14px;
    background-color: transparent;
    border: 0px solid transparent;
    color: #068324;
    cursor: pointer;
  }
}

.table {
  width: auto;
  height: auto;

  .el-button {
    padding: 0px;
    height: auto;
    line-height: normal;
    border: 0px solid transparent;
    background-color: transparent;
    color: #0B8DF1;

    // &:focus,
    // &:hover {
    //   color: #068324;
    // }
  }

  .is-disabled {
    &:focus,
    &:hover {
      color: #aaaaaa;
    }
  }

  .image {
    width: 14px;
    height: 14px;
  }
}
</style>
