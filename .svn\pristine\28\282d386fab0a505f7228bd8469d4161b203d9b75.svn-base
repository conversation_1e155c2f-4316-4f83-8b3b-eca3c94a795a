<template>
  <div class="nd-tabs-box">
    <div class="top-tab" :style="'justify-content:' + props.position + ';'">
      <template v-for="(item, index) in tabList" :key="index">
        <div :class="pageData.active == index ? 'tab-active' : ''" @click="tabChange(item, index)">
          {{ item }}
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive } from "vue"

const emit = defineEmits(["tabClick"])
let props = defineProps({
  tabList: {
    type: Array,
    default: () => {
      return []
    }
  },
  tabActive: {
    type: Number,
    default: 0,
  },
  position: {
    type: String,
    default: "center"
  }
})

onMounted(() => {
  pageData.active = props.tabActive;
  // emit("tabClick", { name: props.tabList[pageData.active], index: pageData.active })
})

let pageData = reactive({
  active: 0,
})

function tabChange(e, i) {
  pageData.active = i;
  emit("tabClick", { name: e, index: i })
}
function tabChange02() {
  pageData.active = props.tabActive;
  console.log(pageData.active, "pageData.active")
}
defineExpose({ tabChange02 })
</script>

<style lang='scss' scoped>
.nd-tabs-box {
  width: 100%;

  .top-tab {
    user-select: none;
    border-radius: 5px;
    width: 100%;
    height: 40px;
    border: 1px solid #eeeeee;
    background: #ffffff;
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 15px;

    > div {
      cursor: pointer;
      // width: 82px;
      padding: 0 13px;
      height: 100%;
      color: #444444;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .tab-active {
      position: relative;
      font-weight: 700;
      color: #068324;
      font-family: MicrosoftYaHei-Bold;
      font-size: 14px;
      transition: 0.3s;
    }

    @keyframes underLine {
      0% {
        width: 0;
      }

      100% {
        width: 100%;
      }
    }

    .tab-active::after {
      position: absolute;
      content: "";
      left: 50%;
      transform: translateX(-50%);
      bottom: 0;
      width: 100%;
      height: 2px;
      background-color: #068324;
      animation: underLine 0.3s;
    }
  }
}
</style>