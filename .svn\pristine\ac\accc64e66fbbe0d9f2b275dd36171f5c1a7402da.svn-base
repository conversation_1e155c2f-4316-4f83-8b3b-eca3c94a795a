<template>
  <nd-dialog ref="verifyRef" width="600px" height="230px" title="修改密码" align-center>
    <div class="main">
    <div class="main-box">
    <div class="verifyall">
      <div class="verifyall-from">
        <el-form :model="dataBox" label-width="80px" :inline="true" class="task-form" :rules="rules" ref="addFormRef">
          <el-form-item label="原密码" prop="oldPassword">
            <nd-input v-model="dataBox.oldPassword" type="password" show-password placeholder="请输入原密码" width="100%"
              maxlength="50"></nd-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <nd-input v-model="dataBox.newPassword" type="password" show-password placeholder="请输入新密码" width="100%"
              maxlength="50"></nd-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="repeatPassword">
            <nd-input v-model="dataBox.repeatPassword" type="password" show-password placeholder="请再次输入新密码" width="100%"
              maxlength="50"></nd-input>
          </el-form-item>
        </el-form>
        <div class="example" :class="dataBox.topFlag ? 'top10' : ''">
          密码长度为8~15位，需包括特殊字符（如%、@、&、#等），且需包含大写字母、小写字母、数字中的两种或以上，不允许有空格。（例：ZHNJ@8888）
        </div>
      </div>
    </div>
    </div>
    </div>
    <template #footer>
      <nd-button type="primary" icon="Check" @click="submitForm()"> &nbsp;&nbsp;确&nbsp;定 </nd-button>
      <nd-button type="" icon="Close" @click="close"> &nbsp;&nbsp;取&nbsp;消 </nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import CryptoJS from "crypto-js";

// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 获取路由
import { useRouter } from "vue-router";
const router_userRouter = useRouter();
// 定义axios
const $axios = inject("$axios");
const dataBox = reactive({
  account: "", //账号
  oldPassword: "", //源密码
  newPassword: "", //新密码
  repeatPassword: "", //确认密码
  topFlag: true, //距离位置
});

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const verifyRef = ref(null);

onMounted(() => {
  dataBox.account = localStorage.getItem("zhgsAccount");
});
// 打开弹窗 ==============================================================
const open = () => {
  verifyRef.value.open();
  dataBox.oldPassword = "";
  dataBox.newPassword = "";
  dataBox.repeatPassword = "";
};

const validatePass2 = (rule, value, callback) => {
  // console.log(rule,value,'************')
  if (!/^((?=.*[a-zA-Z])|(?=.*[A-Z0-9])|(?=.*[a-z0-9]))(?=.*[._~!@#$^&*])[A-Za-z0-9._~!@#$^&*]{8,15}$/g.test(value)) {
    callback(new Error("密码有误，请按要求格式设置"));
  } else if (value == dataBox.oldPassword) {
    callback(new Error("新密码不能与原密码相同"));
  } else {
    callback();
  }
};

const validatePass3 = (rule, value, callback) => {
  console.log(rule, value, "************");

  if (value != dataBox.newPassword) {
    callback(new Error("两次输入的密码不一致。"));
    dataBox.topFlag = false;
  } else {
    dataBox.topFlag = true;
    callback();
  }
};

const rules = reactive({
  oldPassword: [
    { required: true, message: "请输旧密码", trigger: "blur" },
    // {
    //   validator: validatePass1,
    //   trigger: 'blur'
    // },
  ],
  newPassword: [
    { required: true, message: "请输新密码", trigger: "blur" },
    {
      validator: validatePass2,
      trigger: "blur",
    },
  ],

  repeatPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: validatePass3,
      trigger: "blur",
    },
  ],
});

// 关闭弹窗 ==============================================================

const close = () => {
  verifyRef.value.close();
  dataBox.oldPassword = "";
  dataBox.newPassword = "";
  dataBox.repeatPassword = "";
};
// 确定 ==============================================================

const addFormRef = ref(null);
// 修改密码确定按钮
const submitForm = () => {
  addFormRef.value.validate((val) => {
    if (val) {
      //校验成功
      changePassword();
    } else {
      // 校验失败
      return false;
    }
    // console.log(val,'00000')
  });
  // console.log(res.validate,'00000')
};
function changePassword() {
  console.log(dataBox.oldPassword, dataBox.newPassword, dataBox.repeatPassword);
  let params = {
    // account: dataBox.account, //账号
    oldPwd: CryptoJS.MD5(CryptoJS.MD5(dataBox.oldPassword).toString()).toString(), //旧密码
    newPwd: CryptoJS.MD5(CryptoJS.MD5(dataBox.newPassword).toString()).toString(), //密码
    // repeatPassword: CryptoJS.MD5(CryptoJS.MD5(dataBox.repeatPassword).toString()).toString(), //确认密码
  };
  $axios({
    // url: "/auth/changePassword",
    url: "/index/modifyPwd",
    serverName: 'nd-base2',
    method: "post",
    data: params,
  }).then((res) => {
    if (res.data.code == 2000) {
      localStorage.setItem("syAuths", "");
      localStorage.setItem("syToken", "");
      // router_userRouter.push('loginView');
      router_userRouter.push("loginView");
    } else {
      ElMessage({
        message: res.data.message,
        type: "warning",
      });
    }
    console.log(res);
  });
}

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.verifyall {
  padding: 0 30px;

  :deep(.el-input .el-input__icon) {
    margin-right: 8px;
  }

  .top10 {
    transition: all 0.3s;
    margin-top: -10px;
  }

  .example {
    // border: 1px solid red;
    font-family: MicrosoftYaHei;
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    letter-spacing: 0px;
    color: #f56c6c;
    width: 457px;
    margin-left: 82px;
  }

  .title {
    padding: 10px 16px;
    background: #fcf6ec;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    span {
      margin-left: 13px;
      color: #ea9921;
      font-size: 14px;
      line-height: 24px;
    }
  }

  .verifyall-from {
    .task-form {
      .el-form-item {
        margin-right: 0;
        width: 100%;

        .nd-select-box,
        .nd-input-box,
        .el-row {
          width: 100% !important;
        }
      }

      .el-cascader {
        width: 100% !important;
      }
    }

    .change {
      color: #0098ff;
      position: relative;
      top: -2px;
      left: -15px;
      cursor: pointer;
    }
  }
}
.main{
  padding: 12px;
  height: 100%;
  .main-box{
height: 100%;
padding: 12px;
background: #fff;
border: 1px solid #ebeef5;
border-radius: 6px;
  }
}
</style>
