import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

export default defineConfig({
  base: "./",
  plugins: [vue()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  server: {
    port: 7777,
    proxy: {
      "/api1": {
        target: "https://www.ndscsoft.com/sy/mini", // Node服务地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api1/, ""),
      },
      "/api2": {
        target: "https://www.ndscsoft.com/scfw-mini", // Java服务地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api2/, ""),
      },
    },
  },
  build: {
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
