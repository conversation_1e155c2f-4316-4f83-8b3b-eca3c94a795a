<template>
  <div class="nd-tree-select-box" :style="{ width: width }">
    <el-tree-select ref="treeRef" :data="data.tree" :check-strictly="strictly" popper-class="nd-select-popper" multiple
      :props="treeProps" :teleported="teleported" value-key="id" :default-checked-keys="data.defaultCheckedKeys"
      :default-expanded-keys="data.defaultExpandedKeys" v-bind="$attrs" @node-click="handleNodeClick" :load="loadNode"
      lazy v-model="data.id">
      <slot></slot>
    </el-tree-select>
  </div>
</template>
<script setup>
import { ref, onMounted, inject, reactive, watch } from "vue";
// 导入ElMessage
import { ElMessage as elMessage } from "element-plus";
const $axios = inject("$axios");

const props = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px",
  },
  // 是否插入至body元素
  teleported: {
    type: Boolean,
    default: false,
  },
  //是否可以选中任意节点
  strictly: {
    type: Boolean,
    default: false,
  },
  // v-model绑定对象属性
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 限制范围
  limitLevel: {
    type: Number,
    default: 3,
  },
});

const emits = defineEmits(["update:modelValue"]); //定义暴露方法名
const treeRef = ref(null);

const treeProps = {
  label: "name",
  children: "children",
  isLeaf: "leaf",
};

let data = reactive({
  id: [],
  tree: [],
  defaultCheckedKeys: [],
  defaultExpandedKeys: [],
});

watch(
  () => data.id,
  (newVal, oldVal) => {
    emits("update:modelValue", newVal);
    // data.id = newVal;
    // debugger
  },
  {
    // deep: true,
    // immediate: true,
  }
);

watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    let newValStr = newVal.join(",");
    let dataIdStr = data.id.join(",");
    if (newValStr && newValStr != dataIdStr) {
      data.id = newVal;
      data.defaultCheckedKeys = newVal;
      emits("update:modelValue", newVal);
    }
    // debugger
  },
  {
    // deep: true,
    immediate: true,
  }
);

function loadNode(node, resolve) {
  // 非根节点（懒加载）
  let postId = node.data.id || ""
  $axios({
    url: "/common/areaTree",
    method: "get",
    data: {
      id: postId,
      benji: postId ? false : true,
      containDept: true,
      deptType: 2,
      limitLevel: props.limitLevel,
    },
    serverName: "nd-base2"
  })
    .then((res) => {
      if (res.data.code === 2000) {
        let resData = res.data.data || [];
        resData.map(item => {
          if (item.type != 2 && (item.children.length == 0 && item.leaf)) {
            item.disabled = true;
          }
          if (item.children && item.children.length > 0) item.leaf = false;
          return item;
        })
        let returnData = resData.filter(item => (item.type == 2 || (item.level <= props.limitLevel && item.type == 1))); // 只返回type为2的
        resolve(returnData);
      } else {
        elMessage.error(res.data.message);
        resolve([]);
      }
    })
    .catch(() => {
      resolve([]);
    });
}
function handleNodeClick(params) {

}
</script>

<style lang="scss" scoped>
.nd-tree-select-box {
  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-input.is-disabled .el-input__wrapper) {
    // background-color: #fafafa;
  }

  :deep(.el-select-dropdown__item.is-disabled) {
    color: var(--el-text-color-regular) !important;
  }

  :deep(.el-input.is-disabled .el-input__inner) {
    color: #444444;
    -webkit-text-fill-color: #444444;
    background-color: #fafafa;
  }
}
</style>
