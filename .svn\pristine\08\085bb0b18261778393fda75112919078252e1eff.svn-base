<template>
    <div class="commodity-information-view">
        <div class="title">
            <div class="left">
                <div class="line"></div>
                议价信息
            </div>

            <!-- <div class="right">
                <div class="total">原始总金额：<span>￥71,750</span></div>
                <div class="total">原始总金额：<span>￥71,750</span></div>
            </div> -->
        </div>

        <div class="main">
            <ndTable :data="detailData?.detailData?.changePriceVos || []">
                <el-table-column
                    header-align="center"
                    align="center"
                    label="出价时间"
                    prop="insertTime"
                />
                <el-table-column
                    header-align="center"
                    align="right"
                    label="出价（元/斤）"
                    prop="priceShow"
                >
                    <template #default="scope">
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;
                            "
                        >
                            <span
                                v-if="scope.row.type === 0"
                                style="
                                    width: 60px;
                                    height: 24px;
                                    padding: 6px;
                                    background: #fff3e5;
                                    border-radius: 2px;
                                    font-family: Microsoft YaHei;
                                    font-size: 12px;
                                    line-height: 12px;
                                    color: #ff8a00;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    margin-right: 10px;
                                "
                                >需求价格</span
                            >
                            <span>
                                {{ scope.row?.priceShow }}
                            </span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    header-align="center"
                    align="center"
                    label="出价方"
                >
                    <template v-slot="scope">
                        <span v-if="scope.row?.senderType === 1">养殖户</span>
                        <span v-if="scope.row?.senderType === 2">商户</span>
                    </template>
                </el-table-column>
                <el-table-column
                    header-align="center"
                    align="left"
                    label="商议内容"
                    prop="msg"
                />
            </ndTable>
        </div>
    </div>
</template>

<script setup>
// 自定义组件
import ndTable from "@/components/ndTable.vue";
import { inject } from "vue";

// inject
const detailData = inject("$purchaseRequestFormDetailData");
</script>

<style lang="scss" scoped>
.commodity-information-view {
    border: 1px solid #eaeaea;
    border-radius: 5px;
    background: #ffffff;
    padding: 12px;

    .title {
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;

        .left {
            display: flex;
            align-items: center;
            font-family: Microsoft YaHei;
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 0px;

            font-variation-settings: "opsz" auto;
            color: #444444;

            .line {
                width: 2px;
                height: 16px;
                background: #068324;
                margin-right: 6px;
            }
        }

        .right {
            display: flex;
            align-items: center;
            gap: 20px;

            .total {
                display: table-cell;
                vertical-align: bottom;
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                font-size: 14px;
                color: #303133;

                span {
                    font-family: Microsoft YaHei;
                    font-weight: 700;
                    font-size: 20px;
                    font-variation-settings: "opsz" auto;
                    color: #ff0001;
                }
            }
        }
    }

    .main {
    }
}
</style>
