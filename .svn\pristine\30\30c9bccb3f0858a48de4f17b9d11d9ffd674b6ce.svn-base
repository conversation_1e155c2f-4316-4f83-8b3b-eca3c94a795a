import { onMounted, reactive } from "vue";
import axios from "@/http/index";
import { method } from "lodash";
import { ElMessage } from "element-plus";

export function useTable({ detailRef = null }) {

    const tableParams = reactive({
        page: 1,
        size: 10,
        buyUserLike: "",
        demandCodeLike: "",
        acceptTime: [],
        acceptTimeStart: "",
        acceptTimeEnd: "",
        insertTime: [],
        insertTimeStart: "",
        insertTimeEnd: "",
        status: ""
    })

    const tableData = reactive({
        total: 0,
        list: [],
        loading: false
    })

    const STATUS_TEMPLATE = {
        0: {
            text: "待分配",
            bgc: "#FF8A00"
        },
        1: {
            text: "待接单",
            bgc: "#0B8DF1"
        },
        2: {
            text: "已接单",
            bgc: "#068324"
        },
        3: {
            text: "已取消",
            bgc: "#AAAAAA"
        }
    }

    /**
     * getTableData
     * 
     * @returns {void}
     */
    const getTableData = () => {

        console.log(tableParams.acceptTime);

        const params = {
            page: tableParams.page,
            size: tableParams.size,
            buyUserLike: tableParams.buyUserLike,
            demandCodeLike: tableParams.demandCodeLike,
            acceptTimeStart: tableParams.acceptTime ? tableParams.acceptTime[0] : "",
            acceptTimeEnd: tableParams.acceptTime ? tableParams.acceptTime[1] : "",
            insertTimeStart: tableParams.insertTime ? tableParams.insertTime[0] : "",
            insertTimeEnd: tableParams.insertTime ? tableParams.insertTime[1] : "",
        }

        if (tableParams.status !== "" || tableParams.status !== null || tableParams.status !== undefined) {
            params["status"] = tableParams.status;
        }

        tableData.loading = true;

        axios({
            url: "/buy/demand/page/findAll",
            serverName: "nd-base2",
            method: "GET",
            params,
        }).then(r => {

            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return;
            }

            tableData.total = r.data.data.total;

            tableData.list = r.data.data.records || [];

        }).finally(() => tableData.loading = false)
    }

    /**
     * handleSizeChange
     * 
     * @returns {void}
     */
    const handleSizeChange = (size) => {
        tableParams.page = 1;
        tableParams.size = size;
        getTableData();
    }

    /**
     * handleCurrentChange
     * 
     * @returns {void}
     */
    const handleCurrentChange = (current) => {
        tableParams.page = current;
        getTableData();
    }

    /**
     * reset
     * 
     * @returns {void}
     */
    const reset = () => {
        tableParams.page = 1;
        tableParams.size = 10;
        tableParams.buyUserLike = "";
        tableParams.demandCodeLike = "";
        tableParams.acceptTime = [];
        tableParams.acceptTimeStart = "";
        tableParams.acceptTimeEnd = "";
        tableParams.insertTime = [];
        tableParams.insertTimeStart = "";
        tableParams.insertTimeEnd = "";
        tableParams.status = "";
    }

    /**
     * 打开详情
     * 
     * @returns {void}
     */
    const openDetail = (row) => {
        detailRef && detailRef.value.open(row);
    }

    onMounted(() => {
        getTableData();
    })

    return {
        tableData,
        tableParams,
        STATUS_TEMPLATE,
        getTableData,
        handleSizeChange,
        handleCurrentChange,
        reset,
        openDetail
    }
}