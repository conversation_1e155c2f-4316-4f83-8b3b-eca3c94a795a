<template>
  <el-dialog v-model="dialog.visible" v-bind="$attrs" :width="width" :close-on-click-modal="false"
    :close-on-press-escape="false" :before-close="close" class="nd-dialog-box" :draggable="props.draggable"
    v-if="dialog.visible">
    <template #header="{ close, titleId, titleClass }">
      <div class="nd-dialog-header">
        <!-- <div class="fly-left"></div> -->
        <div class="title">{{ title }}<slot name="tip"></slot>
        </div>
        <!-- <div class="fly-right"></div> -->
      </div>
    </template>
    <div class="dialog-content-box" v-loading="loading">
      <el-scrollbar class="dialog-content" :style="{ height: height }">
        <slot></slot>
      </el-scrollbar>
    </div>
    <div v-if="showFooter" class="dialog-footer-box">
      <div class="dialog-footer">
        <slot name="footer"></slot>
      </div>
    </div>
    <div v-if="!showFooter" class="dialog-no-footer-box"></div>
  </el-dialog>
</template>
<script setup>
import { onMounted, ref, inject, reactive, nextTick } from 'vue'
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: "未命名",
  },
  // 是否显示底部按钮区域
  showFooter: {
    type: Boolean,
    default: true,
  },
  // 弹窗宽度
  width: {
    type: String,
    default: "50%",
  },
  // 弹窗高度
  height: {
    type: String,
    default: "auto",
  },
  // 是否可以拖拽
  draggable: {
    type: Boolean,
    default: true,
  },
  // 弹窗打开是否有loading效果
  loading: {
    type: Boolean,
    default: false,
  }
})
const dialog = reactive({
  visible: false,
  loading: false,
})
// 打开
const open = () => {
  dialog.visible = true;
}
// 关闭
const close = () => {
  dialog.visible = false;
}
// 显示等待
const showLoading = () => {
  this.loading = true;
}
// 隐藏等待
const hideLoading = () => {
  this.loading = false;
}
defineExpose({
  open,
  close
})
</script>
<style lang="scss">
.nd-dialog-box {
  // width: auto;
  // height: auto;
  padding: 16px 0;
  padding-bottom: 0;
  .el-dialog__header {
    // padding-top: 20px;
    padding-left: 5px;
    padding-right: 5px;
    // padding-bottom: 16px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    background-color: #ffffff;
    margin: 0px;
    border-bottom: 1px solid #E1EDE4;
  }

  .el-dialog__body {
    padding: 0px;
        .el-scrollbar__view{
        height: 100%;
    }
  }

  .el-dialog__footer {
    padding: 0px;
  }

  .el-dialog__headerbtn {
    top: 6px;
    right: 5px;

    &:hover {
      .el-dialog__close {
        color: #666666;
      }
    }
  }
  

  .el-dialog__close {
    color: #666666;
    font-size: 26px;
  }
}
</style>
<style lang="scss" scoped>
.nd-dialog-header {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  // justify-content: center;

  .fly-left {
    width: 150px;
    height: 10px;
    background: url('@/assets/images/mainView/flyLeft.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
    color: #068324;
    text-align: center;
    padding-left: 10px;
    padding-right: 10px;
  }

  .fly-right {
    width: 150px;
    height: 10px;
    background: url('@/assets/images/mainView/flyRight.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

.dialog-title-box {
  width: 100%;
  height: 35px;
  background-color: #f6faff;
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
  padding-left: 1px;
  padding-right: 1px;
  padding-top: 1px;

  .dialog-title {
    width: 100%;
    height: 100%;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    background-color: #0098ff;
    padding-left: 14px;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

.dialog-content-box {
  width: auto;
  height: auto;
  background-color: #f7f7f7;
  // padding: 16px;
  .dialog-content {
    width: auto;
    height: auto;
    background-color: #f7f7f7;
  }
}

.dialog-footer-box {
  width: 100%;
  height: 60px;
  background-color: #ffffff;
  border-top: 1px solid #E1EDE4;
border-radius: 0 0 4px 4px;

  .dialog-footer {
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
      border-radius: 0 0 4px 4px;
  }
}

.dialog-no-footer-box {
  width: 100%;
  height: 10px;
  background-color: #ffffff;
}

</style>
