import axios from 'axios';
import router from "@/router";
import CryptoJS from "crypto-js";

// 配置响应时间
// axios.defaults.timeout = 20000;

// 配置前后端数据交互的请求头：
// axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';

// 设置后台的访问地址
var url = {
  'nd-base1': window.ipConfig.base1,
  'nd-base2': window.ipConfig.base2,
}

// 获得请求参数
function getRequestParams() {
  let url = location.href;
  let requestParams = {};
  if (url.indexOf("?") !== -1) {
    let str = url.substring(url.indexOf("?") + 1);
    let strs = str.split("&");
    for (let i = 0; i < strs.length; i++) {
      requestParams[strs[i].split("=")[0]] = strs[i].split("=")[1];
    }
  }
  return requestParams;
}

// 添加请求拦截器
axios.interceptors.request.use(config => {
  // get请求拼接字符串
  if (config.method.toLowerCase() === "get") {
    var i = 0;
    for (var key in config.data) {
      if (i === 0) {
        config.url += "?" + key + "=" + encodeURIComponent(config.data[key]);
      } else {
        config.url += "&" + key + "=" + encodeURIComponent(config.data[key]);
      }
      i++;
    }
  }

  // 防止重复提交用
  if (config.data) {
    config.headers.repeat = CryptoJS.MD5(JSON.stringify(config.data)).toString();
  }

  // 拼接token
  let token = localStorage.getItem("syToken");
  if (token && token != undefined) {
    config.headers.token = token;
  }

  // 代理
  if (config.needServerPath === false) {
    // 不拼接地址前缀
  } else {
    // 衔接资金
    if (config.serverName == undefined || config.serverName === 'nd-base1') {
      if (process.env.NODE_ENV === 'development') {
        config.url = '/nd-base1' + config.url;
      } else {
        config.url = url['nd-base1'] + config.url;
      }
    }
    if (config.serverName === 'nd-base2') {
      if (process.env.NODE_ENV === 'development') {
        config.url = '/nd-base2' + config.url;
      } else {
        config.url = url['nd-base2'] + config.url;
      }
    }
  }

  return config;
}, err => {
  return Promise.reject(err);
});

// 返回状态判断(添加响应拦截器)
axios.interceptors.response.use((res) => {
  // 对响应数据做些事，自定义响应数据拦截器
  if (res.data.code === 4001) {
    // 页面过期，直接跳转登录页
    localStorage.setItem("syToken", "");
    router.push("/");
  } else {
    return res;
  }
  // return res;
}, (error) => {
  // if (error && error.response) {
  // 	switch (error.response.status) {
  // 		case 400: error.message = '请求错误(400)';
  // 			break;
  // 		case 401: error.message = '未授权，请重新登录(401)';
  // 			break;
  // 		case 403: error.message = '拒绝访问(403)';
  // 			break;
  // 		case 404: error.message = '请求出错(404)';
  // 			break;
  // 		case 408: error.message = '请求超时(408)';
  // 			break;
  // 		case 500: error.message = '服务器错误(500)';
  // 			break;
  // 		case 501: error.message = '服务未实现(501)';
  // 			break;
  // 		case 502: error.message = '网络错误(502)';
  // 			break;
  // 		case 503: error.message = '服务不可用(503)';
  // 			break;
  // 		case 504: error.message = '网络超时(504)';
  // 			break;
  // 		case 505: error.message = 'HTTP版本不受支持(505)';
  // 			break;
  // 		default: error.message = '连接出错' + (`${error.response.status}`);
  // 	}
  // } else {
  // 	error.message = '连接服务器失败!'
  // }
  return Promise.reject(error);
});

export default axios;