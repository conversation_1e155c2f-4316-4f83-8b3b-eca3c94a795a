<template>
  <nd-dialog ref="dialogRef" width="60vw" height="48vh" :title="page.title" align-center>
    <nd-table border style="height: 100%" :data="page.list.data">
      <el-table-column align="center" type="index" width="70px" label="序号"></el-table-column>
      <el-table-column align="center" label="规格" min-width="160px" prop="spData"></el-table-column>
      <el-table-column align="center" label="可售库存" min-width="120px" prop="diffStock"></el-table-column>
      <!-- label-class-name="star"（必填星号） -->
      <el-table-column align="center" label="库存增减" min-width="240px">
        <template #default="scope">
          <div style="display:flex;align-items:center;">
            <el-radio-group v-model="scope.row.addStockType" style="margin-right:20px" fill="#068324">
              <el-radio-button label="增" value="1" />
              <el-radio-button label="减" value="2" />
            </el-radio-group>
            <nd-input v-model.trim="scope.row.addStock" type2="number3" clearable width="100%" placeholder="请输入" />
          </div>
        </template>
      </el-table-column>
    </nd-table>
    <template #footer>
      <nd-button type="" @click="save">保存</nd-button>
      <!-- userType:1不允许编辑库存，userType:2不允许上下架，所以按钮只显示保存即可 -->
      <!-- <nd-button type="primary" @click="saveAndUp()">立即上架</nd-button>
      <nd-button type="" @click="saveAndDown()">暂先下架</nd-button> -->
      <nd-button type="" icon="Close" @click="close">取&nbsp;消</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入公共组件
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 导入element-plus
import { ElMessage as elMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义ref
const dialogRef = ref(null);
const addFormRef = ref(null);
let currentTab = ref(0);
// 定义page
const page = reactive({
  title: "",
  productId: "",
  list: {
    data: [],
  },
  userType: 2
});

// 打开弹窗
function open(params) {
  page.productId = params.productId;
  page.list.data = []
  page.title = "编辑库存";
  page.userType = localStorage.getItem("syUserType") || 2; // 用户类型 1:运营 2:供应商

  getStock();
  dialogRef.value.open();
}

// 获得详情
function getStock() {
  $axios({
    url: "/goods/getStock",
    method: "get",
    data: {
      id: page.productId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      res.data.data.map((item) => {
        item.addStock = "";
        item.addStockType = "1";
      });
      page.list.data = res.data.data;
      console.log("page.list.data", page.list.data);

    }
  });
}

// 关闭
const close = () => {
  dialogRef.value.close();
  // // 清空校验
  // if (addFormRef.value) {
  //   addFormRef.value.resetFields();
  // }
};

// 校验总库存数是否为空
function checkStockZero() {
  return new Promise(resolve => {
    let allStock = 0
    let errorMsg = ""
    page.list.data.forEach(ele => {
      if (errorMsg) return
      if (ele.diffStock >= 99999999 && ele.addStockType == 1 && ele.addStock > 0) return errorMsg = `规格“${ele.spData}”库存超出允许范围，无法补库存`
      allStock += Math.abs(ele.addStock)
    })
    if (errorMsg) {
      elMessage.error(errorMsg)
      resolve(false)
    } else if (allStock < 1) {
      elMessage.error('没有库存增减，无需保存！')
      resolve(false)
    } else {
      resolve(true)
    }
  })
}

// 立即上架
const saveAndUp = async () => {
  const checkRes = await checkStockZero()
  if (!checkRes) return
  $axios({
    url: "/goods/addStock",
    method: "post",
    data: {
      productId: page.productId,
      stock: page.list.data,
      up: 1
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      elMessage({
        message: "增加成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
};

const save = async () => {
  const checkRes = await checkStockZero()
  if (!checkRes) return
  $axios({
    url: "/goods/addStock",
    method: "post",
    data: {
      productId: page.productId,
      stock: page.list.data.map(item => {
        return {
          ...item,
          addStock: item.addStock * 1
        }
      }),
      up: 2
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      elMessage({
        message: "保存成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
      page.list.data.forEach((ele, index) => {
        if (ele.skuId === res.data.data[0].skuId) {
          page.list.data[index].diffStock = res.data.data[0].diffStock
        }
      })
    }
  });
}

// 暂先下架
const saveAndDown = async () => {
  const checkRes = await checkStockZero()
  if (!checkRes) return
  $axios({
    url: "/goods/addStock",
    method: "post",
    data: {
      productId: page.productId,
      stock: page.list.data,
      up: 0
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      elMessage({
        message: "增加成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
:deep(.star) {

  .el-form-item__label::before,
  .cell::before {
    content: "*";
    color: red;
    margin-right: 2px;
  }
}

.el-radio-group {
  flex-wrap: nowrap;
}

// :deep(.el-radio-button__inner:hover) {
//   color: #068324 !important;
// }

:deep(.el-radio-button__inner:not(.is-active .el-radio-button__inner):hover) {
  color: #068324 !important;
}
</style>
