import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, __dirname);
  return {
    base: "./",
    plugins: [vue()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
        "~@": path.resolve(__dirname, "src"),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
          silenceDeprecations: ['legacy-js-api']
        },
      },
    },
    server: {
      port: 7777,
      proxy: {
        "/nd-base1/": {
          target: "http://localhost:3000/sy/manage", // 开发环境
          // target: "http://*************:3000/sy/manage", // 测试环境
          // target: "https://www.wxssznj.cn/sy/manage", // 生产环境
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/nd-base1/, "/"),
        },
        "/nd-base2/": {
          target: "http://*************:8181/scfw-manage", // 开发环境
          // target: "http://*************:8181/scfw-manage", // 测试环境
          // target: "https://www.wxssznj.cn/scfw-manage", // 生产环境
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/nd-base2/, "/"),
        },
      },
    },
    build: {
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: process.env.NODE_ENV === 'production',
          drop_debugger: process.env.NODE_ENV === 'production'
        }
      },
      rollupOptions: {
        output: {
          // entryFileNames: `assets/[name].[hash:8].js`, // 自定义入口文件名的hash长度为8位
          // chunkFileNames: `assets/[name].[hash:8].js`, // 自定义chunk文件名的hash长度为8位
          // assetFileNames: `assets/[name].[hash:8].[ext]`, // 自定义静态资源文件名的hash长度为8位
          manualChunks(id) {
            // if (id.includes("style.css")) {
            //   return 'src/style.css';
            // }
            // if (id.includes("tasks.vue")) {
            //   return 'src/views/tasksView/tasks.vue';
            // }
            // 最小化拆分包
            if (id.includes("node_modules")) {
              return id
                .toString()
                .split("node_modules/")[1]
                .split("/")[0]
                .toString();
            }
          }
        }
      }
    },
  };
});
