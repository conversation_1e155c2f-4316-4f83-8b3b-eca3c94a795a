<template>
  <div class="nd-select-box" :style="{ width: width }">
    <el-select v-bind="$attrs" ref="select" popper-class="nd-select-popper" :teleported="teleported" :disabled="_disabled">
      <slot></slot>
    </el-select>
  </div>
</template>
<script setup>
import { ref, watch, onMounted } from "vue";

const props = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px",
  },
  // 是否插入至body元素
  teleported: {
    type: Boolean,
    default: true,
  },
  // 只读
  disabled: {
    type: Boolean,
    default: false,
  },
  // 权限key
  authKey: {
    type: String,
    default: "",
  },
});
// 系统级别disabled
var _disabled = ref(false);
// 监视disabled
watch(
  () => props.disabled,
  (newValue, oldValue) => {
    _disabled.value = newValue;
  },
  {
    immediate: true,
  }
);
onMounted(() => {
  if (localStorage.getItem("syAuths")) {
    var auth = localStorage.getItem("syAuths").split(",");
    auth.map((item) => {
      if ((props.authKey) === item) {
        _disabled.value = true;
      }
    });
  }
});

const select = ref(null);
function selectBlur() {
  select.value.blur();
}
defineExpose({
  selectBlur,
});
</script>
<style lang="scss">
.nd-select-popper {
  // width: auto !important;

  .el-select-dropdown__item {
    font-size: 14px;
  }

  .el-select-dropdown__empty {
    font-size: 14px;
  }
  .el-select-dropdown__item.selected,
  .el-select-dropdown__item.is-selected {
    color: #068324 !important;
  }
  .el-select-dropdown__wrap .el-select-dropdown__item.selected {
    color: #068324; /* 例如，将选中文字颜色改为红色 */
  }
  .el-select__wrapper.is-focused {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
}
.el-select {
  --el-select-input-focus-border-color: #068324;
}
</style>
<style lang="scss" scoped>
.nd-select-box {
  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-input__inner) {
    width: 100%;
    border-radius: 0px;
    font-size: 14px;
    padding-left: 2px;
    padding-right: 2px;
  }

  :deep(.el-input__icon) {
    line-height: 100%;
  }

  :deep(.el-select .el-input__wrapper) {
    //   height: 40px;
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
  // 去除选中时蓝色边框
  :deep(
      .el-input .el-input__wrapper.is-focus,
      .el-select__wrapper.is-focused,

    ) {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
}
:deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
</style>