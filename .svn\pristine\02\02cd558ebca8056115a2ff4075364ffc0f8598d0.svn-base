<template>
    <div
        class="nd-table-box"
        :class="{ 'my-empty': empty, 'my-border': !_border }"
    >
        <el-table
            ref="tableRef"
            @cell-mouse-enter="handleCellMouseEnter"
            @cell-mouse-leave="handleCellMouseLeave"
            :border="_border"
            :data="data"
            v-bind="attrs"
        >
            <template #empty>
                <div class="empty-image"></div>
                <div class="empty-data">{{ noValueText }}</div>
            </template>
            <slot></slot>
        </el-table>
    </div>
</template>

<script setup>
// import RowSpan from "./utils/row-span";
import { ref, reactive, useSlots, watch, useAttrs, nextTick } from "vue";

// props
const props = defineProps({
    data: {
        type: Array,
        default: () => [],
    },
    noValueText: {
        type: String,
        default: "暂无数据",
    },
    border: {
        type: Boolean,
        default: true,
    },
    // 是否启用rowspan
    rowSpan: {
        type: Boolean,
        default: false,
    },
    // 分组依据的列的id
    rowSpanId: {
        type: String,
        default: "",
    },
});

// useAttrs
const attrs = useAttrs();

// useSlots
const slots = useSlots();

// data
const tableRef = ref(null);
const empty = ref(false);
const _border = ref(false);

// watch
watch(
    () => props.data,
    (newVal, oldVal) => {
        if (newVal.length === 0) {
            empty.value = true;
        } else {
            empty.value = false;
        }
    },
    { deep: true }
);
watch(
    () => props.border,
    (newVal, oldVal) => {
        _border.value = newVal;
    },
    {
        immediate: true,
    }
);

// 判断是不是多级表头
var firstSlots = slots.default();
firstSlots.map((item) => {
    try {
        var secondSlots = item.children.default();
        secondSlots.map((item2) => {
            if (item2.type.name === "ElTableColumn") {
                _border.value = true;
            }
        });
    } catch (error) {
        // 异常不抛出
    }
});

// 鼠标移入
function handleCellMouseEnter(row, column, rowIndex, columnIndex) {
    if (props.rowSpan) {
        nextTick(() => {
            // 获取鼠标移入时的tbody结点
            const tbody = document.querySelector(
                ".el-table__body-wrapper table tbody"
            );
            // 循环获取tr结点
            for (let i = 0; i < tbody.children.length; i++) {
                const tr = tbody.children[i];
                // 逻辑判断，这步已经获取到了tr所以tableData[i]与tr是一致的
                if (props.data[i][props.rowSpanId] == row[props.rowSpanId]) {
                    // 改变tr的背景颜色
                    tr.style.background = "#F0FEF5";
                    // 循环获取td,改变td的样式
                    for (let j = 0; j < tr.children.length; j++) {
                        const td = tr.children[j];
                        td.style.background = "#F0FEF5";
                    }
                } else {
                    tr.style.background = "#ffffff";
                    for (let j = 0; j < tr.children.length; j++) {
                        const td = tr.children[j];
                        td.style.background = "#ffffff";
                    }
                }
            }
        });
    }
}

function handleCellMouseLeave(params) {
    if (props.rowSpan) {
        // 获取鼠标移入时的tbody结点
        const tbody = document.querySelector(
            ".el-table__body-wrapper table tbody"
        );
        // 循环获取tr结点
        for (let i = 0; i < tbody.children.length; i++) {
            const tr = tbody.children[i];
            tr.style.background = "#ffffff";
            for (let j = 0; j < tr.children.length; j++) {
                const td = tr.children[j];
                td.style.background = "#ffffff";
            }
        }
    }
}

function clearSelection() {
    return tableRef.value.clearSelection();
}

function toggleRowSelection(row, checked) {
    return tableRef.value.toggleRowSelection(row, checked);
}

function toggleRowExpansion(row, checked) {
    return tableRef.value.toggleRowExpansion(row, checked);
}

function getSelectionRows() {
    return tableRef.value.getSelectionRows();
}

defineExpose({
    clearSelection,
    toggleRowSelection,
    getSelectionRows,
    toggleRowExpansion,
});
</script>

<style lang="scss" scoped>
.my-border {
    :deep(.el-table) {
        border-top: 1px solid #e4e7ed;
        border-left: 1px solid #e4e7ed;
        border-right: 1px solid #e4e7ed;
    }
}

.my-empty {
    :deep(.el-table__body-wrapper) {
        z-index: 2;
    }
}

.nd-table-box {
    width: auto;
    height: auto;

    :deep(.el-table th.el-table__cell) {
        background-color: #f9f9f9; // 头部背景色
        padding: 0px;
        height: 30px;
        font-size: 14px;
        color: #333333;
    }

    :deep(.el-table) {
        font-size: 12px;
        color: #333333;
        font-weight: normal;
        .el-scrollbar__view {
            height: 100%;
        }
    }

    :deep(.el-table__empty-text) {
        width: auto;
        height: 80%;
        line-height: normal;
        margin-top: 20px;
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;

        .empty-image {
            width: 153px;
            height: 61px;
            background-image: url("@/assets/images/nodata.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center center;
        }

        .empty-data {
            margin-top: 10px;
        }
    }

    :deep(.el-table thead) {
        color: #3f4457;
    }

    :deep(.el-table .cell) {
        font-weight: normal;
    }

    :deep(.el-table .el-table__cell) {
        padding: 6px 0;
    }

    :deep(.el-table tr) {
        height: 38px;
    }

    :deep(.el-table__body tr:not(.current-row):not(.select-row):hover > td) {
        background-color: #e8f7ff;
    }

    :deep(.el-table__body .el-table__row.hover-row:not(.select-row) td) {
        background-color: #e8f7ff;
    }

    :deep(.el-table__body tr.current-row > td) {
        background-color: #c8ecff;
    }

    :deep(.el-table__body tr.hover-row > td.el-table__cell) {
        background-color: #c8ecff;
    }

    :deep(.el-table__body tr.select-row > td) {
        background-color: #c8ecff;
    }

    :deep(.el-table__expand-icon::before) {
        background: url("../assets/images/arrowRight.png") no-repeat;
        content: "";
        display: block;
        width: 12px;
        height: 12px;
        background-size: 100% 100%;
    }

    :deep(.el-table__expand-icon > .el-icon) {
        display: none;
    }
    :deep(.el-table__body tr:hover > td) {
        background-color: #f0fef5 !important;
        /* 自定义悬停背景色 */
    }
    :deep(.el-checkbox) {
        .el-checkbox__inner:hover {
            border-color: #068324;
        }
        .is-indeterminate {
            .el-checkbox__inner {
                background-color: #068324 !important;
                border-color: #068324;
            }
        }
        .is-checked {
            .el-checkbox__inner {
                background-color: #068324 !important;
                border-color: #068324;
            }
        }
    }
}

// 列表部分字体显示14px 列表黑色文字是#555
:deep(.el-table) {
    tbody {
        td {
            font-size: 14px;
            color: #555;
        }
    }
}
</style>
