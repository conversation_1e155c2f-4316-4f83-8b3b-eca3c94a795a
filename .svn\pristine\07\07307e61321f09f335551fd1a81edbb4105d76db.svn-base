<template>
  <ndb-page-list>
    <template #button>
      <nd-button @click="openAdd()" type="primary" icon="plus">新建</nd-button>
    </template>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="标题">
          <nd-input
            v-model.trim="page.searchData.title"
            placeholder="请输入标题"
          />
        </nd-search-more-item>
        <nd-search-more-item title="简介">
          <nd-input
            v-model.trim="page.searchData.summary"
            placeholder="请输入简介"
          />
        </nd-search-more-item>
        <nd-search-more-item title="公开">
          <nd-select v-model="page.searchData.publiced">
            <el-option label="全部" :value="3" />
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="更新日期">
          <nd-date-picker
            v-model="page.searchData.cycleArr"
            range-separator="至"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择"
          ></nd-date-picker>
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="getTableData"> 查询 </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <nd-table style="height: 100%" :data="page.list.data">
        <el-table-column type="index" label="序号" align="center" width="80" />
        <el-table-column
          align="center"
          label="标题"
          prop="title"
          show-overflow-tooltip
          width="180"
        >
          <template v-slot="scope">
            <div
              @click="openDetail(scope.row)"
              style="
                color: #409eff;
                cursor: pointer;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              "
            >
              {{ scope.row.title }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="简介"
          prop="summary"
          header-align="center"
          min-width="220"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          label="排序"
          prop="sort"
          width="120px"
        ></el-table-column>
        <el-table-column align="center" label="公开" prop="publiced"  width="120px">
          <template v-slot="scope">
            <span v-if="scope.row.publiced == 1">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column
          align="right"
          label="点赞量"
          prop="likeCount"
          header-align="center"
          width="120px"
        ></el-table-column>
        <el-table-column
          align="right"
          label="收藏量"
          prop="favoriteCount"
          header-align="center"
          width="120px"
        ></el-table-column>
        <el-table-column
          align="right"
          label="浏览量"
          prop="viewCount"
          header-align="center"
          width="120px"
        ></el-table-column>
        <el-table-column
          align="center"
          label="创建人"
          prop="userName"
          min-width="120px"
        ></el-table-column>
        <el-table-column
          align="center"
          label="更新时间"
          prop="updateTime"
          width="180px"
        ></el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="120px"
        >
          <template #default="scoped">
            <div style="display: flex; justify-content: space-around">
              <nd-button type="edit" @click="openEdit(scoped.row)"
                >编辑</nd-button
              >
              <nd-button type="delete" @click="deleteRow(scoped.row.klgId)"
                >删除</nd-button
              >
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination
        :current-page="page.pager.pageIndex"
        :page-size="page.pager.pageSize"
        :total="page.pager.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </ndb-page-list>
  <add-dialog ref="addDialogRef" @before-close="getTableData"></add-dialog>
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";

// 导入子组件
import addDialog from "./components/addDialog.vue";

// 导入vue
import { reactive, ref, onMounted, inject, computed, nextTick } from "vue";
// 定义axios
const $axios = inject("$axios");

// 导入element-plus
import { ElMessageBox, ElMessage } from "element-plus";

// 定义page
const page = reactive({
  searchData: {
    title: "",
    summary: "",
    publiced: 3,
    cycleArr: ["", ""],
  },

  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  },
});
onMounted(() => {
  getTableData();
});

const getTableData = () => {
  let params = {
    title: page.searchData.title,
    summary: page.searchData.summary,
    publiced: page.searchData.publiced == 3 ? "" : page.searchData.publiced,
    startTime: page.searchData.cycleArr[0],
    endTime: page.searchData.cycleArr[1],
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
  };
  $axios({
    url: "/information/index",
    method: "get",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code == 2000) {
      page.list.data = res.data.data.records;
      page.pager.total = res.data.data.total;
      page.pager.pageIndex = res.data.data.current;
      page.pager.pageSize = res.data.data.size;
    } else {
      ElMessage.error(res.data.message);
    }
  });
};

const addDialogRef = ref(null);
//新增
function openAdd() {
  addDialogRef.value.open("add", "");
}
//编辑
function openEdit(row) {
  addDialogRef.value.open("edit", row);
}
//详情
function openDetail(row) {
  addDialogRef.value.open("detail", row);
}
const deleteRow = (id) => {
  ElMessageBox.confirm("确定删除？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    confirmButtonClass: "ExitConfirmButton",
    cancelButtonClass: "ExitCancelButton",
    customClass: "ExitCustomClass",
  })
    .then(() => {
      $axios({
        url: `/information/delete/${id}`,
        method: "post",
        serverName: "nd-base2",
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success("删除成功");
          getTableData();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
    .catch(() => {
      console.log("取消删除");
    });
};

// 重置
function reset() {
  // 重置所有查询字段
  page.searchData.title = "";
  page.searchData.summary = "";
  page.searchData.publiced = 3;
  page.searchData.cycleArr = ["", ""];
  getTableData();
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}
</script>

<style lang="scss" scoped>
.workhour {
  color: #444444;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;

  .workhour-item-line {
    width: 4px;
    height: 15px;
    border-radius: 3px;
    background-color: red;
    margin-right: 12px;
  }

  .workhour-item {
    margin-right: 23px;

    .red {
      color: red;
    }

    .blue {
      color: #10a3e7;
    }
  }
}
:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<style lang="css">
.el-popper {
  max-width: 350px ;
}
</style>
