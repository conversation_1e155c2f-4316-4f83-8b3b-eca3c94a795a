<template>
  <ndDialog ref="uploadDialogRef" width="800px" height="420px" title="上传附件" append-to-body :before-close="close" align-center>
            <div class="main">
            <div class="main-box">
    <el-upload class="upload-demo" :drag="!prop.readonly" action="#" multiple :auto-upload="false" :on-change="fileSelected" :http-request="pcUpload">
      <div class="el-upload__text" v-if="!prop.readonly">
        <p class="mar">根据需要拖动附件至本区域上传或点击下方按钮进行操作</p>
        <p class="mar1" style="color: #ff7e7e">{{ prop.tip1 }}</p>
        <p class="mar1" style="color: #ff7e7e">{{ prop.tip2 }}</p>
        <div class="button">
          <nd-button>本地上传</nd-button>
        </div>
      </div>

      <template #tip>
        <div class="imgList" v-loading="loading">
          <div class="imgLi" v-for="(item, index) in prop.files" :key="index" @mouseover="mouseoverHandle(index)" @mouseleave="mouseleaveHandle()">
            <div style="display: flex; align-items: center">
              <div style="padding-right: 10px">{{ index + 1 }}、</div>
              <!-- <div v-if="item.suffix == 'pdf' || item.suffix == 'PDF'" class="file-img" @click="donLod(item.filePath)">
                <img src="@/assets/pdf.png" alt="" />
              </div>
              <div v-else-if="item.suffix == 'zip' || item.suffix == 'ZIP' || item.suffix == 'rar' || item.suffix == 'RAR'" class="file-img" @click="donLod(item.filePath)">
                <img src="@/assets/images/zip.png" alt="" />
              </div>
              <div
                v-else-if="item.suffix == 'txt' || item.suffix == 'TXT' || item.suffix == 'doc' || item.suffix == 'DOC' || item.suffix == 'docx' || item.suffix == 'DOCX'"
                class="file-img"
                @click="donLod(item.filePath)"
              >
                <img src="@/assets/images/doc.png" alt="" />
              </div>
              <div v-else-if="item.suffix == 'xls' || item.suffix == 'XLS' || item.suffix == 'xlsx' || item.suffix == 'XLSX'" class="file-img" @click="donLod(item.filePath)">
                <img src="@/assets/images/xls.png" alt="" />
              </div>
              <div v-else-if="item.suffix == 'ppt' || item.suffix == 'PPT' || item.suffix == 'pptx' || item.suffix == 'PPTX'" class="file-img" @click="donLod(item.filePath)">
                <img src="@/assets/images/ppt.png" alt="" />
              </div> -->
              <el-image
                v-if="item.suffix === 'image/jpeg' || item.suffix === 'image/png' || item.suffix === 'image/bmp'"
                class="file-img"
                style="width: 50px; height: 50px"
                :src="item.fullPath"
                :preview-src-list="previewFiles"
                :initial-index="index"
                fit="cover"
              />
              <div v-if="item.suffix === 'application/pdf'" class="file-img" @click="previewPDf(item.fullPath)">
                <img src="@/assets/pdf.png" alt="" />
              </div>
              <div style="margin-left: 10px">
                {{ item.curName }}
              </div>
            </div>
            <div class="option" v-if="index === data.index">
              <template v-if="!prop.readonly">
                <p class="operation" @click="downLoad(item)">下载</p>
                <p class="operation" @click="openChangeOrderDialog(index)">调整顺序</p>
                <template v-if="prop.hasDelete">
                  <p @click="deleteImg(item, index)" class="operation">删除</p>
                </template>
              </template>
            </div>
          </div>
        </div>
      </template>
    </el-upload>
            </div>
      </div>
    <template #footer>
      <ndButton @click="close" icon="Close">关&nbsp;闭</ndButton>
    </template>
    <change-order ref="changeOrderRef" :files="prop.files"></change-order>
  </ndDialog>
</template>

<script setup>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import changeOrder from "./changeOrder.vue";
import { ElMessage as elMessage } from "element-plus";
import { ref, inject, reactive, computed, onMounted } from "vue";

const $axios = inject("$axios");

const loading = ref();
const changeOrderRef = ref(null);
const setTimeoutObject = ref(null);
const uploadDialogRef = ref(null);

const data = reactive({
  fileList: [],
  imgList: [],
  index: null,
});

const prop = defineProps({
  // 文件
  files: {
    type: Array,
    default: () => [],
  },
  // 附件归属
  fzgs: {
    type: String,
    default: "",
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false,
  },
  // 是否允许删除
  hasDelete: {
    type: Boolean,
    default: true,
  },
  // 限制的数量
  limit: {
    type: Number,
    default: 999999,
  },
  // tip1
  tip1: {
    type: String,
    default: "建议尺寸：800*800像素，最多上传15张；",
  },
  // tip2
  tip2: {
    type: String,
    default: "支持上传文件格式为jpeg、jpg、png、bmp。单个附件大小限制15M。",
  },
  // 支持的文件类型
  mime: {
    type: String,
    default: "jpg,jpeg,png,bmp",
  },
});

// 计算属性 - 预览
const previewFiles = computed(() => {
  return prop.files.map((item) => item.fullPath);
});

// 打开
const open = () => {
  uploadDialogRef.value.open();
};

// 关闭
const close = () => {
  uploadDialogRef.value.close();
};

// 删除图片
const deleteImg = (item, index) => {
  $axios({
    url: "/file/delete",
    method: "post",
    data: {
      fzgs: item.fzgs,
      name: item.name,
    },
  }).then((r) => {
    if (r.data.code === 2000) {
      prop.files.splice(index, 1);
      elMessage({
        message: "删除成功",
        type: "success",
      });
    } else {
      elMessage.error(r.data.message);
    }
  });
};

// 文件选择
const fileSelected = (file) => {
  if (data.fileList.length + prop.files.length + 1 > prop.limit) {
    return elMessage.error(`最多支持上传${prop.limit}张，超出部分上传失败！`);
  }

  loading.value = true;
  // 获取上传文件大小
  let imgSize = Number(file.size / 1024 / 1024);
  //获取文件格式
  let fileTypeLen = file.raw.type.split("/");
  let fileType = fileTypeLen[fileTypeLen.length - 1].toLowerCase(); //转化成小写

  if (prop.mime.split(",").includes(fileType)) {
    if (imgSize > 15) {
      elMessage({
        message: "单个附件最大不得超过15M",
        type: "warning",
      });
      data.fileList = [];
      clearTimeout(setTimeoutObject.value);
      //关闭加载动画
      loading.value = false;
      return;
    }
  } else {
    elMessage({
      message: "暂不支持该格式，无法上传！",
      type: "warning",
    });
    loading.value = false;
    return;
  }
  clearTimeout(setTimeoutObject.value);
  // 构建数组
  data.fileList.push(file);
  setTimeoutObject.value = setTimeout(() => {
    pcUpload();
  }, 1000);
};

// 文件上传
const pcUpload = () => {
  let params = new FormData();
  data.fileList.map((item) => {
    params.append("files", item.raw);
  });
  params.append("fzgs", prop.fzgs);
  $axios({
    url: "/file/upload",
    method: "post",
    data: params,
  }).then((res) => {
    loading.value = false;
    res.data.data.filePaths.map((item) => {
      prop.files.push(item);
    });
    data.fileList = [];
  });
};

// 鼠标悬停
const mouseoverHandle = (index) => {
  data.index = index;
};

// 鼠标离开
const mouseleaveHandle = (index) => {
  data.index = null;
};

//下载附件
const downLoad = (item) => {
  const a = document.createElement("a"); //创建a标签
  a.style.display = "none";
  a.href = item.fullPath; // 指定下载链接
  a.setAttribute("download", item.curName);
  //a.download = this.filename; //指定下载文件名
  a.click(); //触发下载
  URL.revokeObjectURL(a.href); //释放URL对象

  // $axios({
  //   url: "/file/download",
  //   method: "get",
  //   data: {

  //   },
  // }).then((res) => {

  // });
};

// 打开调整顺序对话框
function openChangeOrderDialog(index) {
  changeOrderRef.value.open(index);
}

// 预览PDF
function previewPDf(url) {
  window.open(url);
}

// 方法导出，便于父组件接收并且调用
defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.operation {
  color: #0098ff;
  cursor: pointer;
  display: inline-block;
  margin-right: 8px;
}

.upload-demo {
  .el-upload__text {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .mar {
      font-size: 18px;
      color: #555;
      margin-bottom: 8px;
    }

    .mar1 {
      font-size: 14px;
      color: #777;
      margin-bottom: 8px;
    }

    .button {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 10px;
    }
  }

  :deep(.el-upload-list--text) {
    display: none;
  }

  .imgList {
    padding: 5px 5px;

    .imgLi {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 5px 0;
      cursor: pointer;
      padding-left: 10px;

      img {
        height: 50px;
        width: 50px;
      }

      .rename {
        display: flex;
        align-items: center;
      }

      .option {
        display: flex;
        align-items: center;
      }
    }
  }
}

:deep(.el-upload-dragger) {
  margin: 0 20px;
  padding: 40px;
}

:deep(.el-upload-dragger:hover) {
  border-color: #068324;
}

.main{
  padding: 12px;
  height: 100%;
  .main-box{
    padding: 12px;
    background: #fff;
    border-radius: 6px;
        border: 1px solid #ebeef5;
    height: 100%;
  }
}
</style>
