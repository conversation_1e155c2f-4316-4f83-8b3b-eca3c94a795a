<template>
  <div class="menu-first-box" :class="menu.collapse ? 'menu-box-width-60' : 'menu-box-width-200'">
    <div class="menu-top-box">
      <!-- <div class="logo-box">
        <div class="logo" v-show="!menu.collapse"></div>
        <div class="collapse" @click="collapse" title="收起/展开"></div>
      </div> -->
      <div class="sy-name">水产平台后台管理系统</div>
      <div class="portrait-box" v-show="!menu.collapse">
        <div class="portrait"></div>
      </div>
      <div v-show="!menu.collapse" class="name-box">
        <div class="name" :title="user.name">{{ user.name }}</div>
        <!-- <div class="point"></div>
        <div class="title" :title="user.title">{{ user.title }}</div> -->
      </div>
      <!-- <div class="logout-box">
        <el-icon class="change-password" @click="openChangePassword" title="修改密码">
          <Unlock />
        </el-icon>
        <el-icon class="logout" @click="logout" title="退出系统">
          <SwitchButton />
        </el-icon>
      </div> -->
    </div>
    <el-scrollbar class="menu-data-box">
      <el-menu class="menu-data" :default-active="menu.defaultActive" @select="handleSelect"
        :collapse-transition="false" :collapse="menu.collapse">
        <template v-for="(item, index) in menu.data">
          <el-menu-item :index="item.id">
            <el-icon size="16">
              <!-- <component :is="icons[item.id]"></component> -->
              <component :is="item.icon"></component>
            </el-icon>
            <template #title>{{ item.name }}</template>
          </el-menu-item>
        </template>
      </el-menu>
    </el-scrollbar>
    <div class="cheer-img">
      <img src="@/assets/images/mainView/cheer.png" alt="">
    </div>
  </div>
  <change-password ref="changePasswordRef"></change-password>
  <force-change-password ref="forceChangePasswordRef"></force-change-password>
</template>

<script setup>
// 导入公共组件
import changePassword from "./changePassword.vue"; // 修改密码
import forceChangePassword from "./forceChangePassword.vue"; // 强制修改密码

// 导入vue
import { ElMessageBox, ElMessageBox as elMessageBox } from "element-plus";
import { ref, reactive, onMounted, watch, nextTick, inject } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage as elMessage } from "element-plus";

// 路由
const $route = useRoute();
const $router = useRouter();

// axios
const $axios = inject("$axios");

// emit
const emit = defineEmits(["menu-click"]);

// ref
const forceChangePasswordRef = ref(null);
const changePasswordRef = ref(null);

// 图标对象
let icons = {
  "10001": "Money", // 订单管理
  "10002": "Collection", // 售后管理
  "10003": "DataAnalysis", // 发票管理
  "10004": "PieChart", // 商品管理
  "10005": "Tickets", // 结算中心
  "10006": "User", // 养殖户管理
  "10007": "Memo", // 客服中心
  "10008": "Suitcase", // 机构管理
  "10009": "Setting", // 系统管理
};

// 用户对象
var user = reactive({
  name: "", // 名字
  title: "", // 职位
  token: "", // 用户token
});

// 菜单对象
var menu = reactive({
  data: [], // 原始菜单数据
  defaultActive: "10001", // 高亮的菜单
  collapse: false, // 折叠
});

// watch
watch(
  () => $router.currentRoute.value.fullPath,
  (newVal, oldVal) => {
    highlightMenu();
    localStorage.setItem("syJumpMenuId", menu.defaultActive);
    // // 如果是其他页面跳转而来，等全部改完后，这段if即可废除，只留else
    // if ($route.query.from) {
    //   // 高亮菜单
    //   var id = getMenuIdByPath($route.query.to);
    //   menu.defaultActive = "";
    //   nextTick(() => {
    //     menu.defaultActive = id;
    //   });
    // } else {
    //   // 高亮菜单
    //   var id = getMenuIdByPath($route.name);
    //   if (id === "未知") {
    //     // 什么也不做
    //   } else {
    //     menu.defaultActive = "";
    //     nextTick(() => {
    //       menu.defaultActive = id;
    //     });
    //   }
    // }
  },
  {
    immediate: false,
  }
);

// mounted
onMounted(() => {
  // 初始化用户
  user.name = localStorage.getItem("syUserName");
  user.title = localStorage.getItem("syDeptName");
  user.token = localStorage.getItem("syToken");
  // 获得菜单数据
  getMenuData();
  // 高亮菜单
  highlightMenu();
  // 模拟点击高亮菜单
  clickHighlightMenu();
});

// 打开修改密码对话框
const openChangePassword = () => {
  changePasswordRef.value.open();
};

// 展开/收起菜单
function collapse() {
  menu.collapse = !menu.collapse;
}

// 获得菜单数据
function getMenuData() {
  menu.data = JSON.parse(localStorage.getItem("syMenus"));
}

// 高亮菜单
function highlightMenu() {
  // 去掉路由后面的参数
  var routerWithoutQuery = $router.currentRoute.value.fullPath.split("?")[0];

  var menuData = JSON.parse(localStorage.getItem("syMenus"));
  var node = treeFind(menuData, (item) => {
    return "/" + item.url === routerWithoutQuery;
  });
  var id = "";
  if (node.parentId !== "0") {
    var parentId = node.parentId;
    node = treeFind(menuData, (item) => {
      return item.id === parentId;
    });
  }
  else {
    id = node.id;
  }
  if (node.parentId !== "0") {
    var parentId = node.parentId;
    node = treeFind(menuData, (item) => {
      return item.id === parentId;
    });
  }
  else {
    id = node.id;
  }
  if (node.parentId !== "0") {
    var parentId = node.parentId;
    node = treeFind(menuData, (item) => {
      return item.id === parentId;
    });
  }
  else {
    id = node.id;
  }
  if (node.parentId !== "0") {
    var parentId = node.parentId;
    node = treeFind(menuData, (item) => {
      return item.id === parentId;
    });
  }
  else {
    id = node.id;
  }
  menu.defaultActive = id;
}

// 模拟点击高亮菜单
function clickHighlightMenu() {
  emit("menu-click", { id: menu.defaultActive });
}

// 菜单选中
const handleSelect = (key, keyPath) => {
  emit("menu-click", { id: key });
};

// 退出系统
function logout() {
  elMessageBox
    .confirm("确定退出系统?", "退出系统", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      $axios({
        url: "/index/logout",
        method: "post",
        serverName: 'nd-base2',
        data: {},
      }).then((res) => {
        if (res.data.code === 2000) {
          localStorage.setItem("syToken", "");
          localStorage.setItem("syUserName", "");
          localStorage.setItem("syDeptName", "");
          localStorage.setItem("syMenus", "");
          $router.push("loginView");
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    })
    .catch(() => { });
}

// 查找树节点
function treeFind(tree, func) {
  for (const data of tree) {
    if (func(data)) return data;
    if (data.child && data.child.length > 0) {
      const res = treeFind(data.child, func);
      if (res) return res;
    }
  }
  return null;
}
</script>

<style lang="scss" scoped>
.menu-first-box {
  width: auto;
  height: 100%;
  display: flex;
  flex-direction: column;

  .menu-top-box {
    width: 100%;
    height: auto;

    // padding-bottom: 10px;
    // background-color: salmon;
    .sy-name {
      width: 100%;
      text-align: center;
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: bold;
      line-height: normal;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #068324;
      padding: 10px 0 18px 0;
    }

    .logo-box {
      width: 100%;
      height: 50px;
      padding-top: 15px;
      padding-bottom: 15px;
      padding-left: 23px;
      padding-right: 23px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .logo {
        width: 130px;
        height: 20px;
        background: url("@/assets/images/mainView/logo.png") no-repeat center center;
        background-size: 100% 100%;
      }

      .collapse {
        width: 14px;
        height: 14px;
        background: url("@/assets/images/mainView/hamburgermenu.png") no-repeat center center;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .portrait-box {
      width: 100%;
      height: 70px;
      // padding-top: 15px;
      padding-bottom: 10px;
      padding-left: 10px;
      padding-right: 10px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .portrait {
        width: 60px;
        height: 60px;
        background: url("@/assets/images/mainView/portrait.png") no-repeat center center;
        background-size: 100% 100%;
      }
    }

    .name-box {
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: 0px;
      color: #333333;
      padding-left: 8px;
      padding-right: 8px;
      margin-bottom: 26px;

      .name {
        // max-width: 98px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        font-weight: bold;
        color: #333333;
      }

      .point {
        width: 4px;
        height: 4px;
        background-color: #333333;
        border-radius: 2px;
        margin-left: 5px;
        margin-right: 5px;
      }

      .title {
        // max-width: 56px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .iconstyle {
        padding-top: 5px;
        padding-left: 6px;
      }
    }

    .change-box {
      z-index: 999;
      background-color: #333333;
      border-radius: 5px;
      width: 180px;
      margin-left: 10px;
      height: auto;
      width: auto;
      position: absolute;

      .change-name-box {
        width: 100%;
        height: 36px;

        .change-name {
          width: 100%;
          height: 30px;
          display: flex;
          justify-content: flex-start;
          padding-left: 10px;
          padding-right: 10px;
          align-items: center;
          font-size: 14px;
          font-weight: normal;
          letter-spacing: 0px;
          color: #555;

          .checked {
            padding-left: 10px;
            color: #0b8df1;
          }
        }
      }
    }

    .logout-box {
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;

      .change-password {
        color: #333333;
        cursor: pointer;
        // margin-left: 10px;

        &:hover {
          font-size: 20px;
        }
      }

      .logout {
        color: #333333;
        cursor: pointer;
        // margin-right: 10px;

        &:hover {
          font-size: 20px;
        }
      }
    }
  }

  .menu-data-box {
    width: 100%;
    min-width: 60px;
    height: 0px;
    flex: 1;

    :deep(.el-scrollbar) {
      --el-scrollbar-opacity: 1;
      --el-scrollbar-bg-color: #50adf5;
    }

    .menu-data {
      width: auto;
      height: auto;
      background: none;
      border-right: 0px;

      .second-nemu {
        padding-left: 10px;
      }

      .menu-image {
        width: 18px;
        height: 18px;
        background: url("@/assets/images/mainView/collection.png") no-repeat center center;
        background-size: 100% 100%;
        margin-left: 4px;
        margin-right: 12px;
      }

      :deep(.el-menu) {
        background-color: transparent;
        border-right: 0px;
      }

      :deep(.el-menu-item) {
        font-family: MicrosoftYaHeiUI;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0px;
        color: #333333;
      }

      :deep(.el-sub-menu__title) {
        font-family: MicrosoftYaHeiUI;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0px;
        color: #333333;
      }

      :deep(.el-menu-item:hover) {
        background: linear-gradient(284deg, #F1FFF5 -31%, #DBFCE3 99%);
      }

      :deep(.el-menu-item.is-active) {
        background: linear-gradient(284deg, #F1FFF5 -31%, #DBFCE3 99%);
        // color: #fffb00;
      }

      :deep(.el-sub-menu__title:hover) {
        background: linear-gradient(284deg, #F1FFF5 -31%, #DBFCE3 99%);
      }

      :deep(.el-menu .el-menu-item) {
        background: linear-gradient(284deg, #F1FFF5 -31%, #DBFCE3 99%);
      }

      :deep(.el-menu .el-menu-item:hover) {
        background: linear-gradient(284deg, #F1FFF5 -31%, #DBFCE3 99%);
      }

      :deep(.el-menu .el-sub-menu) {
        background: linear-gradient(284deg, #F1FFF5 -31%, #DBFCE3 99%);
      }
    }
  }

  .cheer-img {
    width: 100%;
    padding-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 174px;
      height: 170px;
    }
  }
}

.menu-box-width-200 {
  width: 200px;
  transition: 0.4s width;
}

.menu-box-width-60 {
  width: 60px;
  transition: 0.4s width;
}
</style>
