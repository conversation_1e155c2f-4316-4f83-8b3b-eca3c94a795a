<template>
  <ndb-page-list>
    <template #button>
      <nd-button @click="openAddDialog" type="primary" icon="plus" authKey=""
        >新增</nd-button
      >
      <nd-button @click="batchDelete" icon="delete" authKey="">删除</nd-button>
    </template>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="数据字典名称">
          <nd-input
            v-model.trim="page.search.accountName"
            placeholder="请输入数据字典名称"
            clearable
            style="width: 100%"
          />
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="getTableData" authKey="">
            查询
          </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <nd-table
        ref="treeTableRef"
        style="height: 100%"
        :data="page.list.data"
        row-key="dictId"
        lazy
        :load="load"
        :tree-props="{
          children: 'children',
          checkStrictly: true,
          hasChildren: 'checkXj',
        }"
        @selection-change="handleSelectionChange"
        :key="tableKey"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column
          prop="dictValue"
          label="数据字典名称"
          align="left"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="dictKey"
          label="数据字典标识"
          align="center"
          width="150"
        ></el-table-column>
        <el-table-column
          prop="dictName"
          label="数据类型名称"
          align="left"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="dictMark"
          label="数据类型标识"
          align="left"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="describes"
          label="描述"
          align="left"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="orderCode"
          label="排序"
          align="center"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <div class="Btn-box">
              <nd-button
                type="edit"
                @click="openEditDialog(scope.row)"
                v-if="scope.row.dictPid != 0"
                >编辑</nd-button
              >
              <!-- <nd-button type="delete" @click="delRow(scope.row)"
                >删除</nd-button
              > -->
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination
        v-model:current-page="pager.page"
        v-model:page-size="pager.size"
        :total="pager.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </ndb-page-list>

  <add-dialog ref="detailDialogRef" @before-close="updateTableData" />
</template>

<script setup>
import { reactive, ref, onMounted, nextTick } from "vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import addDialog from "./components/addDialog.vue";
import { ElMessage, ElMessageBox } from "element-plus";

import axios from "axios";

const detailDialogRef = ref(null);
const treeTableRef = ref(null);

const page = reactive({
  search: {
    accountName: "",
    pid: "",
  },
  list: {
    data: [],
  },
});

// 页码
const pager = reactive({
  page: 1,
  size: 10,
  total: 0,
});

onMounted(() => {
  getTableData();
});
//刷新
const tableKey = ref(0);
// 获取数据列表
const getTableData = async () => {
  axios({
    url: "/dict/findList",
    method: "get",
    params: {
      page: pager.page,
      size: pager.size,
      dictNameLike: page.search.accountName,
    },
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.records;
      pager.total = res.data.data.total;
      pager.page = res.data.data.current;
      pager.size = res.data.data.size;
      tableKey.value += 1;
      selectedPoints.value = [];
    } else {
      ElMessage.error(res.data.message);
    }
  });
};

// 懒加载
const load = async (row, treeNode, resolve) => {
  try {
    const children = await fetchChildren(row.dictId);
    resolve(children);
    nextTick(() => {
      treeTableRef.value?.store?.setTreeExpansion(row, true);
    });
  } catch (error) {
    resolve([]);
    ElMessage.error("子节点加载失败");
  }
};
const fetchChildren = (parentId) => {
  return new Promise((resolve) => {
    axios({
      url: `/dict/findChildes/${parentId}`,
      method: "get",
      serverName: "nd-base2",
    }).then((res) => {
      if (res.data.code === 2000) {
        resolve(res.data.data);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  });
};

const updateTableData = async () => {
  await getTableData();

  // 新增：重置所有节点的展开状态
  if (treeTableRef.value?.store) {
    const expandedKeys = Object.keys(
      treeTableRef.value.store.states.expandedRows
    );
    expandedKeys.forEach((key) => {
      treeTableRef.value.store.states.expandedRows[key] = false;
    });
  }
};

// 选中框
const selectedPoints = ref([]);
function handleSelectionChange(val) {
  selectedPoints.value = val;
}

const isChecked = ref(false); // 是否选中
const openAddDialog = () => {
  if (selectedPoints.value.length > 1) {
    return ElMessage.error("选中数据不可超过1条！");
  } else if (selectedPoints.value.length === 1) {
    isChecked.value = true;
    page.search.pid = selectedPoints.value[0]?.dictId;
  } else {
    isChecked.value = false;
  }
  detailDialogRef.value.open(
    "add",
    isChecked.value,
    "",
    selectedPoints.value[0]?.dictId,
    selectedPoints.value[0]?.dictName,
    selectedPoints.value[0]?.dictMark,
    selectedPoints.value[0]?.dictValue
  );
};

// 打开编辑对话框
const openEditDialog = (row) => {
  detailDialogRef.value.open("edit", isChecked.value, row);
};

// 批量删除
const batchDelete = async () => {
  // 检查是否有选中项
  if (selectedPoints.value.length === 0) {
    ElMessage.warning("请勾选列表中要操作的信息！");
    return;
  }
  // 检查是否有子项
  const hasChildItems = selectedPoints.value.some((item) => item.checkXj);
  if (hasChildItems) {
    ElMessage.error("有子项的字典不允许删除");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `是否确定删除这${selectedPoints.value.length}条数据吗？删除不可恢复，请确认！`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    const idsToDelete = selectedPoints.value.map((item) => item.dictId);
    const parentIds = new Set(selectedPoints.value.map((item) => item.dictPid));

    const response = await axios({
      url: "/dict/deleteAll",
      method: "post",
      data: { idList: idsToDelete },
      serverName: "nd-base2",
    });

    if (response.data.code === 2000) {
      ElMessage.success(response.data.message);
      getTableData();
      if (parentIds.has(0)) {
        treeTableRef.value?.doLayout?.();
      }
    } else {
      ElMessage.error(response.data.message);
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除操作失败");
    } else {
      ElMessage.info("已取消删除");
    }
  }
};

// 删除
function delRow(val) {
  if (val.checkXj) {
    ElMessage.error("有子项的字典不允许删除");
    return;
  }
  ElMessageBox.confirm(
    `是否确定删除该条数据吗？删除不可恢复，请确认！`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      axios({
        url: "/dict/delete?id=" + val.dictId,
        method: "post",
        serverName: "nd-base2",
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success(res.data.message);
          getTableData();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
    .catch(() => {
      ElMessage.info("已取消删除");
      selectedPoints.value = [];
    });
}

function reset() {
  (page.search.accountName = ""), getTableData();
}

// 分页
function handleSizeChange(size) {
  pager.size = size;
  pager.page = 1;
  getTableData();
}

function handleCurrentChange(page) {
  pager.page = page;
  getTableData();
}
</script>

<style lang="scss" scoped>
:deep(.Btn-box) {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 5px 20px;

  .is-disabled {
    color: #aaaaaa ;
  }
}
:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<style lang="css"> 
.el-popper {
  max-width: 300px ;
}
 </style>