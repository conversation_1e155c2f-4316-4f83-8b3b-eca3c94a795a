<template>
  <nd-dialog
    ref="dialogRef"
    width="60vw"
    :title="page.title"
    :before-close="close"
  >
    <div class="box">
      <div class="main">
        <el-form
          :model="page.formData"
          :rules="rulesData"
          label-width="120px"
          ref="formRef"
          :class="{ 'detail-mode': page.type === 'detail' }"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="账户名称" prop="nickname">
                <template v-if="page.type !== 'detail'">
                  <nd-input
                    v-model.trim="page.formData.nickname"
                    placeholder="请输入账户名称"
                    maxlength="10"
                    clearable
                    style="width: 100%"
                  />
                </template>
                <template v-else>
                  {{ page.formData.nickname }}
                </template>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="账号" prop="account">
                <template v-if="page.type !== 'detail'">
                  <nd-input
                    v-model.trim="page.formData.account"
                    placeholder="请输入账号"
                    maxlength="20"
                    clearable
                    :disabled="page.type === 'edit'"
                    style="width: 100%"
                  />
                </template>
                <template v-else>
                  {{ page.formData.account }}
                </template>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="姓名" prop="realname">
                <template v-if="page.type !== 'detail'">
                  <nd-input
                    v-model.trim="page.formData.realname"
                    placeholder="请输入姓名"
                    maxlength="10"
                    clearable
                    style="width: 100%"
                  />
                </template>
                <template v-else>
                  {{ page.formData.realname }}
                </template>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="联系电话" prop="lxdh">
                <template v-if="page.type !== 'detail'">
                  <nd-input
                    v-model.trim="page.formData.lxdh"
                    placeholder="请输入联系电话"
                    clearable
                    style="width: 100%"
                    maxlength="11"
                    type2="number8"
                  />
                </template>
                <template v-else>
                  <div
                    v-if="page.type === 'detail'"
                    style="display: flex; align-items: center"
                  >
                    <div>
                      {{ showPhone ? decryptedPhone : page.formData.lxdh }}
                    </div>
                    <div style="margin-left: 10px" @click="clickPhone">
                      <img
                        style="width: 14px; height: 14px; cursor: pointer"
                        :src="showPhone ? openEyeImg : closeEyeImg"
                      />
                    </div>
                  </div>
                </template>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="身份证号" prop="idCard">
                <template v-if="page.type !== 'detail'">
                  <nd-input
                    v-model.trim="page.formData.idCard"
                    maxlength="18"
                    placeholder="请输入身份证号"
                    clearable
                    style="width: 100%"
                  />
                </template>
                <template v-else>
                  <div
                    v-if="page.type === 'detail'"
                    style="display: flex; align-items: center"
                  >
                    <div>
                      {{ showIdCard ? decryptedIdCard : page.formData.idCard }}
                    </div>
                    <div style="margin-left: 10px" @click="clickIdCard">
                      <img
                        style="width: 14px; height: 14px; cursor: pointer"
                        :src="showIdCard ? openEyeImg : closeEyeImg"
                      />
                    </div>
                  </div>
                </template>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <ndRadioGroup
                  :model-value="page.formData.status"
                  @input.stop="onStatusInput"
                  :disabled="page.type === 'detail'"
                >
                  <ndRadio :value="1">正常</ndRadio>
                  <ndRadio :value="0">禁用</ndRadio>
                </ndRadioGroup>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="联系地址" prop="address" label-width="115px">
                <div
                  style="display: flex; width: 100%;position: relative;"
                  v-if="page.type !== 'detail'"
                >
                <el-tooltip
                  content="该地址为商户下单后的默认收货地址"
                  placement="top"
                >
                  <el-icon style="cursor: pointer;position: absolute; bottom: 30px;left: -12px;">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
                  <nd-input
                    v-model="page.formData.address"
                    placeholder="请输入联系地址"
                    maxlength="100"
                    clearable
                    type="textarea"
                    style="width: 100%;margin-left: 3px;"
                  />
                </div>
                <template v-else>
                  {{ page.formData.address }}
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="table-header">
          <div class="table-title">协议附件</div>
          <nd-button
            v-if="page.type !== 'detail'"
            type="primary"
            @click="openAgreement"
            >新增协议</nd-button
          >
        </div>
        <nd-table :data="page.agreeMentList" style="margin-top: 10px">
          <el-table-column
            show-overflow-tooltip
            type="index"
            label="序号"
            width="80"
            fixed="left"
          />
          <el-table-column
            show-overflow-tooltip
            prop="pactName"
            label="协议名称"
            min-width="120"
          />
          <el-table-column
            show-overflow-tooltip
            prop="beginDate"
            label="协议开始日期"
            align="center"
            min-width="150"
          />
          <el-table-column
            show-overflow-tooltip
            prop="endDate"
            label="协议结束日期"
            align="center"
            min-width="150"
          />
          <el-table-column
            show-overflow-tooltip
            prop="attachment"
            label="附件"
            min-width="80"
            align="center"
          >
            <template #default="scope">
              <div @click="previewFile(scope.row)" class="link-text">
                {{ scope.row.fileList.length }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="状态"
            min-width="80"
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.status === 0">未开始</span>
              <span v-else-if="scope.row.status === 1">正常</span>
              <span v-else-if="scope.row.status === 2">到期</span>
            </template>
          </el-table-column>
          <!-- 添加操作列 -->
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="120"
            v-if="page.type !== 'detail'"
          >
            <template #default="scope">
              <div style="display: flex; justify-content: space-around;">
                <nd-button type="edit" @click="editAgreement(scope.row)"
                  >编辑</nd-button
                >
                <nd-button type="edit" @click="deleteArgeement(scope.row)"
                  >删除</nd-button
                >
              </div>
            </template>
          </el-table-column>
        </nd-table>
      </div>
    </div>
    <template #footer>
      <template v-if="page.type !== 'detail'">
        <nd-button type="primary" @click="submit">提交</nd-button>
        <nd-button @click="close">取消</nd-button>
      </template>
      <template v-else>
        <nd-button @click="close">关闭</nd-button>
      </template>
    </template>
  </nd-dialog>
  <!-- 引入新增协议弹窗组件 -->
  <agreeMent ref="agreementRef" @refresh="getArgeementList" />
  <previewAgree ref="previewDialogRef" />
</template>

<script setup>
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndTable from "@/components/ndTable.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import agreeMent from "./agreeMent.vue"; //新增协议
import previewAgree from "./previewAgree.vue"; //预览附件
import openEyeImg from "@/assets/images/openEye.png";
import closeEyeImg from "@/assets/images/closeEye.png";

import { ref, reactive, inject } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");

// emit
const emits = defineEmits(["refreshData"]);

// ref
const dialogRef = ref(null);
const formRef = ref(null);
const agreementRef = ref(null);
const previewDialogRef = ref(null);

//数据
const page = reactive({
  type: "",
  title: "新增",
  memberId: "",
  formData: {
    nickname: "",
    account: "",
    realname: "",
    lxdh: "",
    lxdhJm: "",
    idCard: "",
    idCardJm: "",
    status: 1,
    address: "",
  },
  agreeMentList: [],
});

//打开
const open = (type, id) => {
  resetData();
  page.type = type;
  page.memberId = id;
  if (type == "add") {
    page.title = "新增";
    getMemberId();
  } else if (type == "edit") {
    page.title = "编辑";
    getDetail(id);
    getArgeementList();
  } else if (type == "detail") {
    page.title = "详情";
    getDetail(id);
    getArgeementList();
  }
  dialogRef.value.open();
};

//检验
const rulesData = {
  nickname: [{ required: true, message: "账户名称不能为空", trigger: "blur" }],
  account: [{ required: true, message: "账号不能为空", trigger: "blur" }],
  realname: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
  lxdh: [
    { required: true, message: "联系电话不能为空", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "手机号格式不正确",
      trigger: ["blur", "change"],
    },
  ],
  idCard: [
    { required: true, message: "身份证号不能为空", trigger: "blur" },
    {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X)$)/,
      message: "请输入正确的身份证号",
      trigger: ["blur", "change"],
    },
  ],
  address: [{ required: true, message: "联系地址不能为空", trigger: "blur" }],
  status: [{ required: true, message: "状态不能为空", trigger: "change" }],
};

//id
const getMemberId = async () => {
  $axios({
    url: "/common/getNewId",
    method: "GET",
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code !== 2000) {
      ElMessage.error(res.data.message);
    } else {
      page.memberId = res.data.data;
    }
  });
};

//提交
const submit = async () => {
  const isValid = await formRef.value.validate();
  if (isValid) {
    const data = {
      memberId: page.memberId,
      nickname: page.formData.nickname,
      account: page.formData.account,
      realname: page.formData.realname,
      lxdh: page.formData.lxdh,
      idCard: page.formData.idCard,
      status: page.formData.status,
      address: page.formData.address,
    };

    const url = page.type === "add" ? "/merchant/save" : "/merchant/update";
    const message = await $axios({
      url,
      method: "POST",
      serverName: "nd-base2",
      data,
    });

    if (message.data.code !== 2000) {
      ElMessage.error(message.data.message);
    } else {
      ElMessage.success(message.data.message);
      close();
      emits("refreshData");
    }
  }
};

//获取协议列表
const getArgeementList = () => {
  const params = {
    memberId: page.memberId,
  };

  $axios({
    url: "/merchant/getPactList",
    method: "GET",
    serverName: "nd-base2",
    params,
  })
    .then((res) => {
      if (res.data.code !== 2000) {
        ElMessage.error(res.data.message);
        return;
      }
      res.data.data?.forEach((item) => {
        item.fileList?.forEach((item2) => {
          item2.sourcePath = window.ipConfig.fileUrl + item2.sourcePath;
          item2.fullPath = item2.sourcePath;
        });
      });
      page.agreeMentList = Array.isArray(res.data.data) ? res.data.data : [];
    })
    .catch(() => {});
};

//打开协议
const openAgreement = () => {
  agreementRef.value.open(page.memberId, "add", "");
};
//编辑协议
const editAgreement = (row) => {
  agreementRef.value.open(page.memberId, "edit", row.pactId);
};

//预览附件
const previewFile = (row) => {
  previewDialogRef.value.open(row.fileList);
};
//删除协议
const deleteArgeement = (row) => {
  ElMessageBox.confirm("确定删除该协议吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    confirmButtonClass: "ExitConfirmButton",
    cancelButtonClass: "ExitCancelButton",
    customClass: "ExitCustomClass",
  })
    .then(() => {
      const data = {
        pactId: row.pactId,
      };
      $axios({
        url: "/merchant/delPact",
        method: "POST",
        serverName: "nd-base2",
        data,
      })
        .then((res) => {
          if (res.data.code !== 2000) {
            ElMessage.error(res.data.message);
          } else {
            ElMessage.success(res.data.message);
            getArgeementList();
          }
        })
        .catch(() => {});
    })
    .catch(() => {});
};

const showPhone = ref(false);
const showIdCard = ref(false);
const decryptedPhone = ref("");
const decryptedIdCard = ref("");

// 切换显示状态
const clickPhone = async () => {
  if (!showPhone.value && !decryptedPhone.value) {
    decryptedPhone.value = (await decryptData(page.formData.lxdhJm)) || "--";
  }
  showPhone.value = !showPhone.value;
};

const clickIdCard = async () => {
  if (!showIdCard.value && !decryptedIdCard.value) {
    decryptedIdCard.value = (await decryptData(page.formData.idCardJm)) || "--";
  }
  showIdCard.value = !showIdCard.value;
};

//详情
const getDetail = (id) => {
  const params = {
    memberId: id,
  };
  $axios({
    url: "/merchant/findDetails",
    method: "GET",
    serverName: "nd-base2",
    params,
  })
    .then((res) => {
      if (res.data.code !== 2000) {
        ElMessage.error(res.data.message);
        return;
      }
      page.formData.nickname = res.data.data?.nickname || "--";
      page.formData.account = res.data.data?.account || "--";
      page.formData.realname = res.data.data?.realname || "--";
      page.formData.status = res.data.data?.status;
      page.formData.address = res.data.data?.address || "";
      if (page.type === "edit") {
        decryptData(res.data.data?.lxdhJm).then((res) => {
          page.formData.lxdh = res || "--";
        });
        decryptData(res.data.data?.idCardJm).then((res) => {
          page.formData.idCard = res || "--";
        });
      } else {
        showPhone.value = false;
        showIdCard.value = false;
        decryptedPhone.value = "";
        decryptedIdCard.value = "";
        page.formData.lxdh = res.data.data?.lxdh || "--";
        page.formData.lxdhJm = res.data.data?.lxdhJm || "--";
        page.formData.idCard = res.data.data?.idCard || "--";
        page.formData.idCardJm = res.data.data?.idCardJm || "--";
      }
    })
    .catch(() => {});
};

//解密
const decryptData = async (content) => {
  const data = {
    content,
  };
  return $axios({
    url: "/common/getPlaintext",
    method: "POST",
    serverName: "nd-base2",
    data,
  }).then((res) => {
    if (res.data.code !== 2000) {
      ElMessage.error(res.data.message);
      return "";
    }
    return res.data.data;
  });
};

//状态
const onStatusInput = () => {
  if (page.formData.status === 1) {
    ElMessageBox.confirm("账号禁用后将无法登录小程序，确定禁用？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: "ExitConfirmButton",
      cancelButtonClass: "ExitCancelButton",
      customClass: "ExitCustomClass",
    })
      .then(async () => {
        page.formData.status = 0;
      })
      .catch(() => {});
  } else {
    page.formData.status = 1;
  }
};

//重置
const resetData = () => {
  page.memberId = "";
  page.formData.nickname = "";
  page.formData.account = "";
  page.formData.realname = "";
  page.formData.lxdh = "";
  page.formData.lxdhJm = "";
  page.formData.idCard = "";
  page.formData.idCardJm = "";
  page.formData.status = 1;
  page.formData.address = "";
  page.agreeMentList = [];
  showPhone.value = false;
  showIdCard.value = false;
  decryptedPhone.value = "";
  decryptedIdCard.value = "";
};

//关闭
const close = () => {
  resetData();
  dialogRef.value.close();
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.link-text {
  cursor: pointer;
  color: #409eff;
}

.box {
  width: 100%;
  height: 100%;
  padding: 12px;

  .main {
    background: #ffffff;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    padding: 12px;
  }
}

:deep(.nd-radio-box .el-radio) {
  height: 100%;
}
:deep(.detail-mode .el-form-item.is-required .el-form-item__label::before) {
  content: "";
  margin-right: 0;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<style lang="scss">
.ExitConfirmButton {
  background: #068324 !important;
  border-color: #068324 !important;

  &:hover {
    opacity: 0.8;
  }
}

.ExitCancelButton {
  // background: #068324 !important;
  // border-color: #068324 !important;

  &:hover {
    background-color: rgba(133, 224, 154, 0.2) !important;
    color: #38864a !important;
    border-color: #38864a !important;
  }
}
.ExitCustomClass {
  .el-message-box__headerbtn:hover {
    .el-message-box__close {
      color: #068324;
    }
  }
}
</style>
