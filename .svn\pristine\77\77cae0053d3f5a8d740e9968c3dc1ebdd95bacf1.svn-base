<template>
  <div class="login-box">
    <div class="main-box">
      <div class="top-box">
        <system-name-flipping></system-name-flipping>
      </div>
      <div class="login-box">
        <div class="left-box">
          <img class="house2" src="@/assets/images/loginView/house.png" alt="" />
        </div>
        <div class="right-box">
          <div class="title-login">用户登录</div>
          <div class="input">
            <img src="@/assets/images/loginView/iconicon.png" alt="" />
            <input type="text" placeholder="请输入用户名" v-model="page.admin.id" />
          </div>
          <div class="input">
            <img src="@/assets/images/loginView/locklock.png" alt="" />
            <input :type="page.admin.password.type" placeholder="请输入密码" v-model="page.admin.password.value" />
            <img @click="togglePassWord()" v-if="page.admin.password.show" class="viewview" src="@/assets/images/loginView/hidehide.png" alt="" />
            <img @click="togglePassWord()" v-if="!page.admin.password.show" class="viewview" src="@/assets/images/loginView/viewview.png" alt="" />
          </div>
          <div @click="login" class="login">登&nbsp;&nbsp;录</div>
          <img class="cheer" src="@/assets/images/loginView/cheer.png" alt="" />
        </div>
      </div>
      <div class="bottom-box">
        <!-- <div class="copyright">版权所有：南京南大尚诚软件科技有限公司</div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
// 导入子组件
import systemNameFlipping from "./components/systemNameFlipping.vue";
// 导入菜单数据
import { menuData } from "./menuData.js";

import { ref, inject, onMounted, reactive, onBeforeUnmount } from "vue";
import { ElMessage as elMessage } from "element-plus";
import { useRouter } from "vue-router";
import CryptoJS from "crypto-js";

const $axios = inject("$axios");
const $router = useRouter();

const page = reactive({
  admin: {
    id: "", // 用户名
    password: {
      value: "",
      show: false,
      type: "password",
    }, // 密码
  },
});

onMounted(() => {
  window.addEventListener("keydown", onkeydown);
});

onBeforeUnmount(() => {
  window.removeEventListener("keydown", onkeydown);
});

// 回车登录
const onkeydown = (e) => {
  if (e.key === "Enter") {
    login();
  }
};

// 密码显示/隐藏
function togglePassWord() {
  if (page.admin.password.show) {
    page.admin.password.type = "password";
  } else {
    page.admin.password.type = "text";
  }
  page.admin.password.show = !page.admin.password.show;
}

// 登录
async function login() {
  // 验证
  const success = await validate();

  // 验证不通过
  if (!success) {
    return false;
  }

  // 获得菜单
  getMenu();

  // 获得按钮权限
  getButtonAuthority();
}

// 验证
async function validate() {
  return new Promise((resolve, reject) => {
    if (page.admin.id === "") {
      elMessage({
        message: "请输入用户名",
        type: "warning",
      });
      return resolve(false);
    }

    if (page.admin.password.value === "") {
      elMessage({
        message: "请输入密码",
        type: "warning",
      });
      return resolve(false);
    }

    let encryption = CryptoJS.MD5(CryptoJS.MD5(page.admin.password.value).toString()).toString();

    $axios({
      url: "/auth/login",
      method: "post",
      serverName: "nd-base2",
      data: {
        account: page.admin.id,
        password: encryption,
      },
    }).then((res) => {
      if (res.data.code === 2000) {
        localStorage.setItem("syToken", res.data.data.token);
        localStorage.setItem("syUserName", res.data.data.userName);
        localStorage.setItem("syDeptName", res.data.data.deptName);
        localStorage.setItem("syUserType", res.data.data.userType); // 1:运营 2:供应商
        localStorage.setItem("syAreaLevel", "none");
        return resolve(true);
      } else {
        elMessage({
          message: res.data.message,
          type: "warning",
        });
        return resolve(false);
      }
    });
  });
}

// 加工菜单数据
function formatMenu(menuArr) {
  menuArr = menuArr.filter((item) => (item.parentId == 0 && item.children && item.children.length > 0) || item.parentId > 0);
  return menuArr.map((item) => {
    const obj = {
      id: item.data.menuId,
      url: item.data.menuUrl ? item.data.menuUrl : "welcomeView",
      parentId: item.data.menuPid,
      name: item.data.menuName,
      icon: item.data.iconUrl,
      show: true,
      child: item.children && item.children.length > 0 ? formatMenu(item.children) : [],
    };
    return obj;
  });
}

// 获得菜单
function getMenu() {
  let dataList = [];
  $axios({
    url: "/menu/findMenuTree",
    method: "get",
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      // 加工菜单数据
      dataList = formatMenu(res.data.data);

      // 菜单记录到localStorage
      localStorage.setItem("syMenus", JSON.stringify(dataList));

      if (dataList.length < 1) {
        $router.push("welcomeView");
      } else {
        // 跳转到第一个有权限的菜单
        var firstMenu = findFirstLeaf(dataList);
        if (firstMenu) {
          $router.push(firstMenu.url);
        } else {
          $router.push("welcomeView");
        }
      }
    }
  });
}

// 查找第一个叶子节点
function findFirstLeaf(tree) {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].child.length === 0) {
      return tree[i]; // 找到第一个叶子节点，返回它
    } else {
      const result = findFirstLeaf(tree[i].child); // 递归查找子节点
      if (result) return result; // 如果找到了叶子节点，返回它
    }
  }
  return null; // 如果没有找到叶子节点，返回null
}

// 获得按钮权限
function getButtonAuthority() {
  localStorage.setItem("syAuths", "");
}
</script>

<style lang="scss" scoped>
.login-box {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 1200px;
  background-image: url("@/assets/images/loginView/loginBackGround.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 0px 0px;
  background-blend-mode: lighten;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  .main-box {
    width: auto;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    .top-box {
      width: 695px;
      height: 90px;
      margin-bottom: 10px;
      overflow: hidden;
    }

    .login-box {
      width: 1100px;
      height: 527px;
      background: #ffffff;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      display: flex;
      // animation: login-box-in 1s;

      .left-box {
        width: 694px;
        height: 100%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        // background:Back_color2.png ;
        background: url("@/assets/images/loginView/Back_color2.png") no-repeat center center;
        background-size: 100% 100%;
        background-position: 100% 100%;

        .house2 {
          width: 100%;
          height: 100%;
        }

        .house {
          width: 100%;
          height: 100%;
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
          z-index: 2;
          position: absolute;
          animation: add 1s linear infinite;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;

          @keyframes add {
            0% {
              opacity: 1;
            }

            50% {
              opacity: 0.5;
            }

            100% {
              opacity: 0;
            }
          }
        }

        .side-box {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .house-box {
          width: 100%;
          height: 100%;
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
          z-index: 1;
          position: absolute;
          animation: plus 2s linear infinite;
          animation-iteration-count: 1;

          @keyframes plus {
            0% {
              opacity: 0;
            }

            50% {
              opacity: 0.5;
            }

            100% {
              opacity: 1;
            }
          }
        }

        .dingding-qrcode-box {
          width: 300px;
          height: 300px;
          position: absolute;
          left: 31.2%;
          top: 23.5%;
          z-index: 0;
          animation: code 2s linear infinite;
          animation-iteration-count: 1;

          @keyframes code {
            0% {
              opacity: 0;
            }

            50% {
              opacity: 0.5;
            }

            100% {
              opacity: 1;
            }
          }
        }
      }

      .right-box {
        width: 406px;
        height: 100%;
        padding-top: 80px;
        line-height: 1;
        text-align: center;

        .title-login {
          font-family: Microsoft YaHei UI;
          font-size: 24px;
          font-weight: bold;
          color: #444444;
        }

        .input {
          width: 294px;
          height: 40px;
          border-radius: 8px;
          box-sizing: border-box;
          border: 1px solid #dbdbdb;
          margin: 0 auto;
          margin-top: 20px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          background: #f7f7f7;

          img {
            width: 24px;
            height: 24px;
            margin: 0 8px;
          }

          input {
            width: calc(100% - 40px);
            height: 100%;
            background: transparent;
            border: none;
            outline: none;
            font-size: 14px;
            // color: #909399;
          }

          .viewview {
            cursor: pointer;
          }
        }

        .login {
          margin: 0 auto;
          margin-top: 18px;
          width: 294px;
          height: 40px;
          border-radius: 40px;
          background: #068324;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          cursor: pointer;
        }

        .cheer {
          width: 266px;
          height: 175px;
          margin-top: 40px;
          animation: young 4s ease-in-out 2s infinite;
        }

        @keyframes young {
          0% {
            scale: 1;
          }

          8% {
            scale: 1.2;
          }

          16% {
            scale: 1;
          }

          100% {
            scale: 1;
          }
        }
      }
    }

    @keyframes login-box-in {
      0% {
        transform: translateY(120%);
      }

      100% {
        transform: translateY(0%);
      }
    }

    .bottom-box {
      padding-top: 26px;

      .copyright {
        margin: 0 auto;
        font-size: 14px;
        line-height: 28px;
        text-align: center;
        color: #ffffff;
      }
    }
  }
}
</style>
