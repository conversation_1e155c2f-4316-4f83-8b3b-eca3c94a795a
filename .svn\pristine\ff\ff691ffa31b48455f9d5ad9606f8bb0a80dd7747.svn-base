import { reactive } from "vue";

export class TagData {
    /** @type {any[]} */
    tagList;
    /** @type {string} */
    tagActive;
}

export function useTag() {

    const tagData = reactive({
        tagList: [
            { name: "全部", num: "", value: "" },
            { name: "待分配", num: "", value: 0 },
            { name: "待接单", num: "", value: 1 },
            { name: "已接单", num: "", value: 2 },
            { name: "已取消", num: "", value: 3 },
        ],
        tagActive: "",
    })

    /**
     * tagchange
     * 
     * @returns {void}
     */
    const tagchange = (tag) => {
        tagData.tagActive = tag.value
    }

    return {
        tagData,
        tagchange
    }
}