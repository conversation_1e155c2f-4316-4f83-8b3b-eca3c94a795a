<template>
  <nd-dialog ref="dialogRef" title="角色详情" width="60vw" height="60vh">
    <div class="dialogBox" v-loading="loading">
      <el-form ref="addFormRef" :model="page.role" :rules="page.status == 'detail' ? '' : rules" class="add-box">
        <div class="borderBox">
          <el-row>
            <el-col :span="24">
              <el-form-item label="权限名称" label-width="100px" prop="rolesName">
                <nd-input v-model="page.role.rolesName" :disabled="page.status == 'detail' ? true : false"
                  placeholder="请输入" width="100%" maxlength="20"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="权限描述" label-width="100px" prop="describes">
                <nd-input v-model="page.role.describes" :disabled="page.status == 'detail' ? true : false"
                  type="textarea" placeholder="请输入" width="100%" maxlength="200" show-word-limit></nd-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="borderBox">
          <div class="box_title">
            <div class="colorLine"></div>权限配置项
          </div>
          <nd-table :data="page.tableData" ref="roleTableRef" style="width: 100%" row-key="id" border
            :tree-props="treeProps" :header-row-class-name="page.status == 'detail' ? 'hideAllCheck' : ''">
            <el-table-column type="selection" width="55" :selectable="() => page.status == 'detail' ? false : true" />
            <el-table-column prop="name" label="界面（菜单）权限" min-width="200px" />
            <!-- <el-table-column label="操作（功能按钮权限）权限" min-width="400px">
              <template #default="{ row }">
                <template v-if="row.data.menuType == 1">
                  <div class="dataList" v-for="(item, index) in row.data" :key="index">
                    <el-checkbox v-model="item.check" />
                  </div>
                </template>
</template>
</el-table-column> -->
          </nd-table>
        </div>
      </el-form>
    </div>
    <template #footer>
      <template v-if="page.status != 'detail'">
        <nd-button type="primary" icon="FolderChecked" @click="save">保&nbsp;存</nd-button>
        <nd-button type="" icon="Close" @click="close">取&nbsp;消</nd-button>
      </template>
      <template v-else>
        <nd-button type="" icon="Close" @click="close">关&nbsp;闭</nd-button>
      </template>
    </template>
  </nd-dialog>
</template>

<script setup>
import { nextTick, reactive, ref } from "vue";
import ndDialog from "@/components/ndDialog.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndTable from "@/components/ndTable.vue";
import axios from "axios";
import { ElMessage } from "element-plus";
let emit = defineEmits(["before-close"]);

const dialogRef = ref(null);
const page = reactive({
  status: "detail",
  role: {
    rolesId: "",
    rolesName: "",
    describes: "",
  },
  tableData: []
});
let loading = ref(false)
const treeProps = reactive({
  checkStrictly: false,
})

const addFormRef = ref(null);

// 表单校验规则
const rules = reactive({
  rolesName: [{ required: true, message: "请输入权限名称！", trigger: ["blur", "change"] }],
  describes: [{ required: true, message: "请输入权限描述！", trigger: ["blur", "change"] }],
});

function open(type, rolesId) {
  console.log("rolesId", rolesId);
  page.role.rolesId = rolesId || "";
  page.status = type;
  page.role.rolesName = "";
  page.role.describes = "";

  dialogRef.value?.open();
  if (addFormRef.value) addFormRef.value.resetFields();

  if (rolesId) getRoleDetail()
  getRolesList();
}

function getRoleDetail() {
  axios({
    url: "/roles/find",
    method: "get",
    data: { rolesId: page.role.rolesId },
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      page.role.rolesName = res.data.data.rolesName;
      page.role.describes = res.data.data.describes;
    } else {
      ElMessage.error(res.data.message);
    }
  })
}

const roleTableRef = ref(null)
function getRolesList() {
  loading.value = true;
  axios({
    url: "/menu/findMenuTreeById",
    method: "get",
    data: { rolesId: page.role.rolesId },
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      loading.value = false;
      page.tableData = res.data.data || [];
      // page.tableData[0].check = true;
      nextTick(() => {
        function getChildrenCheck(children) {
          children.forEach((item) => {
            if (item.check) {
              roleTableRef.value.toggleRowSelection(item, true)
            } else {
              roleTableRef.value.toggleRowSelection(item, false)
            }
            if (item.children && item.children.length > 0) {
              getChildrenCheck(item.children)
            }
          })

        }
        getChildrenCheck(page.tableData)
      })
    } else {
      ElMessage.error(res.data.message);
    }
  })
}

function save() {
  // 校验
  addFormRef.value.validate((valid) => {
    if (valid) {
      const menuIdList = roleTableRef.value.getSelectionRows().map(item => item.id)
      if (menuIdList.length == 0) return ElMessage.error("请勾选界面（菜单）权限！");
      const postData = {
        rolesId: page.role.rolesId,
        rolesName: page.role.rolesName,
        describes: page.role.describes,
        menuIdList: menuIdList,
      }
      if (postData.rolesId) {
        editData(postData)
      } else {
        addData(postData)
      }
    }
  });
}

function addData(postData) {
  axios({
    url: "/roles/save",
    method: "post",
    data: postData,
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("保存成功！");
      emit("before-close");
      close();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

function editData(postData) {
  axios({
    url: "/roles/update",
    method: "post",
    data: postData,
    serverName: "nd-base2"
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("保存成功！");
      emit("before-close");
      close();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

// 关闭
const close = () => {
  // 清空校验
  if (addFormRef.value) addFormRef.value.resetFields();

  dialogRef.value.close();
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
:deep(.hideAllCheck .el-checkbox) {
  display: none;
}

.dialogBox {
  padding: 15px 12px;

  :deep(.el-textarea__inner) {
    height: 80px;
  }
}

.borderBox {
  border: 1px solid #EAEAEA;
  background-color: #fff;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 5px;

  .box_title {
    font-family: Microsoft YaHei;
    font-size: 16px;
    font-weight: bold;
    color: #444444;
    display: flex;
    align-items: center;
    margin-bottom: 14px;

    .colorLine {
      width: 2px;
      height: 16px;
      background: #068324;
      margin-right: 6px;
    }
  }
}
</style>