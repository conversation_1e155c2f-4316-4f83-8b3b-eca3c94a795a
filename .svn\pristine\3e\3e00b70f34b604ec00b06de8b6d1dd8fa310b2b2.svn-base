<template>
    <nd-dialog
        ref="previewDialogRef"
        :title="dialogData.title"
        width="40vw"
        height="36vh"
    >
        <div class="view-box">
            <div class="box">
                <el-scrollbar>
                    <nd-upload
                        :files="fileData.files"
                        fzgs="product1"
                        :limit="1"
                        disabled
                    />
                </el-scrollbar>
            </div>
        </div>
        <template #footer>
            <nd-button @click="close">取消</nd-button>
        </template>
    </nd-dialog>
</template>

<script setup>
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";

import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";

// hooks
import { useDialog } from "../../hooks/previewAttanchment/useDialog";
import { useFile } from "../../hooks/previewAttanchment/useFile";

const previewDialogRef = ref(null);

// useFile
const { fileData } = useFile();

// useDialog
const { dialogData, open, close } = useDialog({
    dialogRef: previewDialogRef,
    fileData: fileData,
});

defineExpose({
    open,
});
</script>

<style lang="scss" scoped>
/* 可添加样式 */
.view-box {
    padding: 12px;
    height: 100%;

    .box {
        background: #ffffff;
        border: 1px solid #eaeaea;
        border-radius: 5px;
        padding: 12px;
        height: 100%;
        overflow-y: auto;
    }
}
</style>
