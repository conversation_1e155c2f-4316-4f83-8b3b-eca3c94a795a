<template>
  <div class="nd-search-more-box">
    <div class="arrow" :style="{ left: arrowMarginLeft }"></div>
    <div class="arrow-cover" :style="{ left: arrowMarginLeft }"></div>
    <div class="nd-search-more-content">
      <slot></slot>
    </div>
    <div class="nd-search-more-foot">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
// // 导入公共组件
// import ndButton from "./ndButton.vue";
// // 导入vue
// import { onMounted, reactive, ref, inject, watch } from 'vue';

const props = defineProps({
  arrowMarginLeft: {
    type: String,
    default: "80px",
  },
})

// var page = reactive({
//   showContent: false // 是否显示查询条件
// })

// function showContent() {
//   page.showContent = !page.showContent;
// }
</script>

<style scoped lang="scss">
.nd-search-more-box {
  width: 100%;
  height: auto;
  padding-top: 12px;
  padding-bottom: 12px;
  background-color: #ffffff;
  position: relative;
  border: 1px solid #E4E7ED;

  .arrow {
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-bottom-color: #E4E7ED;
    position: absolute;
    // left: 280px;
    top: -16px;
  }

  .arrow-cover {
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-bottom-color: #ffffff;
    position: absolute;
    // left: 280px;
    top: -14px;
  }

  .nd-search-more-content {
    width: 100%;
    height: auto;
    padding-left: 8px;
    padding-right: 8px;
    font-size: 14px;
    display: flex;
    flex-wrap: wrap;
  }

  .nd-search-more-foot {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
}
</style>