<template>
  <div class="nd-dropdown-box" :style="{ width: width }">
    <el-dropdown v-bind="$attrs">
      <el-button>
        {{ title }}<el-icon style="margin-left: 5px"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <slot></slot>
      </template>
    </el-dropdown>
  </div>
</template>
<script setup>
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: "未命名",
  },
  // 宽度
  width: {
    type: String,
    default: "auto",
  },
});
</script>
<style lang="scss" scoped>
.nd-dropdown-box {
  .dropdown-menu {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  :deep(.el-button) {
    border: 1px solid #0b8df1; 
    color: #0b8df1;
  }
}

.normal + .nd-dropdown-box {
  margin-left: 10px;
}
</style>
