<template>
  <nd-dialog ref="previewDialogRef" width="40vw" title="附件详情" height="36vh">
    <div class="view-box">
      <div class="box">
        <el-scrollbar>
          <nd-upload
            :files="fileData.files"
            fzgs="product1"
            :limit="1"
            disabled
          />
        </el-scrollbar>
      </div>
    </div>
    <template #footer>
      <nd-button @click="close">取消</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";

import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";

const previewDialogRef = ref(null);

const fileData = reactive({
  files: [],
});

const open = (data) => {
  fileData.files = data;
  previewDialogRef.value.open();
};

const close = () => {
  previewDialogRef.value.close();
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
/* 可添加样式 */
.view-box {
  padding: 12px;
  height: 100%;

  .box {
    background: #ffffff;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    padding: 12px;
    height: 100%;
    overflow-y: auto;
  }
}
</style>
