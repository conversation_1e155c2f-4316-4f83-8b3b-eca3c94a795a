<template>
  <div class="nd-input-number-box" :style="{ width: width }">
    <el-input-number ref="inputNumberRef" v-bind="$attrs" controls-position="right"> </el-input-number>
  </div>
</template>

<script setup>
import { ref } from "vue";

var prop = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px",
  },
});
</script>
<style lang="scss" scoped>
.nd-input-number-box {
  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}
</style>
