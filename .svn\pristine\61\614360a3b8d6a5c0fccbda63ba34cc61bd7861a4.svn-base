<template>
  <div class="ndb-page-tree-list-box">
    <div class="left-box">
      <slot name="tree"></slot>
    </div>
    <div class="right-box">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import ndbPageList from "@/components/business/ndbPageList/index.vue";
</script>

<style lang="scss" scoped>
.ndb-page-tree-list-box {
  width: 100%;
  height: 100%;
  display: flex;

  .left-box {
    width: 200px;
    height: 100%;
    margin-right: 10px;
  }

  .right-box {
    width: 0px;
    height: 100%;
    flex: 1;
    padding: 15px;
    border: 1px solid #eeeeee;
    background-color: #fff;
    display: flex;
    flex-direction: column;
  }
}
</style>
