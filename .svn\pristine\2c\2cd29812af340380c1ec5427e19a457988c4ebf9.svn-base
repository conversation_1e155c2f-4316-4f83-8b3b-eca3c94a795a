<template>
    <div class="basic-information-box">
        <div class="basic-information-top">
            <div class="top-code">
                需求编号：
                <span id="copy-code">{{
                    detailData.detailData.demandCode
                }}</span>
                <img
                    @click="copy(detailData.detailData.demandCode)"
                    class="copy copy-button"
                    src="@/assets/images/purchaseRequestFormView/copy.png"
                    alt=""
                />
            </div>

            <div
                class="top-status"
                :style="`color: ${
                    STATUS_TEMPLATE[detailData.detailData.status]?.color
                };`"
            >
                {{ STATUS_TEMPLATE[detailData.detailData.status]?.text }}
            </div>
        </div>

        <div class="line"></div>

        <div class="basic-information-main">
            <div class="template">
                <div class="template-item">
                    <div class="item">
                        <div class="item-title">买家（商户）：</div>

                        <div class="item-main">
                            {{ detailData.detailData.nickname }}
                        </div>
                    </div>
                    <div class="item">
                        <div class="item-title">收鱼日期：</div>

                        <div class="item-main">
                            {{ detailData.detailData.acceptTime }}
                        </div>
                    </div>
                </div>

                <div class="template-item">
                    <div class="item">
                        <div class="item-title">其他要求：</div>

                        <div class="item-main">
                            <div class="text">
                                {{ detailData.detailData.remark }}
                            </div>

                            <div
                                class="audo"
                                v-for="(item, index) in detailData.detailData
                                    ?.sourceVos || []"
                                :key="index"
                                @click="
                                    handleVoice(
                                        item,
                                        detailData.detailData?.sourceVos
                                    )
                                "
                            >
                                <img
                                    class="audo-img"
                                    src="@/assets/images/purchaseRequestFormView/audo.png"
                                    alt=""
                                />
                                {{ item.sourceSize }}"
                            </div>
                        </div>
                    </div>
                </div>

                <div class="template-item">
                    <div class="item">
                        <div class="item-title">登记时间：</div>

                        <div class="item-main">
                            {{ detailData.detailData.insertTime }}
                        </div>
                    </div>
                </div>

                <template v-if="detailData.detailData.status === 3">
                    <div class="template-item">
                        <div class="item">
                            <div class="item-title">取消时间：</div>

                            <div class="item-main">
                                {{ detailData.detailData.cancelTime }}
                            </div>
                        </div>
                    </div>
                    <div class="template-item">
                        <div class="item">
                            <div class="item-title">取消理由：</div>

                            <div class="item-main">
                                {{ detailData.detailData.cancelReason }}
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup>
// hooks
import { inject, watch } from "vue";
import { useCopy } from "../../hooks/basicInformation/useCopy";
import { useBasicInformation } from "../../hooks/basicInformation/useBasicInformation";
import { useAudo } from "../../hooks/basicInformation/useAudo";

// inject
const detailData = inject("$purchaseRequestFormDetailData");

watch(
    () => detailData,
    () => {
        console.log(detailData, "detailData");
    },
    { immediate: true, deep: true }
);

// useCopy
const { copy } = useCopy();

// useBasicInformation
const { STATUS_TEMPLATE } = useBasicInformation();

// useAudo
const { handlePlay, handlePause, handleVoice } = useAudo();
</script>

<style lang="scss" scoped>
.basic-information-box {
    width: 100%;
    border-radius: 5px;
    background: #ffffff;
    border: 1px solid #eaeaea;

    .basic-information-top {
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 24px;

        .top-code {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            font-variation-settings: "opsz" auto;
            color: #999999;
            display: flex;
            align-items: center;

            .copy {
                width: 18px;
                height: 18px;
                cursor: pointer;
                user-select: none;
                margin-left: 6px;
            }
        }

        .top-status {
            font-family: Microsoft YaHei;
            font-size: 24px;
            font-weight: bold;
            line-height: 24px;
            letter-spacing: 0px;

            font-variation-settings: "opsz" auto;
            color: #0b8df1;
        }
    }

    .line {
        height: 1px;
        width: 100%;
        background: #eaeaea;
    }

    .basic-information-main {
        padding: 15px 24px;

        .template {
            .template-item {
                display: flex;
                align-items: center;
                gap: 0 10px;
                margin-bottom: 8px;

                &:nth-last-of-type(1) {
                    margin-bottom: 0;
                }

                .item {
                    flex: 1;
                    display: flex;
                    align-items: center;

                    .item-title {
                        min-width: 120px;
                        font-family: Microsoft YaHei UI;
                        font-size: 14px;
                        font-weight: normal;
                        line-height: 24px;
                        letter-spacing: 0px;

                        color: #888888;
                        align-self: flex-start;
                    }

                    .item-main {
                        flex: 1;
                        font-family: Microsoft YaHei UI;
                        font-size: 14px;
                        font-weight: normal;
                        color: #000000;
                        word-break: break-all;

                        .text {
                            min-height: 24px;
                            display: flex;
                            align-items: center;
                        }

                        .audo {
                            height: 24px;
                            width: 100px;
                            border-radius: 4px;
                            background: #e4e4e4;
                            margin-top: 4px;
                            display: flex;
                            align-items: center;
                            padding: 0 6px;
                            font-family: PingFang SC;
                            font-size: 10px;
                            font-weight: normal;
                            line-height: 20px;
                            text-align: justify; /* 浏览器可能不支持 */
                            letter-spacing: 0px;
                            cursor: pointer;
                            color: #333333;

                            .audo-img {
                                width: 10px;
                                height: 10px;
                                margin-right: 4px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
