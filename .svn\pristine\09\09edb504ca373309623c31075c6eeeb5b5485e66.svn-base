<template>
  <div class="ndb-upload-box">
    <div class="file-box">
      <div v-for="(item, index) in prop.files" :key="index" class="file-item">
        <el-image
          v-if="item.suffix === 'image/jpeg' || item.suffix === 'image/png' || item.suffix === 'image/bmp'"
          class="file-img"
          :src="item.fullPath"
          :preview-src-list="previewFiles"
          :initial-index="index"
          fit="cover"
        />
        <div v-if="item.suffix === 'application/pdf'" class="file-img" @click="previewPDf(item.fullPath)">
          <img src="@/assets/pdf.png" alt="" />
        </div>
        <!-- <div v-if="item.suffix == 'pdf' || item.suffix == 'PDF'" class="file-img" @click="previewPDf(item.filePath)">
          <img src="@/assets/pdf.png" alt="" />
        </div>
        <div v-else-if="item.suffix == 'zip' || item.suffix == 'ZIP' || item.suffix == 'rar' || item.suffix == 'RAR'" class="file-img" @click="previewOffice(item)">
          <img src="@/assets/images/zip.png" alt="" />
        </div>
        <div
          v-else-if="item.suffix == 'txt' || item.suffix == 'TXT' || item.suffix == 'doc' || item.suffix == 'DOC' || item.suffix == 'docx' || item.suffix == 'DOCX'"
          class="file-img"
          @click="previewOffice(item)"
        >
          <img src="@/assets/images/doc.png" alt="" />
        </div>
        <div v-else-if="item.suffix == 'xls' || item.suffix == 'XLS' || item.suffix == 'xlsx' || item.suffix == 'XLSX'" class="file-img" @click="previewOffice(item)">
          <img src="@/assets/images/xls.png" alt="" />
        </div>
        <div v-else-if="item.suffix == 'ppt' || item.suffix == 'PPT' || item.suffix == 'pptx' || item.suffix == 'PPTX'" class="file-img" @click="previewOffice(item)">
          <img src="@/assets/images/ppt.png" alt="" />
        </div> -->
        <template v-if="!prop.disabled">
          <el-icon class="el-icon-remove">
            <CircleClose @click="deleteFile(item, index)" />
          </el-icon>
        </template>
      </div>
      <!-- <div v-if="upload.fileList && upload.fileList.length === 0 && prop.disabled" class="no-data"></div> -->
      <div v-if="!prop.disabled && prop.files.length < limit" class="file-item file-item-add" @click="openUploadDialog()">+</div>
    </div>
    <upload ref="uploadRef" :files="prop.files" :fzgs="prop.fzgs" :limit="prop.limit" :mime="prop.mime" :tip1="prop.tip1" :tip2="prop.tip2"> </upload>
  </div>
</template>

<script setup>
// 导入子组件
import upload from "./components/upload.vue";

// 导入vue
import { ref, inject, computed, onMounted, reactive, watch, nextTick } from "vue";
import { ElMessage as elMessage } from "element-plus";

// 定义axios
const $axios = inject("$axios");

// 定义ref
const uploadRef = ref(null);

// 定义属性
const prop = defineProps({
  // 只读
  disabled: {
    type: Boolean,
    default: false,
  },
  // 附件归属
  fzgs: {
    type: String,
    default: "",
  },
  // 文件
  files: {
    type: Array,
    default: () => [],
  },
  // 限制的数量
  limit: {
    type: Number,
    default: 999999,
  },
  // 支持的文件类型
  mime: {
    type: String,
    default: "jpg,jpeg,png,bmp",
  },
  // tip1
  tip1: {
    type: String,
    default: "建议尺寸：800*800像素，最多上传15张；",
  },
  // tip2
  tip2: {
    type: String,
    default: "支持上传文件格式为jpeg、jpg、png、bmp。单个附件大小限制15M。",
  },
});

// 计算属性 - 预览
const previewFiles = computed(() => {
  return prop.files.map((item) => item.fullPath);
});

// 打开上传对话框
const openUploadDialog = () => {
  uploadRef.value.open();
};

// 删除附件
const deleteFile = (item, index) => {
  $axios({
    url: "/file/delete",
    method: "post",
    data: {
      id: item.id,
    },
  }).then((r) => {
    if (r.data.code === 2000) {
      prop.files.splice(index, 1);
      elMessage({
        message: "删除成功",
        type: "success",
      });
    } else {
      elMessage.error(r.data.message);
    }
  });
};

// 预览PDF
function previewPDf(url) {
  window.open(url);
}

// 预览office
function previewOffice(item) {
  $axios({
    url: item.filePath,
    method: "get",
    data: {},
    responseType: "blob",
    needServerPath: false,
  }).then((res) => {
    if (!res) return;
    const blob = new Blob([res.data], {
      type: "application/vnd.ms-excel",
    }); // 构造一个blob对象来处理数据，并设置文件类型
    const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
    const a = document.createElement("a"); //创建a标签
    a.style.display = "none";
    a.href = href; // 指定下载链接
    a.setAttribute("download", item.curName);
    a.click(); //触发下载
    URL.revokeObjectURL(a.href); //释放URL对象
  });
}
</script>

<style lang="scss" scoped>
.demo-image__error .image-slot {
  font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}

.demo-image__error .el-image {
  width: 100%;
  height: 200px;
}

.ndb-upload-box {
  width: 100%;
  height: auto;

  .file-box {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
    flex-wrap: wrap;

    .file-item {
      float: left;
      list-style: none;
      width: 80px;
      height: 80px;
      border: 1px solid #dce6f3;
      border-radius: 2px;
      cursor: pointer;
      position: relative;
      margin: 0 6px 6px 0;

      .file-img {
        width: 100%;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
        }

        :deep(.el-image__inner) {
          width: 80px;
          height: 80px;
        }
      }

      .el-icon-remove {
        color: red;
        position: absolute;
        top: 4px;
        right: 4px;
        font-size: 14px;
      }
    }

    .no-data {
      margin-right: 10px;
    }

    .no-data-img {
      width: 153.3px;
      height: 100px;
      margin-bottom: 10px;
      margin-right: 8px;
      border: 1px solid #e2eaf5;
      text-align: center;
    }

    .file-item-add {
      width: 80px;
      height: 80px;
      background: #fff;
      border-radius: 4px;
      // border: 1px solid #dce6f3;
      border: none;
      // border-radius: 2px;
      font-size: 30px;
      color: #068324;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border: 1px solid #dcdfe6;
    }
  }
}
</style>
