<template>
  <div class="nd-cascader-box" :style="{ width: width }">
    <el-cascader v-bind="$attrs" :placeholder="placeholder" popper-class="nd-cascader-popper" />
  </div>
</template>
  
<script setup>
let prop = defineProps({
  width: {
    type: String,
    default: "230px",
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
})
</script>
  
<style lang="scss">
.el-radio__input.is-checked .el-radio__inner {
  background: #068324 !important;
  border-color: #068324 !important;
}
.el-radio__inner:hover {
  border-color: #068324 !important;
}
.nd-cascader-popper {
  .el-cascader-node.in-active-path,
  .el-cascader-node.is-active,
  .el-cascader-node.is-selectable.in-checked-path {
    color: #068324 !important;
  }
}
</style>
<style lang="scss" scoped>
:deep(.el-cascader) {
  width: 100% !important;
  border-radius: 0px;
  // border: 1px solid #dcdfe6;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: 14px;
  background-color: transparent;

  .el-input__inner {
    border: none;
  }
  .el-input.is-focus .el-input__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
}
:deep(.is-focus) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
</style>