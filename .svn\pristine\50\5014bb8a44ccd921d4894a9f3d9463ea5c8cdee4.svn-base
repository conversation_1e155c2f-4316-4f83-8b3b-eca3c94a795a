<template>
    <!-- // 编辑地址 -->
    <nd-dialog ref="editAddressRef" width="60vw" height="48vh" :title="title" align-center>
        <el-form ref="addFormRef" :model="form" :rules="rules" class="add-box" label-position="left">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="收货人" label-width="80px" prop="name">
                        <nd-input v-model="form.name" placeholder=" " width="90%" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="联系电话" label-width="80px" prop="phone">
                        <nd-input v-model="form.phone" placeholder=" " width="90%" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="配送地址" label-width="80px" prop="classis">
                        <el-cascader @change="cascaderChenage" :props="cascaderProps" width="90%" style="width:90%" />
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item label="详细地址" label-width="80px" prop="detailAddress">
                        <nd-input type="textarea" v-model="form.detailAddress" placeholder=" " width="95.5%" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <nd-button type="" icon="Pointer" @click="submit('1')">确&nbsp;定</nd-button>
            <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button>
        </template>
    </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vuea
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义方法
let myEmit = defineEmits(["refreshTable"]);
// 定义属性
const props = defineProps({

});

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const editAddressRef = ref(null);
// 定义对话框的标题
let title = ref("");
// 定义表单数据
const addFormRef = ref(null);
let tableContent = ref(null);
let tableData = ref([]);
let orderId = ref('');
let form = ref({
    wlgs: '',
    wlbh: '',
    remake: '',
    provinceId: '',
    province: '',
    cityId: '',
    city: '',
    districtId: '',
    district: '',
    detailAddress: '',
    townId: "",
    town: "",
    villageId: "",
    village: "",
});

const handleMenuNameChange = (value) => {
    console.log(value);
    if (form.value.menuName) {
        console.log("有值");
    } else {
        console.log("没值了");
        // form.value.menuName = ''
        form.value.functionName = "";
    }
};



// 定义校验规则 ==========================================================

// 自定义校验规则 --- 正则 --- 功能菜单名称最长不得超过10个汉字
const validateFunctionName = (rule, value, callback) => {
    const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
    let chineseCharacters = form.value.menuName.match(chineseReg); // 匹配输入字符串中的汉字
    if (chineseCharacters && chineseCharacters.length > 10) {
        return callback(new Error("error"));
    } else {
        return callback();
    }
};

// 表单校验规则
const rules = reactive({
    name: [{ required: true, message: "请输入收货人", }],
    phone: [{ required: true, message: "请输入拒联系电话", }],
    detailAddress: [{ required: true, message: "请输入详细地址", }],
    functionName: [
        { required: true, message: "请输入功能点名称", trigger: "blur" },
        // { min: 1, max: 50, message: "最长不得超过50个汉字", trigger: "blur" },
        { validator: validateFunctionName, trigger: "blur", message: "最长不得超过50个汉字" },
    ],

});

// 打开弹窗 ==============================================================
// 打开弹窗
function open(dzglVo) {
    if (dzglVo) {
        form.value = dzglVo;
    }
    title.value = "编辑地址";
    editAddressRef.value.open();
}
// 获得详情
function getDetail() {
    $axios({
        url: "/order/manage/find",
        serverName: 'nd-base2',
        method: "get",
        data: {
            orderId: orderId.value,
        },
    }).then((res) => {
        if (res.data.code === 2000) {
            tableContent.value = res.data.data;
            tableData.value = tableContent.value.detailVos;
        }
    });
}

// 清空表单
const clear = () => {
    // 清空校验
    if (addFormRef.value) {
        addFormRef.value.resetFields();
    }
};

// 关闭弹窗 ==============================================================
const close = () => {
    editAddressRef.value.close();
    clear();
};

// 保存提交 ==============================================================
const submit = async () => {
    await addFormRef.value.validate((valid, fields) => {
        if (valid) {
            // 编辑
            if (form.value.pointId !== null) {
                let params = form.value;
                $axios({
                    url: "/order/manage/dzgl/update",
                    method: "post",
                    serverName: 'nd-base2',
                    data: params,
                }).then((res) => {
                    if (res.data.code === 2000) {
                        // 轻提示
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        close();
                    } else {
                        ElMessage({
                            message: res.data.message,
                            type: "warning",
                        });
                    }
                });
            } else {
                // 新建
                let params = form.value;
                $axios({
                    url: "/projectPoint/save",
                    method: "post",
                    // serverName: 'zjd-base',
                    data: params,
                }).then((res) => {
                    console.log(form.value.projectId, "项目id");
                    if (res.data.code === 2000) {
                        // 轻提示
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        myEmit("refreshTable");
                        // 关闭弹框
                        close();
                    } else {
                        ElMessage({
                            message: res.data.message,
                            type: "warning",
                        });
                    }
                });
            }
        } else {
            console.log("校验失败!", fields);
        }
    });
};
// 定义一个缓存对象来存储已加载的数据
const loadedDataCache = ref({});

// 动态加载
const cascaderProps = {
    lazy: true,
    label: 'name',
    value: 'id',
    lazyLoad(node, resolve) {
        const { level, value = '' } = node
        setTimeout(() => {
            $axios({
                url: "/common/area",
                method: "get",
                serverName: 'nd-base2',
                data: {
                    id: value
                },
            }).then((res) => {
                if (res.data.code === 2000) {
                    // 缓存已加载的数据
                    loadedDataCache.value[value] = res.data.data;
                    resolve(res.data.data)
                }
            });
        }, 1000)
    },
}
//级联
const cascaderChenage = (value) => {
    console.log("选中项的 id:", value);
    const names = [];

    let currentId = null;
    let currentLevelData = loadedDataCache.value['']; // 从根数据开始查找
    for (const id of value) {
        currentId = id;
        const item = currentLevelData.find(item => item.id === currentId);
        if (item) {
            names.push(item.name);
            // 更新下一级数据
            currentLevelData = loadedDataCache.value[currentId] || [];
        }
    }
    console.log("🚀 ~ cascaderChenage ~ names:", names)
    // 可以将选中的 name 更新到表单数据中

    form.value.provinceId = value[0] || '';
    form.value.province = names[0] || '';
    form.value.cityId = value[1] || '';
    form.value.city = names[1] || '';
    form.value.districtId = value[2] || '';
    form.value.district = names[2] || '';
    form.value.townId = value[3] || '';
    form.value.town = names[3] || '';
    form.value.villageId = value[4] || '';
    form.value.village = names[4] || '';
}
// 暴露方法给父组件 =======================================================
defineExpose({
    open,
    clear,
});
</script>

<style lang="scss" scoped>
.goodstotal {
    margin-bottom: 20px;
}

.add-box {
    padding: 0px;
    margin-top: 32px;

    :deep(.el-textarea__inner) {
        height: 80px;
    }
}
</style>