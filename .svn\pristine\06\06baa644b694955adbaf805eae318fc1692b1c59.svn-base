<template>
  <div class="el-quarter-picker">
    <el-popover :visible="visible" :width="322" placement="bottom-start" trigger="click">
      <div>
        <div class="top-box">
          <el-icon @click="preYear">
            <DArrowLeft />
          </el-icon>
          <span>{{ currYear }} 年</span>
          <el-icon @click="nextYear">
            <DArrowRight />
          </el-icon>
        </div>
        <div class="content-box">
          <span :style="{ color: i.color }" @click="quartSelect(i)" v-for="i in quartlist" :key="i.id">{{ i.label }}</span>
        </div>
      </div>
      <template #reference>
        <el-input readonly v-model="innerValue" @focus="onFocus" ref="inputRef" @blur="onBlur" :placeholder="placeholder" :prefix-icon="Calendar" :clearable="false" style="width: 230px;">
          <!-- <template #suffix>
            <el-icon @click="clear" v-show="clearVisble"><CircleClose /></el-icon>
          </template> -->
        </el-input>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, nextTick } from "vue";
import { Calendar, DArrowRight, DArrowLeft, CircleClose } from "@element-plus/icons-vue";

// 定义属性
const props = defineProps({
  placeholder: {
    type: String,
    default: "选择季度",
  },
  modelValue: {
    type: String,
    default: "",
  },
});

// 定义ref
const inputRef = ref<any>(null);

// 定义emit
const emit = defineEmits(["change", "update:modelValue"]);

const innerValue = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  },
});

const visible = ref(false);
const currYear = ref<any>("");
const currMonth = ref<any>("");
const currDay = ref<any>("");
const quartlist = ref<any>([
  {
    label: "第一季度（1～3月）",
    id: 1,
    color: "var(--el-datepicker-text-color)",
  },
  {
    label: "第二季度（4～6月）",
    id: 2,
    color: "var(--el-datepicker-text-color)",
  },
  {
    label: "第三季度（7～9月）",
    id: 3,
    color: "var(--el-datepicker-text-color)",
  },
  {
    label: "第四季度（10～12月）",
    id: 4,
    color: "var(--el-datepicker-text-color)",
  },
]);
const currQuart = reactive<any>({});
const clearVisble = ref(false);
const startDate = ref(["-01-01", "-04-01", "-07-01", "-10-01"]);
const endDate = ref(["-03-31", "-06-30", "-09-30", "-12-31"]);

watch(
  () => innerValue.value,
  (val) => {
    // 内容不为空时显示清除
    nextTick(() => {
      clearVisble.value = !!val;
    });
  }
);

currQuart.value = quartlist.value[0];

// 获得当前年份，月份，日期
const getCurrYear = () => {
  let date = new Date();
  currYear.value = date.getFullYear();
  currMonth.value = date.getMonth() + 1;
  currDay.value = date.getDate();
  console.log(date.getDate());
  
  console.log('年月日', currYear.value, currMonth.value, currDay.value);
  // 若当前系统时间某个季度的首月10号及之前，则默认选中当前季度上一季度，如系统时间为4月5号，则默认选中相应年度第1季（即当前时间上一季度），若当前时间为4月15号，则默认选中当前时间月份所属季度（注意跨年的情况处理，即在次年首月10号之前年度默认显示上一年度，否则显示当前年度）
  if (currMonth.value === 1 && currDay.value <= 10) {
    currYear.value--
  }
};

// 获得当前季度
function getCurrQuarter() {
  const now = new Date();
  const month = now.getMonth() + 1;
  let index = 0;
  if (month <= 3) {
    index = 0;
  } else if (month <= 6) {
    index = 1;
  } else if (month <= 9) {
    index = 2;
  } else {
    index = 3;
  }
  // 若当前系统时间某个季度的首月10号及之前，则默认选中当前季度上一季度，如系统时间为4月5号，则默认选中相应年度第1季（即当前时间上一季度），若当前时间为4月15号，则默认选中当前时间月份所属季度（注意跨年的情况处理，即在次年首月10号之前年度默认显示上一年度，否则显示当前年度）
  if (currMonth.value === 1 && currDay.value <= 10) {
    index = 3
  } else if ((currMonth.value === 4 || currMonth.value === 7 || currMonth.value === 10) && currDay.value <= 10) {
    index --
  }
  // 高亮当前季度
  quartlist.value[index].color = "var(--el-color-primary)";
}

// 设置当前季度
const setCurrQuart = ()=>{
  console.log(innerValue.value);
}

// 输入框获得焦点
const inputFocus = () => {
  inputRef.value?.focus();
};

// 清空季度高亮
const resetHighlight = () => {
  quartlist.value[0].color = "var(--el-datepicker-text-color)";
  quartlist.value[1].color = "var(--el-datepicker-text-color)";
  quartlist.value[2].color = "var(--el-datepicker-text-color)";
  quartlist.value[3].color = "var(--el-datepicker-text-color)";
}

// 下一年
const nextYear = () => {
  resetHighlight()
  // 获取焦点防止丢失焦点隐藏
  inputFocus();
  let year = new Date(new Date().setFullYear(currYear.value + 1)).getFullYear();
  currYear.value = year;
  innerValue.value = currYear.value + "-" + currQuart.value.label;
  let emitDate = {
    startDate: currYear.value + startDate.value[currQuart.value.id - 1],
    endDate: currYear.value + endDate.value[currQuart.value.id - 1],
  };
  emit("change", emitDate);
};

// 上一年
const preYear = () => {
  resetHighlight()
  inputFocus();
  let year = new Date(new Date().setFullYear(currYear.value - 1)).getFullYear();
  currYear.value = year;
  innerValue.value = currYear.value + "-" + currQuart.value.label;
  let emitDate = {
    startDate: currYear.value + startDate.value[currQuart.value.id - 1],
    endDate: currYear.value + endDate.value[currQuart.value.id - 1],
  };
  emit("change", emitDate);
};

// 选中季度
const quartSelect = (item: any) => {
  quartlist.value.forEach((v: any) => {
    v.color = "var(--el-datepicker-text-color)";
    if (v.id == item.id) {
      v.color = "var(--el-color-primary)";
    }
  });
  currQuart.value = item;
  let emitDate = {
    startDate: currYear.value + startDate.value[currQuart.value.id - 1],
    endDate: currYear.value + endDate.value[currQuart.value.id - 1],
  };
  emit("change", emitDate);
  innerValue.value = currYear.value + "-" + currQuart.value.label;
  nextTick(() => {
    inputRef.value?.blur();
    visible.value = false;
    console.log(inputRef.value.blur);
  });
};

// 清除
const clear = () => {
  innerValue.value = "";
  let emitDate = {
    startDate: "",
    endDate: "",
  };
  emit("change", emitDate);
};

const onFocus = () => {
  visible.value = true;
};

const onBlur = () => {
  visible.value = false;
};

// 获得当前年份，月份，日期
getCurrYear();
// 获得当前季度
getCurrQuarter();
// 设置当前季度
// setCurrQuart()
console.log('年月日', currYear.value, currMonth.value, currDay.value);

// 重置时间默认值
function reset() {
  resetHighlight()
  // 获得当前年份，月份，日期
  getCurrYear();
  // 获得当前季度
  getCurrQuarter();
}

defineExpose({
  inputFocus,
  reset,
});
</script>

<style scoped>
.el-quarter-picker {
  display: inline-block;
  cursor: pointer;
}

.top-box {
  font-size: 16px;
  cursor: pointer;
  display: flex;
  padding-bottom: 12px;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.content-box {
  width: 100%;
  height: 100px;
  display: flex;
  padding-top: 12px;
  flex-wrap: wrap;
}

.content-box > span {
  width: 148px;
  height: 40px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
