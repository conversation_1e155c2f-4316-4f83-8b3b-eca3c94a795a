<template>
  <ndb-page-list :isSearch="false">
    <template #button>
      <nd-button @click="openAddDialog" type="primary" icon="plus" authKey="">新增</nd-button>
      <nd-button @click="mulDelete" icon="delete" authKey="">删除</nd-button>
    </template>

    <template #table>
      <nd-table style="height: 100%" :data="page.list.data" row-key="id" :tree-props="treeProps"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column prop="data.menuName" label="功能/菜单名称" align="left" min-width="120" show-overflow-tooltip />
        <el-table-column label="图标" align="center" width="150">
          <template #default="{ row }">
            <el-icon size="24" v-if="row.data.iconUrl" style="display:block;margin:0 auto">
              <component :is="row.data.iconUrl"></component>
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="data.menuUrl" label="URL" align="left" min-width="150" show-overflow-tooltip />
        <el-table-column prop="data.orderCode" label="排序" align="center" width="120" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <div class="caozuoBtn">
              <nd-button type="edit" @click="openEditDialog(scope.row)">编辑</nd-button>
              <nd-button type="delete" @click="delRow(scope.row.id)">删除</nd-button>
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
  </ndb-page-list>

  <detail-dialog ref="detailDialogRef" @before-close="getDataList" />
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndInput from "@/components/ndInput.vue"
import ndButton from "@/components/ndButton.vue"
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue"
import ndSearchMore from "@/components/ndSearchMore.vue"
import ndTable from "@/components/ndTable.vue"
import ndPagination from "@/components/ndPagination.vue"
import detailDialog from "./components/detailDialog.vue"
import { ElMessage, ElMessageBox } from "element-plus"

import axios from "axios";

const detailDialogRef = ref(null)

const page = reactive({
  search: {
    nameLike: '',
    describesLike: ''
  },
  list: {
    data: []
  }
})

const treeProps = reactive({
  checkStrictly: true,
})

onMounted(() => {
  getDataList()
})

// 获取数据列表
const getDataList = async () => {
  axios({
    url: '/menu/findMenuTree',
    method: 'get',
    serverName: 'nd-base2'
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data
    } else {
      ElMessage.error(res.data.message)
    }
  })
}

// 选中框
const selectedPoints = ref([]);
function handleSelectionChange(val) {
  selectedPoints.value = val;
}

// 打开新增对话框
const openAddDialog = () => {
  if (selectedPoints.value.length > 1) return ElMessage.error('选中数据不可超过1条！')
  console.log("selectedPoints.value", selectedPoints.value);

  let menuId = 0;
  let menuName = "";
  let menuPid = 0;
  if (selectedPoints.value.length > 0) {
    menuId = selectedPoints.value[0].data.menuId;
    menuName = selectedPoints.value[0].data.menuName;
    menuPid = selectedPoints.value[0].parentId;
  }
  if (menuPid > 0) return ElMessage.error('系统菜单最低允许创建到二级！')
  detailDialogRef.value.open('add', 0, menuName, menuId)
}

// 打开编辑对话框
const openEditDialog = (row) => {
  detailDialogRef.value.open('edit', row.id, row.parentName, row.parentId)
}

function delRow(rolesId) {
  ElMessageBox
    .confirm(`是否确定删除该条数据吗？删除不可恢复，请确认！`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      axios({
        url: "/menu/delete?id=" + rolesId,
        method: "post",
        serverName: "nd-base2"
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success(res.data.message);
          getDataList();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
}

// 批量删除
const mulDelete = () => {
  if (selectedPoints.value.length < 1) return ElMessage.warning("请勾选列表中要操作的信息！");
  ElMessageBox
    .confirm(`是否确定删除这${selectedPoints.value.length}数据吗？删除不可恢复，请确认！`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      axios({
        url: "/menu/deleteAll",
        method: "post",
        data: {
          idList: selectedPoints.value.map(item => item.id)
        },
        serverName: "nd-base2"
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success(res.data.message);
          getDataList();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
}
</script>

<style lang="scss" scoped>
:deep(.el-table th.el-table__cell) {
  color: #303133 !important;
}

:deep(.caozuoBtn) {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 5px 20px;
  line-height: normal;

  .is-disabled {
    color: #aaaaaa !important;
  }
}
</style>