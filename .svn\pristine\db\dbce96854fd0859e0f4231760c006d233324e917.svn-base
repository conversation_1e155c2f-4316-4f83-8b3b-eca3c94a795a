<template>
  <div class="box-top">
    <div class="title-box">
      <div class="line"></div>
      <div class="title" style="font-weight: bold">结算概览</div>
    </div>
    <div class="card-box">
      <div class="con-box">
        <div class="con-desc">待结算(元)</div>
        <div class="con-num">{{ page.data.preCheckoutAmountStr }}</div>
        <div class="con-detail">
          共<span class="blue">{{ page.data.preCheckoutCount }}</span
          >笔，本月<span class="blue">{{
            page.data.preCheckoutCountMonth
          }}</span
          >笔
        </div>
      </div>
      <div class="con-box1">
        <div class="con-desc">已结算(元)</div>
        <div class="con-num">{{ page.data.checkoutAmountStr }}</div>
        <div class="con-detail">
          共<span class="blue">{{ page.data.checkoutCount }}</span
          >笔，本月<span class="blue">{{ page.data.checkoutCountMonth }}</span
          >笔
        </div>
      </div>
      <div class="con-box2">
        <div class="con-desc">账户余额(元)</div>
        <div class="con-num">{{ page.data.balanceStr }}</div>
        <div class="con-detail">
          本月收入<span class="blue">{{ page.data.incomeMonthStr }}</span
          >元，支出<span class="blue">{{ page.data.expendMonthStr }}</span
          >元
        </div>
      </div>
    </div>
  </div>
  <div class="box">
    <div class="title-box">
      <div class="line"></div>
      <div class="title" style="font-weight: bold">订单结算明细</div>
    </div>
    <ndb-page-list>
      <template #tag>
        <nd-tag
          @click="tagClick('')"
          :checked="page.search.data.checkoutStatus === ''"
          >全部</nd-tag
        >
        <nd-tag
          @click="tagClick(0)"
          :checked="page.search.data.checkoutStatus === '0'"
          >待结算</nd-tag
        >
        <nd-tag
          @click="tagClick(1)"
          :checked="page.search.data.checkoutStatus === '1'"
          >已结算</nd-tag
        >
      </template>
      <template #search>
        <nd-search-more arrowMarginLeft="30px">
          <nd-search-more-item title="订单编号" width="120px">
            <nd-input
              type="text"
              v-model.trim="page.search.data.orderCode"
              width="100%"
              placeholder="请输入"
              resize="none"
            />
          </nd-search-more-item>
          <nd-search-more-item title="下单时间" width="120px">
            <nd-date-picker
              v-model="page.search.data.orderTimeArr"
              range-separator="至"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="请选择"
              width="100%"
            ></nd-date-picker>
          </nd-search-more-item>
          <nd-search-more-item title="订单完成时间" width="150px">
            <nd-date-picker
              v-model="page.search.data.completeTimeArr"
              range-separator="至"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="请选择"
              width="100%"
            ></nd-date-picker>
          </nd-search-more-item>
          <nd-search-more-item title="到账时间" width="120px">
            <nd-date-picker
              v-model="page.search.data.checkoutTimeArr"
              range-separator="至"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="请选择"
              width="100%"
            ></nd-date-picker>
          </nd-search-more-item>
          <template #footer>
            <nd-button type="primary" @click="getTableData"> 查询 </nd-button>
            <nd-button @click="reset"> 重置 </nd-button>
          </template>
        </nd-search-more>
      </template>
      <template #table>
        <div class="table-container">
          <!-- 表头行 -->
          <div class="table-row header-row">
            <div class="header-cell order-product">订单商品</div>
            <div class="header-cell order-product">费用项</div>
            <div class="header-cell">结算状态</div>
            <div class="header-cell">到账时间</div>
            <div class="header-cell order-product">备注</div>
            <div class="header-cell">操作</div>
          </div>

          <!-- 数据行 -->
          <div class="table-content">
            <div
              class="item-box"
              v-for="item in page.list.data"
              :key="item.orderCode"
            >
              <div class="row-indicator">
                <div class="row-item">
                  <div class="row-code">
                    订单编号：
                    <div class="order-num" @click.stop="openDetail(item)">
                      {{ item.orderCode }}
                      <img
                        @click.stop="copyClick(item.orderCode)"
                        class="copy-icon"
                        src="../../assets/images/settlement/copy.png"
                        alt=""
                      />
                    </div>
                  </div>
                  <div>订单状态：{{ item.statusStr }}</div>
                  <div>下单时间：{{ item.orderTime }}</div>
                  <div>订单完成时间：{{ item.finishTime }}</div>
                </div>
              </div>

              <div class="table-row">
                <div class="data-cell order-product">
                  <el-tooltip placement="top" :content="item.productName">
                    <div class="title">{{ item.productName }}</div>
                  </el-tooltip>

                  <!-- <div class="desc">商品编号：{{ item.productCode }}</div> -->
                  <div class="desc">商品数量:{{ item.amount }}斤</div>
                </div>
                <div class="data-cell order-product">
                  <div
                    class="money"
                    v-for="bill in item.orderBills"
                    :key="bill.type"
                  >
                    <div>{{ getBillType(bill.type) }}</div>
                    <div v-if="bill.type == 1" style="color: red">
                      <span>+</span>{{ bill.amountStr }}
                    </div>
                    <div
                      v-else-if="bill.type == 2 || bill.type == 3"
                      style="color: #068324"
                    >
                      <span>-</span>{{ bill.amountStr }}
                    </div>
                  </div>
                </div>

                <!-- 结算状态 -->
                <div class="data-cell">
                  <div
                    class="status"
                    v-for="bill in item.orderBills"
                    :key="bill.type"
                  >
                    <div :class="bill.status == 0 ? 'round' : 'round1'"></div>
                    <div>{{ bill.status == 0 ? "未结算" : "已结算" }}</div>
                  </div>
                </div>

                <!-- 到账时间 -->
                <div class="data-cell">
                  <div
                    class="status"
                    v-for="bill in item.orderBills"
                    :key="bill.type"
                  >
                    {{ bill.checkoutTime }}
                  </div>
                </div>

                <!-- 备注 -->
                <div class="data-cell order-product">
                  <el-tooltip
                    v-for="bill in item.orderBills"
                    :key="bill.type"
                    placement="top"
                    :content="bill.remark"
                  >
                    <div class="status1 remark-text">
                      {{ bill.remark }}
                    </div>
                  </el-tooltip>
                </div>
                <!-- <div class="data-cell order-product">
                <div
                  class="status"
                  v-for="bill in item.orderBills"
                  :key="bill.type"
                >
                  {{ bill.remark }}
                </div>
              </div> -->

                <!-- 操作 -->
                <div class="data-cell">
                  <div class="operation">
                    <div @click="openIncome(item)" v-if="item.showType">
                      收入结算
                    </div>
                    <div
                      @click="openExpense(item, item.showfw, item.showyz)"
                      v-if="item.showZc"
                    >
                      支出结算
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #page>
        <nd-pagination
          :current-page="page.pager.pageIndex"
          :page-size="page.pager.pageSize"
          :total="page.pager.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </template>
    </ndb-page-list>
  </div>
  <add-dialog ref="addDialogRef" @before-close="getTableData"></add-dialog>
  <income ref="incomeDialogRef" @before-close="getTableData"></income>
  <expense ref="expenseDialogRef" @before-close="getTableData"></expense>
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";

// 导入vue
import { reactive, ref, inject, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { onMounted } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();
//导入子组件
import addDialog from "@/views2/purchaseOrderView/components/addDialog.vue";
import income from "./components/income.vue";
import expense from "./components/expense.vue";

// 定义axios
const $axios = inject("$axios");

// 定义ref
const incomeDialogRef = ref(null);
const expenseDialogRef = ref(null);

// 定义page
const page = reactive({
  search: {
    data: {
      orderCode: "", //订单编号
      checkoutStatus: "", //结算状态
      orderTimeArr: ["", ""], //下单时间
      completeTimeArr: ["", ""], //完成时间
      checkoutTimeArr: ["", ""], //到账时间
    },
  },
  data: {
    balance: "",
    balanceStr: "",
    checkoutAmount: "",
    checkoutAmountStr: "",
    checkoutCount: "",
    checkoutCountMonth: "",
    expendMonth: "",
    expendMonthStr: "",
    incomeMonth: "",
    incomeMonthStr: "",
    preCheckoutAmount: "",
    preCheckoutAmountStr: "",
    preCheckoutCount: "",
    preCheckoutCountMonth: "",
  },
  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  },
});

onMounted(() => {
  let query = route.query;
  if (query.orderCode) {
    page.search.data.orderCode = query.orderCode;
  }
  getDataTotal();
  getTableData();
});

watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.orderCode) {
      page.search.data.orderCode = newQuery.orderCode;
      getTableData();
    } else {
      page.search.data.orderCode = "";
      getTableData();
    }
  }
);

//切换
function tagClick(index) {
  page.search.data.checkoutStatus = index === 0 ? "0" : index === 1 ? "1" : "";
  getTableData();
}

// 获得表格数据
function getTableData() {
  let params = {
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    orderCode: page.search.data.orderCode,
    checkoutStatus: page.search.data.checkoutStatus,
    orderTimeStart: page.search.data.orderTimeArr
      ? page.search.data.orderTimeArr[0]
      : "",
    orderTimeEnd: page.search.data.orderTimeArr
      ? page.search.data.orderTimeArr[1]
      : "",
    completeTimeStart: page.search.data.completeTimeArr
      ? page.search.data.completeTimeArr[0]
      : "",
    completeTimeEnd: page.search.data.completeTimeArr
      ? page.search.data.completeTimeArr[1]
      : "",
    checkoutTimeStart: page.search.data.checkoutTimeArr
      ? page.search.data.checkoutTimeArr[0]
      : "",
    checkoutTimeEnd: page.search.data.checkoutTimeArr
      ? page.search.data.checkoutTimeArr[1]
      : "",
  };
  $axios({
    url: "/orderBill/page",
    method: "get",
    serverName: "nd-base2",
    params,
  })
    .then((res) => {
      if (res.data.code === 2000) {
        page.list.data = res.data.data.records;
        const statusMap = {
          0: "待出货",
          1: "待收货",
          2: "已完成",
          3: "已取消",
        };
        page.list.data.forEach((item) => {
          item.statusStr = statusMap[item.status] || "未知状态";
          item.showType = item.orderBills.some(
            (item2) => item2.type === 1 && item2.status === 0
          );
          item.showZc = item.orderBills.some(
            (item2) =>
              (item2.type === 2 || item2.type === 3) && item2.status === 0
          );
          item.showfw = item.orderBills.some(
            (item2) => item2.type === 2 && item2.status === 1
          );
          item.showyz = item.orderBills.some(
            (item2) => item2.type === 3 && item2.status === 1
          );
        });

        page.pager.total = res.data.data.total; //总页数
        page.pager.pageIndex = res.data.data.current; //当前页
        page.pager.pageSize = res.data.data.size; //每页记录数
      } else {
        ElMessage.error(res.data.message);
      }
    })
    .catch(() => {});
}

function getBillType(type) {
  switch (type) {
    case 1:
      return "货款";
    case 2:
      return "养殖户费用";
    default:
      return "交易服务费";
  }
}

//数据总览
function getDataTotal() {
  $axios({
    url: "/orderBill/overview",
    method: "get",
    serverName: "nd-base2",
  })
    .then((res) => {
      if (res.data.code === 2000) {
        page.data.balance = res.data.data.balance || "";
        page.data.balanceStr = res.data.data.balanceStr || "";
        page.data.checkoutAmount = res.data.data.checkoutAmount || "";
        page.data.checkoutAmountStr = res.data.data.checkoutAmountStr || "";
        page.data.checkoutCount = res.data.data.checkoutCount || "";
        page.data.checkoutCountMonth = res.data.data.checkoutCountMonth || "";
        page.data.expendMonth = res.data.data.expendMonth || "";
        page.data.expendMonthStr = res.data.data.expendMonthStr || "";
        page.data.incomeMonth = res.data.data.incomeMonth || "";
        page.data.incomeMonthStr = res.data.data.incomeMonthStr || "";
        page.data.preCheckoutAmount = res.data.data.preCheckoutAmount || "";
        page.data.preCheckoutAmountStr =
          res.data.data.preCheckoutAmountStr || "";
        page.data.preCheckoutCount = res.data.data.preCheckoutCount || "";
        page.data.preCheckoutCountMonth =
          res.data.data.preCheckoutCountMonth || "";
      } else {
        ElMessage.error(res.data.message);
      }
    })
    .catch(() => {});
}

const addDialogRef = ref(null);
//打开订单详情
function openDetail(params) {
  addDialogRef.value.open(params, "detail", true);
}

//复制
const copyClick = async (val) => {
  let copyText = val;
  var textarea = document.createElement("textarea"); // 创建textarea对象
  textarea.value = copyText; // 设置复制内容
  document.body.appendChild(textarea); // 添加临时实例
  textarea.select(); // 选择实例内容
  document.execCommand("Copy"); // 执行复制
  document.body.removeChild(textarea); // 删除临时实例
  ElMessage.success("复制成功");
};

// 收入
function openIncome(row) {
  incomeDialogRef.value.open(row);
}
//支出
function openExpense(row, flag, yzFlag) {
  expenseDialogRef.value.open(row, flag, yzFlag);
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

// 重置
function reset() {
  page.search.data.orderCode = "";
  page.search.data.checkoutStatus = "";
  page.search.data.orderTimeArr = ["", ""];
  page.search.data.completeTimeArr = ["", ""];
  page.search.data.checkoutTimeArr = ["", ""];
  getTableData();
}
</script>

<style lang="scss" scoped>
.box-top {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 12px;
  background-color: #fff;
  .title-box {
    display: flex;
    align-items: center;
    gap: 8px;
    .line {
      width: 2px;
      height: 16px;
      background: #068324;
    }
    .tltle {
      font-size: 16px;
      font-weight: bold;
      color: #444444;
    }
  }
  .card-box {
    display: flex;
    justify-content: space-evenly;
    gap: 20px;
    .con-box,
    .con-box1,
    .con-box2 {
      width: 100%;
      height: 148px;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 200px;
      gap: 10px;
      background-size: cover;
      background-repeat: no-repeat;
    }

    .con-box {
      background-image: url("@/assets/images/settlement/bg1.png");
    }

    .con-box1 {
      background-image: url("@/assets/images/settlement/bg2.png");
    }

    .con-box2 {
      background-image: url("@/assets/images/settlement/bg3.png");
    }

    .con-desc {
      font-size: 16px;
      color: #666666;
    }

    .con-num {
      font-size: 30px;
      color: #333333;
      font-weight: bold;
    }

    .con-detail {
      font-size: 14px;
      color: #999999;
      .blue {
        color: #0098ff;
        padding: 0 5px;
      }
    }
  }
}
.box {
  background-color: #fff;
  padding: 12px;
  height: calc(100% - 218px);
  .title-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 8px;
    .line {
      width: 2px;
      height: 16px;
      background: #068324;
    }
    .tltle {
      font-size: 16px;
      font-weight: bold;
      color: #444444;
    }
  }
}
.table-container {
  width: 100%;
  margin: auto;
  border-collapse: collapse;
  border-bottom: 1px solid #ededed;
  border-left: 1px solid #ededed;
  border-right: 1px solid #ededed;
  height: calc(100% - 29px);

  .table-row {
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: inset 0 -1px 0 0 #ededed, inset 0 1px 0 0 #ededed;
    .header-cell {
      color: #303133;
      font-size: 14px;
      width: 180px;
      // padding: 8px 14px 14px 12px;
      text-align: center;
    }
    .data-cell {
      color: #303133;
      width: 180px;
      padding: 8px 14px 14px 12px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      min-height: 100px;
      flex-grow: 1;
      border-right: 1px solid #ededed;
      .title {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .desc {
        color: #666666;
        font-size: 14px;
      }
      .money {
        display: flex;
        justify-content: space-between;
        color: #555;
        font-size: 14px;
      }
      .status {
        display: flex;
        color: #555;
        font-size: 14px;
        align-items: center;
        justify-content: center;
        min-height: 19px;
        .round {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: red;
          margin-right: 10px;
        }
        .round1 {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #068324;
          margin-right: 10px;
        }
      }
      .status1 {
        display: flex;
        color: #555;
        font-size: 14px;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .remark-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        min-height: 19px;
      }
      .status1:hover {
        text-overflow: clip;
      }
      //   .status1:hover {
      //     white-space: normal;
      //     overflow: visible;
      //     text-overflow: clip;
      //   }
    }
    .order-product {
      width: 300px;
    }
  }
  .header-row {
    background-color: #f9f9f9;
    height: 38px;
  }
  .table-content {
    height: calc(100% - 29px);
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0bcbc;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #cfcdcd;
    }
    .row-indicator {
      margin-top: 16px;
      background-color: #f8fffa;
      border-top: 1px solid #ededed;
      height: 38px;
      width: 100%;
      .row-item {
        display: flex;
        font-size: 14px;
        gap: 16px;
        height: 100%;
        padding-left: 10px;
        align-items: center;
        color: #303133;
        .row-code {
          display: flex;
          height: 100%;
          align-items: center;
          .order-num {
            color: #0098ff;
            text-decoration: underline;
            display: flex;
            .copy-icon {
              width: 18px;
              height: 18px;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }
}

.operation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #0098ff;
  font-size: 14px;

  div {
    margin: 3px 0;
    cursor: pointer;
  }
}

:deep(.footer-box) {
  position: relative;
  top: -30px;
  background: #f9f9f9;
}

:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
}
</style>
<style lang="css">
.el-popper {
  max-width: 300px;
}
</style>
