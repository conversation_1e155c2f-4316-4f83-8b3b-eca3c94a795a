<template>
    <div>
        <ndDialog
            ref="fileImportDialog"
            width="800px"
            height="340px"
            :title="data.title"
            append-to-body
            :before-close="hsDetailClose"
            align-center
        >
            <div class="main">
                <div class="main-box">
                    <div class="from">
                        <div class="fromItem">
                            <p>
                                <span style="color: red">*</span
                                >{{ data.title }}文件
                            </p>
                            <div
                                style="
                                    width: 76%;
                                    display: flex;
                                    align-items: center;
                                "
                            >
                                <ndInput
                                    width="66%"
                                    disabled
                                    v-model="data.fileName"
                                ></ndInput>
                                <el-upload
                                    class="upload-demo"
                                    action="#"
                                    :auto-upload="false"
                                    :on-change="fileSelected"
                                >
                                    <!-- :http-request="pcUpload" -->
                                    <nd-button type="primary">浏览</nd-button>
                                </el-upload>
                            </div>
                        </div>
                        <div class="fromItem">
                            <div
                                class="operation"
                                style="margin-left: 23%"
                                @click="exportExcel"
                            >
                                <el-icon>
                                    <Download />
                                </el-icon>
                                下载导入模板
                            </div>
                            <div
                                class="operation"
                                style="margin-left: 10px; text-align: right"
                                @click="openImport"
                            >
                                <el-icon>
                                    <View />
                                </el-icon>
                                查看上次导入结果
                            </div>
                        </div>
                    </div>
                    <!-- 重要说明 -->
                    <div class="importantExplanation">
                        <div class="title">重要说明：</div>
                        <div class="explanation">
                            <p
                                v-for="(item, index) in prop.importantText"
                                :key="index"
                            >
                                {{ item }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <nd-button type="primary" @click="fileImport"
                    >确定导入</nd-button
                >
                <nd-button @click="hsDetailClose">取消</nd-button>
            </template>
        </ndDialog>
        <!-- 导入记录 -->
        <ImportRecord
            ref="record"
            @farOpen="farOpen"
            @again="again"
        ></ImportRecord>
    </div>
</template>

<script setup>
import ndDialog from "@/components/ndDialog.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ImportRecord from "./components/ImportRecord.vue";
import ndSelect from "@/components/ndSelect.vue";

import { ref, inject, reactive, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

const $axios = inject("$axios");
const emit = defineEmits(["farOpen1", "upbates"]);
// 变量
const searchData = reactive({
    projectNameList: [],
});
const data = reactive({
    title: "",
    orgName: "",
    orgId: "",
    dataList: [],
    fileName: "",
    fileBinary: {},
    projectId: "",
    type: "",
});
const prop = defineProps({
    // 是否只读
    projectId: {
        type: String,
        default: "",
    },
    dhbtId: {
        type: String,
        default: "",
    },

    // 类型，唯一，由后端具体业务提供
    type: {
        type: String,
        default: "",
    },
    //标题
    titles: {
        type: String,
        default: "导入",
    },
    // 模板文件标题
    templateFileTitle: {
        type: String,
        default: "模板文件",
    },
    importantText: {
        type: Array,
        default: [
            "1、请按导入模板要求整理数据。",
            "2、每次导入的数据均是在已有数据基础上更新或追加。",
            "3、若有多人同时导入或导入数据量过大，系统会排队处理，您无需一直停留在该界面等待导入结果。",
        ],
    },
    modeType: {
        type: String,
        default: "",
    },
});
// 获取数据
onMounted(() => {
    data.title = prop.titles;
    // console.log(prop.titles,'prop.titles')
    // if (prop.type == "PRO_POINT") {
    //   data.title = "导入功能点";
    // } else {
    //   data.title = "导入任务";
    // }
});
const querySearchAsync = (queryString, cb) => {
    var params = {
        orgName: queryString,
    };
    $axios({
        url: "/memberModify/searchOrgByParam",
        method: "get",
        // serverName: 'gq-base',
        data: params,
    }).then((res) => {
        if (res.data.code === 2000) {
            res.data.data.forEach((element) => {
                element["value"] = element.orgName;
            });
            data.dataList = res.data.data;
            const results = res.data.data;
            cb(results);
        }
    });
};

// 浏览文件上传
const fileSelected = (file) => {
    console.log(file);
    data.fileName = "";
    data.fileBinary = {};
    let fileSize = Number(file.size / 1024 / 1024);
    //获取文件格式
    let fileTypeLen = file.raw.name.split(".");
    let fileType = fileTypeLen[fileTypeLen.length - 1].toLowerCase(); //转化成小写
    if (fileType !== "xls" && fileType !== "xlsx") {
        ElMessage({
            message: "只能上传xls或xlsx文件，请重新上传",
            type: "warning",
        });
        return;
    } else {
    }
    data.fileName = file.name;
    data.fileBinary = file.raw;
};

const autocompleteRef = ref(null);
// 点击组织，判断组织是否配置股权类型
const handleSelect = (item) => {
    $axios({
        url: "/memberRegister/getOrgMsg",
        method: "get",
        serverName: "gq-base",
        data: { orgId: item.orgId },
    }).then((res) => {
        if (res.data.code === 2000) {
            data.orgId = item.orgId;
        } else {
            ElMessageBox.confirm(res.data.message, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    data.orgName = "";
                    autocompleteRef.value.blur();
                })
                .catch(() => {
                    data.orgName = "";
                    autocompleteRef.value.blur();
                });
        }
    });
};

// 确定导入
const fileImport = () => {
    // let nowProject = searchData.projectNameList.filter(
    //     (item) => item.projectId == data.orgName
    // );
    if (JSON.stringify(data.fileBinary) === "{}") {
        ElMessageBox.confirm("请选择上传的文件！", "提示", {
            confirmButtonText: "确定",
            showCancelButton: false,
            type: "warning",
        })
            .then(() => {
                return;
            })
            .catch(() => {
                return;
            });
        return;
    } else {
        let params = new FormData();
        params.append("file", data.fileBinary);
        if (prop.dhbtId) {
            params.append("dhbtId", prop.dhbtId);
        }
        $axios({
            url: "/import/excel/" + prop.modeType,
            method: "post",
            serverName: "nd-base2",
            data: params,
        }).then((res) => {
            if (res.data.code === 2000) {
                data.fileName = "";
                data.dataList = [];
                data.fileBinary = {};
                emit("farOpen1");
                fileImportDialog.value.close();
                record.value.open(prop.modeType, data.title);
            } else {
                ElMessage({
                    message: res.data.message,
                    type: "warning",
                });
            }
        });
    }
};

const fileImportDialog = ref(null);
const getList = () => {
    $axios({
        url: "/import/result/",
        method: "get",
    }).then((res) => {
        if (res.data.code == 200) {
            searchData.projectNameList = res.data.data; //
            if (prop.projectId) {
                data.orgName = prop.projectId.toString();
            }
        }
    });
};

/**
 * 打开函数
 * @returns {void}
 */
const open = () => {
    data.orgId = "";
    data.orgName = "";
    data.fileName = "";
    data.fileBinary = {};
    // getList();
    fileImportDialog.value.open();
};

/**
 * 弹窗调用方法
 */
function again() {
    hsDetailClose();
}

/**
 * 关闭函数
 * @returns {void}
 */
const hsDetailClose = () => {
    emit("upbates");
    data.orgId = "";
    data.orgName = "";
    data.dataList = [];
    data.fileName = "";
    data.fileBinary = {};
    fileImportDialog.value.close();
};

const record = ref(null);
// 打开导入记录
const openImport = () => {
    fileImportDialog.value.close();
    record.value.open(prop.modeType, data.title);
};

/**
 * 下载导入模板函数
 * @returns {void}
 */
const exportExcel = () => {
    $axios({
        url: "/import/downloadModel",
        method: "get",
        serverName: "nd-base2",
        responseType: "blob",
        data: {
            modeType: prop.modeType,
        },
    }).then((r) => {
        if (!r.data) return;
        const link = document.createElement("a");
        let blob = new Blob([r.data], {
            type: "application/vnd.ms-excel",
        });
        link.href = URL.createObjectURL(blob);
        const nameData = decodeURIComponent(r.headers["content-disposition"]);
        const match = nameData.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        link.download = match[1].replace(/['"]/g, "");
        link.click();
        link.remove();
    });
};

const farOpen = () => {
    console.log(data.orgId, "data.orgId");
    // data.orgId = "";
    // data.orgName = "";
    data.dataList = [];
    data.fileName = "";
    data.fileBinary = {};
    // getList()
    fileImportDialog.value.open();
};
// 方法导出，便于父组件接收并且调用
defineExpose({
    open,
});
</script>

<style lang="scss" scoped>
:deep(.el-upload-list--text) {
    display: none;
}

.operation {
    font-size: 14px;
    font-weight: normal;
    color: #0b8df1;
    cursor: pointer;
    display: inline-block;
    margin-right: 8px;
    display: flex;
    align-items: center;

    .el-icon {
        margin-right: 3px;
        font-size: 12px;
    }
}

.from {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    // margin-top: 50px;

    .fromItem {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 10px;

        .upload-demo {
            display: flex;
            margin-left: 5px;
        }

        > p {
            text-align: right;
            width: 22%;
            margin-right: 5px;
            font-size: 14px;
            color: #555;
        }
    }
}

.importantExplanation {
    margin-top: 16px;
    padding: 0 75px;

    .title {
        font-size: 12px;
        font-weight: bold;
        line-height: 20px;
        color: #606266;
    }

    .explanation {
        p {
            font-size: 12px;
            font-weight: normal;
            line-height: 20px;
            color: #909399;
        }
    }
}

.main {
    padding: 12px;
    height: 100%;
    .main-box {
        height: 100%;
        padding: 12px;
        background: #fff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
    }
}
</style>
