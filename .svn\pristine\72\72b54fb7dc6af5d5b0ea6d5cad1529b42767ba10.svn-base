<template>
    <!-- // 订单处理 -->
    <nd-dialog ref="agreeDialogRef" width="1000px" height="48vh" title="查看物流" align-center>
      <div class="logistics-dialog-box">
         <nd-table :data="wlDateInfo" style="width: 100%;height: 100%;" row-class-name="home-service-work-no-row" cell-class-name="home-service-work-no-cell" :header-cell-style="{ 'text-align': 'center' }">
        <el-table-column align="center" label="序号" type="index" width="60px"></el-table-column>
            <el-table-column label="物流单号/司机联系方式" width="200" prop="wlgs" align="left">
            <template #default="scope">
                <el-tooltip :content="scope.row.wlbh" placement="top-start">
                <div @click="viewShipmentRequest"  v-if="scope.row.type == 1" class="operation mfAbq">
                    {{ scope.row.wlbh }}
                </div>
                <template v-if="scope.row.type == 2">
                    {{ scope.row.wlbh }}
                </template>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column label="商品名称" width="200" prop="name" align="left">
            <template #default="scope">
              <div class="order-box">
                <div>{{ scope.row.name }}</div>
                <div>{{ scope.row.spData }}</div>
              </div>
            </template>
        </el-table-column>
        <el-table-column label="数量" width="120" prop="nums" align="center">
        </el-table-column>
        <el-table-column label="状态" width="100" prop="tkje" align="center">
          <template #default="scope">
            <template v-if="scope.row.sffh== 1">已发货</template>
            <template v-if="scope.row.sffh == 0">未发货</template>
          </template>
        </el-table-column>
        <el-table-column label="发货方式" width="150" prop="type" align="center">
            <template #default="scope">
            <template v-if="scope.row.type== 1">第三方物流发货</template>
            <template v-else-if="scope.row.type== 2">商家自行发货</template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column label="发货日期" width="120" prop="fhrq" align="center">
        </el-table-column>
        <el-table-column label="物流公司/司机姓名" width="160" prop="wlgs" align="center">
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="scope">
            <div v-if="scope.row.type == 1">
              <div class="operation">
                <div @click="viewShipmentRequest">查看物流</div>
              </div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
      </nd-table>
      </div>
        <template #footer>
            <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button>
        </template>
    </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndTable from "@/components/ndTable.vue";

// 导入element-plus方法
import { ElMessage as elMessage } from "element-plus";
// 导入vue
import { ref,inject } from "vue";

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const agreeDialogRef = ref(null);
const wlDateInfo=ref([])
// 定义axios
const $axios = inject("$axios");
// 打开弹窗
function open(orderId) {
    agreeDialogRef.value.open();
    getDetail(orderId)
}
// 获得详情
function getDetail(orderId) {
  $axios({
    url: "/sh/getOrderWlList",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: orderId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      wlDateInfo.value = res.data.data;
    }
  });
}
// 清空表单
const clear = () => {
};

// 关闭弹窗 ==============================================================
const close = () => {
    agreeDialogRef.value.close();
    clear();
};
// 查看物流
const viewShipmentRequest = () => {
  elMessage({
    message: "功能开发中...",
    type: "warning",
  });
};
// 暴露方法给父组件 =======================================================
defineExpose({
    open,
    clear,
});
</script>

<style lang="scss" scoped>
.logistics-dialog-box{
    height: 100%;
    padding: 10px;
    background: #fff;
}
.operation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #0098FF;
  font-size: 14px;

  div {
    margin: 3px 0;
    cursor: pointer;
  }
}
//超链
.mfAbq {
  text-decoration: underline;
  cursor: pointer;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp:1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}
.order-box{
  div{
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp:1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
  }
}
</style>