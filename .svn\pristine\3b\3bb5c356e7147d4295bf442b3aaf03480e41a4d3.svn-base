<template>
  <div class="nd-tree-box">
    <el-tree v-bind="$attrs">
      <template #default="{ node, data }">
        <slot name="default" :node="node" :data="data"></slot>
      </template>
    </el-tree>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.nd-tree-box {
  :deep(.el-tree-node__content) {
    margin-bottom: 0px !important;
    height: 32px;
  }

  :deep(.el-tree-node__label) {
    font-size: 12px;
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: #c8ecff;
  }

  :deep(.el-tree-node__content) {
    margin-bottom: 10px;

    &:hover {
      background-color: #e8f7ff;
    }
  }

    :deep(.el-checkbox){
    .el-checkbox__inner:hover{
      border-color: #068324;
    }
    .is-indeterminate{
      .el-checkbox__inner{
        background-color: #068324 !important;
        border-color: #068324;
      }
    }
    .is-checked{
      .el-checkbox__inner{
        background-color: #068324 !important;
        border-color: #068324;
      }
    }
  }
}
</style>
