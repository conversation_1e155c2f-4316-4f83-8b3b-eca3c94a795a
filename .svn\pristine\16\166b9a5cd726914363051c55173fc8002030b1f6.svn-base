<template>
  <nd-dialog ref="dialogRef" :title="page.title" width="60vw">
    <el-form
      :model="formData"
      :rules="rules"
      label-width="120px"
      ref="formRef"
      style="
        padding: 12px;
        background-color: #fff;
        margin: 12px;
        border-radius: 5px;
        border: 1px solid #eaeaea;
      "
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="数据字典名称" prop="dictValue" required>
            <nd-input
              v-model.trim="formData.dictValue"
              placeholder="如身份证"
              maxlength="30"
              clearable
              style="width: 100%"
              @blur="handleInput(1, formData.dictValue)"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="数据字典标识" prop="dictKey" required>
            <nd-input
              v-model="formData.dictKey"
              placeholder="SFZ"
              maxlength="30"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="!page.isChecked">
          <el-form-item label="数据类型名称" prop="dictName" required>
            <nd-input
              v-model="formData.dictName"
              placeholder="如证件类型"
              maxlength="30"
              clearable
              style="width: 100%"
              @blur="handleInput(2, formData.dictName)"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="!page.isChecked">
          <el-form-item label="数据类型标识" prop="dictMark" required>
            <nd-input
              v-model="formData.dictMark"
              placeholder="ZJLX"
              clearable
              style="width: 100%"
              maxlength="30"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="page.isChecked">
          <el-form-item label="所属上级" prop="dictPidName">
            <nd-input
              readonly
              v-model="formData.dictPidName"
              placeholder="请输入所属上级"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="排序" required prop="orderCode">
            <nd-input
              v-model="formData.orderCode"
              placeholder="请输入排序"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="字典描述">
            <nd-input
              v-model="formData.describes"
              maxlength="300"
              type="textarea"
              placeholder="请输入字典描述"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <nd-button type="primary" @click="save">提交</nd-button>
      <nd-button @click="close">取消</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";

import { ref, reactive, watch } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import axios from "axios";

const formRef = ref(null);
const dialogRef = ref(null);
const formData = reactive({
  dictName: "",
  dictKey: "",
  dictValue: "",
  dictMark: "",
  dictPid: "",
  orderCode: "",
  describes: "",
  dictPidName: "", //所属上级
});

//校验
const rules = {
  dictName: [{ required: true, message: "请输入数据类型名称", trigger: "blur" }],
  dictKey: [{ required: true, message: "请输入数据字典标识", trigger: "blur" }],
  dictValue: [{ required: true, message: "请输入数据字典名称", trigger: "blur" }],
  dictMark: [{ required: true, message: "请输入数据类型标识", trigger: "blur" }],
  orderCode: [
    { required: true, message: "请输入排序", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === "") return callback();
        const regex = /^[1-9]\d*$/;
        if (!regex.test(value)) {
          callback(new Error("只能输入正整数"));
        } else if (value > 999999999) {
          callback(new Error("不能大于999999999"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

//输入
const handleInput = (num, val) => {
  getspellName(num, val);
  console.log(val, "00000");
};
const getspellName = (num, val) => {
  axios({
    url: "/dict/findPinyin",
    method: "get",
    serverName: "nd-base2",
    params: { name: val },
  }).then((res) => {
    if (res.data.code === 2000) {
      if (num === 1) {
        formData.dictKey = res.data.data;
      } else if (num === 2) {
        formData.dictMark = res.data.data;
      }
    }
  });
};

const emit = defineEmits(["before-close", "success"]);

const page = reactive({
  type: "",
  title: "",
  id: "",
  isChecked: false,
  dictPidName: "",
});

const open = (model, isChecked, row, dictId, dictName, dictMark, dictValue) => {
  resetForm();
  page.id = "";

  if (model === "edit") {
    page.type = "edit";
    page.title = "编辑";
    page.id = row.dictId;
    page.isChecked = true;
    getDetail();
  } else {
    page.type = "add";
    page.title = "新增";
    page.isChecked = isChecked;
    formData.orderCode = page.isChecked ? "" : "1";
    if (dictId) {
      formData.dictPid = dictId;
      formData.dictPidName = dictValue;
      formData.dictMark = dictMark;
      formData.dictName = dictValue;
    }
    console.log(page.isChecked, "isChecked");
  }
  dialogRef.value.open();
};

// 获取详情
function getDetail() {
  axios({
    url: "/dict/find",
    method: "get",
    serverName: "nd-base2",
    params: { dictId: page.id },
  }).then((res) => {
    if (res.data.code === 2000) {
      formData.dictName = res.data.data.dictName;
      formData.dictKey = res.data.data.dictKey;
      formData.dictValue = res.data.data.dictValue;
      formData.dictMark = res.data.data.dictMark;
      formData.orderCode = res.data.data.orderCode;
      formData.describes = res.data.data.describes;
      formData.dictPid = res.data.data.dictPid;
      formData.dictPidName = res.data.data.dictName;
    }
  });
}

function save() {
  // 校验
  formRef.value.validate((valid) => {
    if (valid) {
      const paramsData = {
        dictId: page.id,
        dictName: formData.dictName,
        dictKey: formData.dictKey,
        dictValue: formData.dictValue,
        dictMark: formData.dictMark,
        dictPid: formData.dictPid ? formData.dictPid : 0,
        orderCode: formData.orderCode,
        describes: formData.describes,
      };
      if (page.type === "edit") {
        editData(paramsData);
      } else {
        addData(paramsData);
      }
    }
  });
}

//新增
function addData(paramsData) {
  axios({
    url: "/dict/save",
    method: "post",
    data: paramsData,
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("保存成功！");
      emit("before-close");
      close();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

//编辑
function editData(paramsData) {
  axios({
    url: "/dict/update",
    method: "post",
    data: paramsData,
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("保存成功！");
      emit("before-close");
      close();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

// 重置表单方法
const resetForm = () => {
  formData.dictName = "";
  formData.dictKey = "";
  formData.dictValue = "";
  formData.dictMark = "";
  formData.dictPid = "";
  formData.orderCode = "";
  formData.describes = "";
};
const close = () => {
  resetForm();
  dialogRef.value.close();
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
</style>
