import { reactive } from "vue";
import axios from "@/http/index";
import { ElMessage } from "element-plus";

export class DialogData {
    /** @type {string} */
    title;
}

export function useDialog({ dialogRef = null, cancelFormRef = null, emit = null }) {

    const dialogData = reactive({
        title: "取消",

    })

    const cancelParams = reactive({
        demandId: "",
        cancelReason: ""
    })

    const cancelRules = reactive({
        cancelReason: [
            { required: true, message: "请输入取消原因", trigger: "blur" }
        ]
    })

    /**
     * open
     * 
     * @returns {void}
     */
    const open = (demandId) => {
        cancelParams.demandId = demandId;
        dialogRef && dialogRef.value.open();
    }

    /**
     * close
     * 
     * @returns {void}
     */
    const close = () => {
        console.log("close");

        cancelParams.cancelReason = "";
        cancelParams.demandId = "";

        dialogRef && dialogRef.value.close();
    }

    /**
     * cancel
     * 
     * @returns {void}
     */
    const cancel = () => {
        console.log("cancel");

        cancelFormRef && cancelFormRef.value.validate(e => {
            console.log(e);

            if (e) {
                axios({
                    url: "/buy/demand/cancel",
                    method: "POST",
                    serverName: "nd-base2",
                    data: {
                        demandId: cancelParams.demandId,
                        cancelReason: cancelParams.cancelReason,
                        cancelRole: 3
                    }
                }).then(r => {
                    if (r.data.code !== 2000) {
                        ElMessage.error(r.data.message);
                        return;
                    }

                    emit && emit("refresh");

                    close();
                })
            }
        })
    }

    return {
        dialogData,
        cancelParams,
        cancelRules,
        open,
        close,
        cancel
    }
}