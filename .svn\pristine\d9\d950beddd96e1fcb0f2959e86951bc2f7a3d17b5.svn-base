<template>
    <ndDialog ref="downloadCenterDialog" width="600px" height="200px" :title="data.title" append-to-body :before-close="hsDetailClose" align-center>
        <div class="exportUploadBox">
            <!-- 提示 -->
            <div class="remind" v-if="data.loading">
                <p>· 当您导出的数据量较大时，需要较长时间进行处理，退出本功能不影响导出指令。</p>
                <p>· 若想快速导出数据，可以缩小数据范围并重新提交导出任务。</p>
            </div>
            <div class="success" v-if="!data.loading && data.success">
                <img :src="success" alt="">
                <p>导出数据已生成</p>
            </div>
            <div class="warning" v-if="!data.loading && !data.success">
                <img :src="noPass" alt="">
                <p>导出数据失败</p>
            </div>
            <div class="load" v-if="data.loading">
                <div v-loading="data.loading" element-loading-text="导出文件生成中"></div>
            </div>
        </div>
        <template #footer>
            <!-- <ndButton type="primary" @click="exportExcel" v-if="!data.loading && data.success">下载导出结果</ndButton> -->
            <ndButton @click="hsDetailClose">关闭</ndButton>
        </template>
    </ndDialog>
</template>

<script setup>
import ndDialog from '@/components/ndDialog.vue';
import ndButton from '@/components/ndButton.vue';
import { ref, inject, reactive, watch, onMounted, onUpdated } from 'vue';
import success from '@/assets/images/agree.png'
import noPass from '@/assets/images/noPass.png'
import { ElMessage } from 'element-plus'
const $axios = inject('$axios');
// 变量
const data = reactive({
    title: '导出',
    query: {},
    loading: false,
    size: '15px',
    color: '#33a954',
    types: '',
    logId: '',
    success: false
})

const downloadCenterDialog = ref(null)

const open = (types, logId) => {
    data.types = types;
    data.logId = logId;
    getRecord(types, logId);
    downloadCenterDialog.value.open();
}
// 获取导出记录
const getRecord = (types, logId) => {
    data.loading = true
    let params = {
        // types: types,
        logId: logId
    }
    // data.loading = true
    $axios({
        url: "/export/result",
        method: "get",
        // serverName: 'gq-base',
        data: params
    }).then((res) => {
        if (res.data.code === 2000) {
            if (res.data.data[0].done && res.data.data.length > 0) {
                data.loading = false
                if (res.data.data[0].status) {
                    data.success = true
                    data.filePath = res.data.data[0].filePath
                    exportExcel();
                } else {
                    data.success = false
                    ElMessage({
                        message: '导出失败',
                        type: 'warning',
                    })
                }
            } else {
                setTimeout(() => {
                    getRecord(data.types, data.logId)
                }, 1000);
            }
        } else {
            data.loading = false
        }
    })
}
// 下载文件
const exportExcel = () => {
    window.location.href = data.filePath
}
const hsDetailClose = () => {
    downloadCenterDialog.value.close()
}
// 方法导出，便于父组件接收并且调用
defineExpose({
    open
})
</script>

<style lang="scss" scoped>
.exportUploadBox {
    width: 100%;
    height: 100%;
    padding: 5px 5px;

    .remind {
        background-color: #E1F0FF;
        color: #76bffc;
        font-size: 14px;
        padding: 5px 10px;
    }

    .success,
    .warning {
        height: 70%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .load {
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;

        :deep(.el-loading-parent--relative) {
            width: 100%;
        }
    }
}
</style>