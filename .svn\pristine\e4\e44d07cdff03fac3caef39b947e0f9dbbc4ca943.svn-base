<template>
  <nd-dialog ref="initialPassword" width="1200px" height="600px" title="设置密码" :showFooter="false" :show-close="false" align-center>
    <div class="dialpass">设置密码</div>
    <div class="dialpass-font">您正在用初始化密码登录系统，为了确保账户安全，请您设置登录密码；设置密码前，将无法访问系统。</div>
    <div class="main">
      <img class="main-img" src="@/assets/images/loginView/lock.png" alt="" />
      <div class="form">
        <div style="display: flex; justify-content: space-between">
          <div>
            当前登录账号&nbsp;&nbsp;<span class="userlight">{{ sizeForm.account }}</span>
          </div>
          <div>
            用户姓名&nbsp;&nbsp;<span class="userlight">{{ sizeForm.username }}</span>
          </div>
        </div>
        <el-form ref="ruleFormRef" :model="sizeForm" :rules="rules" label-width="auto" label-position="top" style="margin-top: 51px">
          <el-form-item label="新密码" error="" prop="password" :required="true">
            <el-input v-model="sizeForm.password" type="password" show-password />
          </el-form-item>
          <el-form-item label="确认密码" placeholder="" error="" prop="repeatPassword" :required="true">
            <el-input v-model="sizeForm.repeatPassword" type="password" show-password />
          </el-form-item>
          <div class="passrule" :class="sizeForm.topFlag ? 'top10' : ''">
            密码长度为8~15位，需包括特殊字符（如%、@、&、#等），且需包含大写字母、小写字母、数字中的两种或以上，不允许有空格。（例SSp@8888）
          </div>
          <div style="display: flex; justify-content: center; margin-top: 50px">
            <nd-button type="primary" icon="Check" @click="saveNewPassword(ruleFormRef)">确定</nd-button>
            <nd-button @click="logout" icon="SwitchButton">退出系统</nd-button>
          </div>
        </el-form>
      </div>
    </div>
  </nd-dialog>
</template>
<script setup>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import CryptoJS from "crypto-js";

import { ElMessage, ElMessageBox } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 获取路由
import { useRouter } from "vue-router";

const $router = useRouter();
const $axios = inject("$axios");
const initialPassword = ref(null);

onMounted(() => {});
// 打开弹窗 ==============================================================

const open = () => {
  initialPassword.value.open();
  sizeForm.username = localStorage.getItem("zhgsUserName");
  sizeForm.account = localStorage.getItem("zhgsAccount");
};

const ruleFormRef = ref();

const sizeForm = reactive({
  account: "", //账号
  username: "", //用户名
  password: "", //密码
  repeatPassword: "", //确认密码
  topFlag: false, //距离位置
});

const validatePass = (rule, value, callback) => {
  if (!/^((?=.*[a-zA-Z])|(?=.*[A-Z0-9])|(?=.*[a-z0-9]))(?=.*[._~!@#$^&*])[A-Za-z0-9._~!@#$^&*]{8,15}$/g.test(sizeForm.password)) {
    callback(new Error("密码有误，请按要求格式设置"));
  } else if (value == "Njusc001") {
    callback(new Error("新密码不能与初始密码相同"));
  } else {
    callback();
  }
};

const validatePass2 = (rule, value, callback) => {
  if (sizeForm.password != value) {
    callback(new Error("两次输入的密码不一致。"));
    sizeForm.topFlag = true;
  } else {
    sizeForm.topFlag = false;
    callback();
  }
};

const rules = reactive({
  password: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    {
      validator: validatePass,
      trigger: "blur",
    },
  ],

  repeatPassword: [
    { required: true, message: "请输入确认密码", trigger: "blur" },
    {
      validator: validatePass2,
      trigger: "blur",
    },
  ],
});

// 保存新密码
const saveNewPassword = (res) => {
  res.validate((val) => {
    if (val) {
      // 校验成功
      changePassword();
    } else {
      // 校验失败
      return false;
    }
  });
};

// 退出系统
function logout() {
  ElMessageBox.confirm("确定退出系统?", "退出系统", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      localStorage.setItem("syAuths", "");
      localStorage.setItem("syToken", "");
      $router.push("loginView");
    })
    .catch(() => {});
}
//初始化密码确定 接口

function changePassword() {
  let params = {
    account: sizeForm.account, //账号
    password: CryptoJS.MD5(CryptoJS.MD5(sizeForm.password).toString()).toString(), //密码
    repeatPassword: CryptoJS.MD5(CryptoJS.MD5(sizeForm.repeatPassword).toString()).toString(), //确认密码
  };
  $axios({
    url: "/auth/initPassword",
    method: "post",
    data: params,
  }).then((res) => {
    if (res.data.code == 200) {
      localStorage.setItem("syAuths", "");
      localStorage.setItem("syToken", "");
      // $router.push('loginView');
      $router.push("loginView");
      ElMessage({
        message: "新密码保存成功",
        type: "success",
      });
    } else {
      ElMessage({
        message: res.data.message,
        type: "warning",
      });
    }
    // console.log(res);
  });
}

defineExpose({
  open,
  // clear,
});
</script>
<style lang="scss" scoped>
.head-font {
  font-family: MicrosoftYaHei;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: 0px;
}
.top10 {
  transition: all 0.3s;
  margin-top: 20px;
}

.userlight {
  font-size: 14px;
  font-weight: normal;
  line-height: 24px;
  text-align: right;
  letter-spacing: 0px;
  color: #000000;
}
:deep(.el-form-item) {
  margin-bottom: 12px;
}

.dialpass {
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  letter-spacing: 0px;
  color: #444444;
  margin-left: 50px;
  margin-bottom: 10px;
  width: 100px;
}

.dialpass-font {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  width: 650px;
  margin-left: 50px;
  letter-spacing: 0px;
  color: #888888;
}

.main {
  width: 100%;
  margin-top: 10px;
  // height: 100%;
  display: flex;
  padding: 0 100px 0 50px;

  .main-img {
    width: 482px;
    height: 426px;
    margin-right: 166px;
  }

  .form {
    width: 406px;
    height: 364px;
    // border: 1px solid gold;
    margin-top: 77px;
  }

  .passrule {
    font-family: MicrosoftYaHei;
    font-size: 12px;
    font-weight: normal;

    color: #f56c6c;
  }
}
</style>
