<template>
  <div class="ndb-page-tab-list-box">
    <div class="top-box">
      <slot name="tab"></slot>
    </div>
    <div class="bottom-box">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
// import ndbPageList from "@/components/business/ndbPageList/index.vue";
</script>

<style lang="scss" scoped>
.ndb-page-tab-list-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f7f7;

  .top-box {
    width: 100%;
    height: 40px;
    margin-bottom: 10px;
    // padding: 15px 0 0 15px;
    border: 1px solid #EEEEEE;
    background: #ffffff;
  }

  .bottom-box {
    width: 100%;
    height: calc(100% - 50px);
    // flex: 1;
    padding: 15px;
    border: 1px solid #EEEEEE;
    background-color: #fff;
  }
}
</style>