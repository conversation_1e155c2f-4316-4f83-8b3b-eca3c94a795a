<template>
  <div class="nd-dropdown-menu-box" :style="{ width: width }">
    <el-dropdown-menu class="dropdown-menu" v-bind="$attrs">
      <slot></slot>
    </el-dropdown-menu>
  </div>
</template>
<script setup>
const props = defineProps({
  // 宽度
  width: {
    type: String,
    default: "auto",
  },
});
</script>
<style lang="scss" scoped>
.nd-dropdown-menu-box {
  .dropdown-menu {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  // :deep(.el-checkbox__label) {
  //   padding-left: 6px;
  //   font-size: 14px;
  // }

  // :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  //   font-family: Microsoft YaHei;
  //   font-weight: 400;
  //   color: #555555;
  // }
}
</style>
