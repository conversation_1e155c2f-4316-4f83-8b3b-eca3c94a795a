<template>
    <ndb-page-list>
        <template #tag>
            <template v-for="(item, index) in tagData.tagList" :key="index">
                <nd-tag
                    @click="
                        tagchange(item);
                        tableParams.status = item.value;
                        getTableData();
                    "
                    :checked="tagData.tagActive === item.value"
                >
                    {{ item.name }}
                    <template v-if="item.num"> ({{ item.num }}) </template>
                </nd-tag>
            </template>
        </template>

        <template #search>
            <nd-search-more arrowMarginLeft="425px">
                <nd-search-more-item title="买家（商户）" width="150px">
                    <nd-input
                        v-model.trim="tableParams.buyUserLike"
                        placeholder="请输入商户名称"
                        clearable
                        style="width: 100%"
                    />
                </nd-search-more-item>
                <nd-search-more-item title="需求编号">
                    <nd-input
                        v-model.trim="tableParams.demandCodeLike"
                        placeholder="请输入需求编号"
                        clearable
                        style="width: 100%"
                    />
                </nd-search-more-item>
                <nd-search-more-item title="收鱼日期">
                    <nd-date-picker
                        v-model="tableParams.acceptTime"
                        type="daterange"
                        value-format="YYYY-MM-DD"
                        format="YYYY-MM-DD"
                    />
                </nd-search-more-item>
                <nd-search-more-item title="登记时间">
                    <nd-date-picker
                        v-model="tableParams.insertTime"
                        type="daterange"
                        value-format="YYYY-MM-DD"
                        format="YYYY-MM-DD"
                    />
                </nd-search-more-item>
                <template #footer>
                    <nd-button type="primary" @click="getTableData" authKey="">
                        查询
                    </nd-button>
                    <nd-button
                        @click="
                            reset();
                            getTableData();
                        "
                    >
                        重置
                    </nd-button>
                </template>
            </nd-search-more>
        </template>

        <template #table>
            <nd-table
                style="height: 100%"
                :data="tableData.list"
                v-loading="tableData.loading"
            >
                <el-table-column
                    label="序号"
                    min-width="80px"
                    align="center"
                    fixed="left"
                    type="index"
                />
                <el-table-column
                    label="需求编号"
                    min-width="200px"
                    align="center"
                    prop="demandCode"
                >
                    <template #default="scope">
                        <nd-button type="edit" @click="openDetail(scope.row)">
                            {{ scope.row.demandCode }}
                        </nd-button>
                    </template>
                </el-table-column>
                <el-table-column
                    label="品类"
                    align="center"
                    min-width="200px"
                    prop="productName"
                />
                <el-table-column
                    label="采购报价（元/斤）"
                    min-width="150px"
                    header-align="center"
                    align="right"
                    prop="priceShow"
                />
                <el-table-column
                    label="需求量（斤）"
                    min-width="150px"
                    header-align="center"
                    align="right"
                    prop="amount"
                />
                <el-table-column
                    label="预估总价（元）"
                    min-width="150px"
                    header-align="center"
                    align="right"
                    prop="totalPriceShow"
                />
                <el-table-column
                    label="收鱼日期"
                    min-width="150px"
                    header-align="center"
                    align="center"
                    prop="acceptTime"
                />
                <el-table-column
                    label="买家（商户）"
                    min-width="200px"
                    header-align="center"
                    align="center"
                    prop="nickname"
                />
                <el-table-column
                    label="登记时间"
                    min-width="200px"
                    header-align="center"
                    align="center"
                    prop="insertTime"
                />
                <el-table-column
                    label="状态"
                    min-width="100px"
                    header-align="center"
                    align="center"
                >
                    <template #default="scope">
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            "
                        >
                            <span
                                :style="`width: 8px;
                                    height: 8px;
                                    margin-right: 5px;
                                    border-radius: 50%;
                                    background-color: ${
                                        STATUS_TEMPLATE[scope.row.status]?.bgc
                                    };`"
                            >
                            </span>
                            {{ STATUS_TEMPLATE[scope.row.status]?.text }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    min-width="250px"
                    header-align="center"
                    align="center"
                    fixed="right"
                >
                    <template #default="scope">
                        <!-- 待分配 -->
                        <template v-if="scope.row.status === 0">
                            <div
                                style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    gap: 12px;
                                "
                            >
                                <nd-button
                                    type="edit"
                                    @click="openDetail(scope.row)"
                                >
                                    详情
                                </nd-button>
                                <nd-button
                                    type="edit"
                                    @click="
                                        dispatchRef.open(scope.row.demandId)
                                    "
                                >
                                    派单
                                </nd-button>
                                <nd-button
                                    type="edit"
                                    @click="cancelRef.open(scope.row.demandId)"
                                >
                                    取消
                                </nd-button>
                            </div>
                        </template>

                        <!-- 待接单 -->
                        <template v-if="scope.row.status === 1">
                            <div
                                style="
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    gap: 12px;
                                "
                            >
                                <nd-button
                                    type="edit"
                                    @click="openDetail(scope.row)"
                                >
                                    详情
                                </nd-button>
                                <nd-button
                                    type="edit"
                                    @click="cancelRef.open(scope.row.demandId)"
                                >
                                    取消
                                </nd-button>
                            </div>
                        </template>

                        <!-- 已接单 -->
                        <template v-if="scope.row.status === 2">
                            <nd-button
                                type="edit"
                                @click="openDetail(scope.row)"
                            >
                                详情
                            </nd-button>
                        </template>

                        <!-- 已取消 -->
                        <template v-if="scope.row.status === 3">
                            <nd-button
                                type="edit"
                                @click="openDetail(scope.row)"
                            >
                                详情
                            </nd-button>
                        </template>
                    </template>
                </el-table-column>
            </nd-table>
        </template>

        <template #page>
            <nd-pagination
                :current-page="tableParams.page"
                :page-size="tableParams.size"
                :total="tableData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </template>
    </ndb-page-list>

    <!-- 详情 -->
    <Detail ref="detailRef" @refresh="getTableData" />
    <!-- 派单 -->
    <Dispatch ref="dispatchRef" @refresh="getTableData" />
    <!-- 取消 -->
    <Cancel ref="cancelRef" @refresh="getTableData" />
</template>

<script setup>
// 公共组件
import ndTag from "@/components/ndTag.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndButton from "@/components/ndButton.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";

// 自定义组件
// 详情
import Detail from "./components/Detail/index.vue";
// 派单
import Dispatch from "./components/Dispatch/index.vue";
// 取消
import Cancel from "./components/Cancel/index.vue";

// hooks
import { useTag } from "./hooks/index/useTag";
import { useTable } from "./hooks/index/useTable";

import { onMounted, ref } from "vue";

// ref
const detailRef = ref(null);
const dispatchRef = ref(null);
const cancelRef = ref(null);

// useTag
const { tagData, tagchange } = useTag();

// useTable
const {
    tableData,
    tableParams,
    STATUS_TEMPLATE,
    getTableData,
    handleSizeChange,
    handleCurrentChange,
    reset,
    openDetail,
} = useTable({ detailRef });

onMounted(() => {
    // detailRef.value.open();
});
</script>

<style lang="scss" scoped>
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
