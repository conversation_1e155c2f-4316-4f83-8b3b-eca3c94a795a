import { reactive, watch } from "vue";

export function useTable({ detailData = null }) {
    const tableData = reactive({
        list: []
    })

    // watch(
    //     () => detailData,
    //     () => {
    //         tableData.list = [
    //             {
    //                 productCategoryName: detailData?.productCategoryName ?? "",
    //                 amount: detailData?.amount ?? "",
    //                 priceShow: detailData?.priceShow ?? "",

    //             }
    //         ]
    //     },
    //     { immediate: true, deep: true }
    // )
}