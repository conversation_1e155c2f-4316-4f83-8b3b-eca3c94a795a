import { ElMessage } from "element-plus";
import { reactive } from "vue";
import axios from "@/http/index";
import { FormData } from "./useForm"

export class DialogData {
    /** @type {String} */
    title;
    /** @type {String} */
    memberId;
    /** @type {String} */
    type;
    /** @type {Boolean} */
    loading;
}

/**
 * 
 * @param {Object} param0
 * @param {Object} param0.dialogRef
 * @param {Function} param0.getArgeementList 
 * @param {FormData} param0.formData
 * @param {Function} param0.emits
 * @param {Function} param0.resetData
 * @returns 
 */
export function useDialog({
    dialogRef = null,
    getArgeementList = null,
    formData = null,
    emits = null,
    resetData = null
}) {
    const dialogData = reactive({
        title: "",
        memberId: "",
        type: "",
        loading: false
    })

    /**
     * open
     * 
     * @param {Object} param0
     * @param {String} param0.type
     * @returns {void}
     */
    const open = async ({ type, memberId }) => {
        dialogData.type = type;

        switch (type) {
            case "add":
                dialogData.title = "新增";
                dialogData.memberId = await getId();
                break;
            case "edit":
                dialogData.title = "编辑";
                dialogData.memberId = memberId;
                getDetail();
                break;
            case "detail":
                dialogData.title = "详情";
                dialogData.memberId = memberId;
                getDetail();
                break;
            default:
                break;
        }

        dialogRef && dialogRef.value.open()
        getArgeementList && getArgeementList({ memberId: dialogData.memberId });
    }

    /**
     * close
     * 
     * @returns {void}
     */
    const close = () => {
        resetData && resetData();
        dialogRef && dialogRef.value.close();
    }

    /**
     * 获取id
     * 
     * @returns {String}
     */
    const getId = async () => {
        return axios({
            url: "/common/getNewId",
            method: "GET",
            serverName: "nd-base2",
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return "";
            }

            return r.data.data ?? "";
        })
    }

    /**
     * 打开协议附件弹窗
     * 
     * @param {Object} param
     * @param {Object} param.dialogRef
     * @param {Object} param.param
     * @returns {void}
     */
    const openAddAgreementDialog = ({ dialogRef, param }) => {
        dialogRef && dialogRef.open(param);
    }

    /**
     * 提交
     * 
     * @param {Object} param
     * @param {Object} param.formRef
     * @returns {void}
     */
    const submit = ({ formRef }) => {
        formRef && formRef.validate(async (valid) => {

            console.log(valid);
            if (valid) {
                if (await submitApi()) {
                    switch (dialogData.type) {
                        case "add":
                            ElMessage.success("新增成功！用户初始化密码为ZHSY@8888");
                            emits && emits("addRefresh");
                            break;
                        case "edit":
                            emits && emits("upDateRefresh");
                            break;
                        default:
                            break;
                    }

                    close();
                }
            }
        });
    }

    /**
     * 提交接口
     * 
     * @returns {boolean}
     */
    const submitApi = async () => {

        const data = {
            memberId: dialogData.memberId,
            nickname: formData.nickname,
            account: formData.account,
            realname: formData.realname,
            lxdh: formData.lxdh,
            idCard: formData.idCard,
            status: formData.status
        }

        return axios({
            url: "/yhgl/yzhgl/saveOrUpdateMember",
            method: "POST",
            serverName: "nd-base2",
            data
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return false;
            }

            return true;
        }).catch(() => {
            return false;
        })
    }

    /**
     * 获取详情
     * 
     * @returns {void}
     */
    const getDetail = () => {
        dialogData.loading = true;

        const params = {
            memberId: dialogData.memberId,
        }

        axios({
            url: "/yhgl/yzhgl/findDetails",
            method: "GET",
            serverName: "nd-base2",
            params
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return;
            }

            formData.nickname = r.data.data?.nickname || "--";
            formData.account = r.data.data?.account || "--";
            formData.realname = r.data.data?.realname || "--";
            formData.status = r.data.data?.status ?? "";
            formData.phone = r.data.data?.phone || "--";
            formData.openid = r.data.data?.openid || "--";

            if (dialogData.type === "edit") {
                decryptData(r.data.data?.lxdhJm).then(r => {
                    formData.lxdh = r || "--";
                })
                decryptData(r.data.data?.idCardJm).then(r => {
                    formData.idCard = r || "--";
                })
            } else {
                formData.lxdh = r.data.data?.lxdh || "--";
                formData.lxdhJm = r.data.data?.lxdhJm || "--";
                formData.idCard = r.data.data?.idCard || "--";
                formData.idCardJm = r.data.data?.idCardJm || "--";
            }

        }).finally(() => dialogData.loading = false)
    }

    /**
     * 解密数据接口
     * 
     * @param {String} content
     * @returns {String}
     */
    const decryptData = async (content) => {
        const data = {
            content
        }
        return axios({
            url: "/common/getPlaintext",
            method: "POST",
            serverName: "nd-base2",
            data,
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return "";
            }
            return r.data.data;
        })
    }

    return {
        dialogData,
        open,
        close,
        openAddAgreementDialog,
        submit
    }
}