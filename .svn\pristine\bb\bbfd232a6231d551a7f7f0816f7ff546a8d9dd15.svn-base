<template>
  <nd-dialog ref="dialogRef" width="50vw" title="收入结算" align-center>
    <el-form
      ref="formRef"
      :model="page.form"
      :rules="rules"
      class="add-box"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="货款" prop="amount">
            <div style="display: flex; width: 100%">
              <div>{{ page.form.amount }}</div>
              <el-tooltip
                content="订单总金额，即买家(商户)应与平台结算金额"
                placement="top"
              >
                <el-icon style="margin-left: 10px; cursor: pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结算时间" prop="checkoutTime" required>
            <ndDatePicker
              v-model="page.form.checkoutTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择"
              width="100%"
              @change="checkoutTimeChange(page.form.checkoutTime)"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <nd-input
              v-model="page.form.remark"
              :maxlength="200"
              placeholder="请输入备注"
              show-word-limit
              width="100%"
              :rows="6"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <nd-button icon="Back" @click="close">取&nbsp;消</nd-button>
      <nd-button type="primary" icon="Pointer" @click="submitForm"
        >确&nbsp;定</nd-button
      >
    </template>
  </nd-dialog>
</template>

<script setup>
// 组件导入
import ndInput from "@/components/ndInput.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import { ref, reactive, inject } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");

// 定义
const page = reactive({
  form: {
    opId: "",
    orderId: "",
    amount: "",
    checkoutTime: "",
    remark: "",
    type: "1",
  },
});

const dialogRef = ref(null);

const open = (row) => {
  reset();
  page.form.opId = row.opId;
  page.form.orderId = row.orderId;
  setCurrentTime();
  if (page.form.checkoutTime) {
    getData();
  }
  dialogRef.value?.open();
};

// 新增表单引用
const formRef = ref(null);

const emit = defineEmits(["before-close"]);

//校验规则
const rules = {
  checkoutTime: [
    { required: true, message: "结算时间不能为空", trigger: "blur" },
  ],
};

//获取当前时间
const setCurrentTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  page.form.checkoutTime = formattedTime;
};

//结算时间更改
function checkoutTimeChange(val) {
  if (val) {
    getData();
  }
}

//获取货款
function getData() {
  let params = {
    opId: page.form.opId,
    orderId: page.form.orderId,
    amount: page.form.amount,
    checkoutTime: page.form.checkoutTime,
    remark: page.form.remark,
    type: page.form.type,
  };
  $axios({
    url: "/orderBill/preCheckout",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.form.amount = res.data.data.amount;
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      let params = {
        opId: page.form.opId,
        orderId: page.form.orderId,
        amount: page.form.amount,
        checkoutTime: page.form.checkoutTime,
        remark: page.form.remark,
        type: page.form.type,
      };
      $axios({
        url: "/orderBill/checkout",
        method: "post",
        serverName: "nd-base2",
        data: params,
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success(res.data.message);
          emit("before-close");
          close();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    }
  });
};

//重置
const reset = () => {
  page.form.opId = "";
  page.form.orderId = "";
  page.form.amount = "";
  page.form.checkoutTime = "";
  page.form.remark = "";
};
//状态
const close = () => {
  dialogRef.value.close();
  emit("before-close");
};
defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.add-box {
  padding: 12px;
  background-color: #fff;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;

  .el-form-item {
    margin-bottom: 16px;

    :deep(.el-form-item__label) {
      color: #606266;
      font-weight: 500;
    }
  }
}
.form-container {
  padding: 12px;
  background-color: #fff;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
  }

  .form-item {
    flex: 0 0 50%;
    padding: 8px;
    display: flex;
    align-items: center;

    .form-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      color: #303133;
    }
  }
  .form-item1 {
    flex: 0 0 50%;
    padding: 8px;
    display: flex;

    &.full-width {
      flex: 0 0 100%;
    }

    .form-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      color: #303133;
    }
  }
}
</style>
