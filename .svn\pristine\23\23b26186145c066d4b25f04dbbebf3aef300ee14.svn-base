<template>
  <div class="nd-radio-group-box">
    <el-radio-group v-bind="$attrs" @change="handleChange">
      <slot />
    </el-radio-group>
  </div>
</template>
<script setup>
const emits = defineEmits(["change"]);

function handleChange(params) {
  emits("change", params);
}
</script>
<style lang="scss" scoped>
.nd-radio-group-box {
  height: auto;
  width: auto;

  :deep(.el-radio-group) {
    height: auto;
  }
  :deep(
      .el-radio__input.is-checked + .el-radio__label,
      .el-radio__inner:hover
    ) {
    color: #068324 !important;
  }
  :deep(.el-radio__input.is-checked .el-radio__inner) {
    border-color: #068324 !important;
    background: #068324 !important;
  }
}
</style>
