<template>
  <ndb-page-list>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="发票类型" width="120px">
          <nd-select v-model="searchData.fplx" placeholder="请选择" clearable :value-on-clear="''" :empty-values="['全部']">
            <el-option v-for="item in selectList.invoiceTypeList" :label="item.label" :key="item.value"
              :value="item.value" />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="开票状态" width="120px">
          <nd-select v-model="searchData.status" placeholder="请选择" clearable :value-on-clear="''"
            :empty-values="['全部']">
            <el-option v-for="item in selectList.invoiceStatusList" :label="item.label" :key="item.value"
              :value="item.value" />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="日期搜索" width="120px">
          <nd-select v-model="searchData.timeType" placeholder="请选择" clearable style="margin-right: 10px; width: 120px"
            :value-on-clear="''">
            <el-option v-for="item in selectList.timeTypeList" :label="item.label" :key="item.value"
              :value="item.value" />
          </nd-select>
          <nd-date-picker v-model="searchData.timeRanger" range-separator="至" type="daterange" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" placeholder="请选择" @change="timeChange"></nd-date-picker>
        </nd-search-more-item>
        <nd-search-more-item title="关键字搜索" width="120px">
          <nd-input type="text" v-model.trim="searchData.keywords" placeholder="请输入" />
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="handleSearch" authKey="">
            查询
          </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <!--  row-key="id" -->
      <nd-table style="height: 100%" :data="page.list.data" @selection-change="handleSelectionChange"
        :span-method="spanMethod">
        <el-table-column align="center" label="#" type="selection" width="52px" fixed="left"></el-table-column>
        <el-table-column align="center" label="订单编号" prop="orderNo" width="180" show-overflow-tooltip
          fixed="left"></el-table-column>
        <el-table-column align="center" label="订单信息" prop="examineName" min-width="500">
          <template v-slot="scope">
            <div class="order-info">
              <div class="img-cont">
                <el-image :preview-teleported="true" style="width: 100%; height: 100%" :src="scope.row.productPic"
                  :preview-src-list="[scope.row.productPic]" />
              </div>
              <div class="info-cont">
                <div class="info-item">
                  <div class="item-name">{{ scope.row.productName }}</div>
                  <div class="item-price item-text">
                    ¥{{ scope.row.productPrice }}
                  </div>
                </div>
                <div class="info-item">
                  <div class="item-weight item-text">
                    {{ scope.row.spData }}
                  </div>
                  <div class="item-num item-text">
                    x{{ scope.row.productNum }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="申请时间" prop="insertTime" width="160"></el-table-column>
        <el-table-column align="center" label="发票类型" prop="userName" width="100">
          <template v-slot="scope">
            <div>{{ scope.row.fplx === 1 ? "电子普通发票" : "专用发票" }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="收货信息" prop="deptName" min-width="200">
          <template v-slot="scope">
            <div style="
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              ">
              <div class="tbale-row">买家: {{ scope.row.buyer }}</div>
              <div class="tbale-row">
                收货人：{{ scope.row.receiverName }}&nbsp;{{
                  scope.row.receiverPhone
                }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="remark" width="100">
          <template v-slot="scope">
            <div>{{ scope.row.status === 1 ? "已开票" : "待开票" }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="开票时间" prop="kpTime" width="160"></el-table-column>
        <el-table-column align="center" label="经办人" prop="jbrName" width="110"></el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="120px">
          <template #default="scoped">
            <div class="table-btn" style="
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              ">
              <nd-button @click="openDetailDia(scoped.row)" type="document" authKey="" link>订单详情</nd-button>
              <nd-button v-if="scoped.row.permission === 2 && scoped.row.status === 0" @click="openMakeInvoiceDia(scoped.row)" authKey="" type="document"
                link>开票</nd-button>
              <nd-button v-if="scoped.row.status===1" @click="viewInvoice(scoped.row)" authKey="" type="document"
                link>查看发票</nd-button>
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination :current-page="page.pager.pageIndex" :page-size="page.pager.pageSize" :total="page.pager.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </template>
  </ndb-page-list>
  <detail-dialog ref="detailDialogRef" @before-close="getTableData"></detail-dialog>
  <makeInvoiceDia ref="makeInvoiceDiaRef" @refreshDetail="getTableData"></makeInvoiceDia>
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";

// 导入子组件
import makeInvoiceDia from "./components/makeInvoiceDia.vue";
import detailDialog from "@/views2/orderView/components/addDialog.vue";

// 导入vue
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  computed,
  nextTick,
} from "vue";
import {
  ElMessage as elMessage,
  ElMessageBox as elMessageBox,
} from "element-plus";

// 定义axios
const $axios = inject("$axios");

// 定义ref

const searchData = reactive({
  fplx: "", //发票类型
  status: "", //开票状态
  timeType: "", // 时间类型
  timeRanger: [], //时间范围
  startTime: "", //开始时间
  endTime: "", //结束时间
  keywords: "", //关键字
});

const selectList = reactive({
  invoiceTypeList: [
    { label: "全部", value: "" },
    { label: "电子普通发票", value: "1" },
    { label: "专用发票", value: "2" },
  ],
  invoiceStatusList: [
    { label: "全部", value: "" },
    { label: "待开票", value: "0" },
    { label: "已开票", value: "1" },
  ],
  timeTypeList: [
    { label: "下单时间", value: "1" },
    { label: "付款时间", value: "2" },
    { label: "发货时间", value: "3" },
    { label: "确认收货时间", value: "4" },
    { label: "售后申请时间", value: "5" },
  ],
});

// 定义page
const page = reactive({
  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 30,
    total: 0,
  },
});

const handleSearch = () => {
  if (searchData.timeType && (!searchData.timeRanger || !searchData.timeRanger.length)) {
    elMessage.warning("请选择日期范围")
  } else if ((searchData.timeRanger && searchData.timeRanger.length) && !searchData.timeType) {
    elMessage.warning("请选择日期类型")
  } else {
    getTableData()
  }
}

// 获得表格数据
function getTableData() {
  let params = {
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    fplx: searchData.fplx,
    status: searchData.status,
    timeType: searchData.timeRanger && searchData.timeRanger.length ? searchData.timeType : '',
    startTime: searchData.timeType ? searchData.startTime : '',
    endTime: searchData.timeType ? searchData.endTime : '',
    keywords: searchData.keywords,
  };
  $axios({
    url: "/invoice/invoiceList",
    method: "get",
    serverName: "nd-base2",
    params,
  }).then((res) => {
    console.log(res, "表格数据");
    if (res.data.code === 2000) {
      if (Object.keys(res.data.data).length) {
        page.list.data = res.data.data.records;
        page.pager.total = res.data.data.total; //总条数
        page.pager.pageIndex = res.data.data.current; //当前页
        page.pager.pageSize = res.data.data.size; //每页记录数
      }
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 重置
function reset() {
  searchData.fplx = "";
  searchData.status = "";
  searchData.timeType = "";
  searchData.timeRanger = [];
  searchData.startTime = "";
  searchData.endTime = "";
  searchData.keywords = "";
  getTableData();
}

const makeInvoiceDiaRef = ref(null);
// 打开开票对话框
function openMakeInvoiceDia(row) {
  makeInvoiceDiaRef.value.open(row);
}

// 选中框
const selectedPoints = ref([]);
const handleSelectionChange = (val) => {
  selectedPoints.value = val.map((item) => item.buildId);
};

const detailDialogRef = ref(null);
// 打开详情
function openDetailDia(params) {
  let invoiceStatus = params.status === 1 ? "ykp" : "wkp";
  detailDialogRef.value.open(params, "detail", invoiceStatus);
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

const timeChange = (val) => {
  console.log(val, "时间");
  if (val && val.length) {
    searchData.startTime = searchData.timeRanger[0];
    searchData.endTime = searchData.timeRanger[1];
  } else {
    searchData.startTime = "";
    searchData.endTime = "";
  }
};

const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
  let spanOneArr = [],
    concatOne = 0;
  page.list.data.map((item, index) => {
    if (index === 0) {
      spanOneArr.push(1);
    } else {
      //合并相同内容的判断条件(基于一个条件合并)
      if (item.orderId === page.list.data[index - 1].orderId) {
        spanOneArr[concatOne] += 1;
        spanOneArr.push(0);
      } else {
        spanOneArr.push(1);
        concatOne = index;
      }
    }
  });

  if (columnIndex !== 2) {
    const _row = spanOneArr[rowIndex];
    const _col = _row > 0 ? 1 : 0;
    return {
      rowspan: _row,
      colspan: _col,
    };
  }
};

// 查看发票
const viewInvoice = (row) => {
  const imgUrl = row.fpdz;
  const previewWindow = window.open("", "_blank");
  previewWindow.document.write(`<!DOCTYPE html>
          <html>
            <body style="background:black;display: flex;justify-content: center;align-items: center;">
                <div style="width:90vw;height:90vh;display: flex;justify-content: center;align-items: center;">
                  <img style="max-height: 50%; width: auto;" src='${imgUrl}'/>
                </div>
            </body>
          </html>
`);
};

// onMounted
onMounted(() => {
  // 获得表格数据
  getTableData();
});
</script>

<style lang="scss" scoped>
.order-info {
  display: flex;
  align-content: flex-start;
  width: 100%;

  .img-cont {
    width: 90px;
    height: 90px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 10px;
  }

  .info-cont {
    width: calc(100% - 100px);

    .info-item:not(:last-child) {
      margin-bottom: 10px;
    }

    .info-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item-name {
        font-family: Microsoft YaHei;
        font-size: 14px;
        font-weight: bold;
        line-height: normal;
        display: flex;
        align-items: center;
        letter-spacing: 0px;
        font-variation-settings: "opsz" auto;
        color: #333333;
        text-align: left;
        margin-right: 10px;
      }

      // .item-price {}

      // .item-weight {}

      // .item-num {}

      .item-text {
        font-family: Microsoft YaHei;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0px;
        font-variation-settings: "opsz" auto;
        color: #333333;
      }
    }
  }
}

.table-btn {
  :deep(.normal .normal-button) {
    border: none;
  }

  :deep(.normal + .normal) {
    margin-left: 0px;
  }
}

.tbale-row {
  display: flex;
  justify-content: flex-start;
  word-break: break-all;
  text-align: left;
}
:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
}
</style>
