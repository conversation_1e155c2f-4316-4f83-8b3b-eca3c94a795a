<template>
    <div class="commodity-information-view">
        <div class="title">
            <div class="left">
                <div class="line"></div>
                商品信息
            </div>

            <div class="right">
                <div class="total">
                    预估总价：
                    <span>￥{{ detailData.detailData?.totalPriceShow }}</span>
                </div>
                <div class="total" v-if="detailData.detailData?.status === 2">
                    协商后预估总价：
                    <span>￥{{ detailData.detailData?.cpPriceTotalShow }}</span>
                </div>
            </div>
        </div>

        <div class="main">
            <ndTable :data="detailData.detailData?.productVos || []">
                <el-table-column
                    header-align="center"
                    align="left"
                    label="品类"
                    prop="productName"
                />
                <el-table-column
                    header-align="center"
                    align="center"
                    label="需求量（斤）"
                    prop="amount"
                />
                <el-table-column
                    header-align="center"
                    align="right"
                    label="采购报价（元/斤）"
                    prop="priceShow"
                />
                <el-table-column
                    header-align="center"
                    align="right"
                    label="最终协商价格（元/斤）"
                    prop="cpPriceShow"
                >
                    <template #default="scope">
                        {{
                            [0, 1, 3].includes(detailData.detailData?.status)
                                ? ""
                                : scope.row.cpPriceShow
                        }}
                    </template>
                </el-table-column>
            </ndTable>
        </div>
    </div>
</template>

<script setup>
// 自定义组件
import ndTable from "@/components/ndTable.vue";

// hooks
import { useTable } from "../../hooks/commodityInformation/useTable";

import { inject } from "vue";

// inject
const detailData = inject("$purchaseRequestFormDetailData");

// useTable
// const {} = useTable({ detailData });
</script>

<style lang="scss" scoped>
.commodity-information-view {
    border: 1px solid #eaeaea;
    border-radius: 5px;
    background: #ffffff;
    padding: 12px;

    .title {
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;

        .left {
            display: flex;
            align-items: center;
            font-family: Microsoft YaHei;
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 0px;

            font-variation-settings: "opsz" auto;
            color: #444444;

            .line {
                width: 2px;
                height: 16px;
                background: #068324;
                margin-right: 6px;
            }
        }

        .right {
            display: flex;
            align-items: center;
            gap: 20px;

            .total {
                display: table-cell;
                vertical-align: bottom;
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                font-size: 14px;
                color: #303133;

                span {
                    font-family: Microsoft YaHei;
                    font-weight: 700;
                    font-size: 20px;
                    font-variation-settings: "opsz" auto;
                    color: #ff0001;
                }
            }
        }
    }

    .main {
    }
}
</style>
