<template>
    <div class="commodity-information-view">
        <div class="title">
            <div class="left">
                <div class="line"></div>
                接单信息
            </div>

            <!-- <div class="right">
                <div class="total">原始总金额：<span>￥71,750</span></div>
                <div class="total">原始总金额：<span>￥71,750</span></div>
            </div> -->
        </div>

        <div class="main">
            <ndTable :data="detailData.detailData?.orderVoList || []">
                <el-table-column
                    header-align="center"
                    align="left"
                    label="卖家（养殖户）"
                    prop="producerName"
                />
                <el-table-column
                    header-align="center"
                    align="center"
                    label="接单时间"
                    prop="orderTime"
                />
                <el-table-column
                    header-align="center"
                    align="center"
                    label="订单"
                    prop="orderCode"
                >
                    <template #default="scope">
                        <span
                            @click="addDialogRef.open(scope.row,'detail')"
                            style="color: #0098ff; text-decoration: underline;cursor: pointer;"
                        >
                            {{ scope.row.orderCode }}
                        </span>
                    </template>
                </el-table-column>
            </ndTable>
        </div>
    </div>
    <addDialog ref="addDialogRef" />
</template>

<script setup>
// 自定义组件
import ndTable from "@/components/ndTable.vue";
import addDialog from "../../../purchaseOrderView/components/addDialog.vue";

import { inject, ref } from "vue";

// REF
const addDialogRef = ref(null);

// inject
const detailData = inject("$purchaseRequestFormDetailData");
</script>

<style lang="scss" scoped>
.commodity-information-view {
    border: 1px solid #eaeaea;
    border-radius: 5px;
    background: #ffffff;
    padding: 12px;

    .title {
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;

        .left {
            display: flex;
            align-items: center;
            font-family: Microsoft YaHei;
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 0px;

            font-variation-settings: "opsz" auto;
            color: #444444;

            .line {
                width: 2px;
                height: 16px;
                background: #068324;
                margin-right: 6px;
            }
        }

        .right {
            display: flex;
            align-items: center;
            gap: 20px;

            .total {
                display: table-cell;
                vertical-align: bottom;
                font-family: Microsoft YaHei UI;
                font-weight: 400;
                font-size: 14px;
                color: #303133;

                span {
                    font-family: Microsoft YaHei;
                    font-weight: 700;
                    font-size: 20px;
                    font-variation-settings: "opsz" auto;
                    color: #ff0001;
                }
            }
        }
    }

    .main {
    }
}
</style>
