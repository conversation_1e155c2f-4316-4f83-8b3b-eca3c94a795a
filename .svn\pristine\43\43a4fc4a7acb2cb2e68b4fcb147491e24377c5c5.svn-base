<template>
  <nd-dialog
    ref="dialogRef"
    title="权限管理"
    width="70vw"
    :before-close="closeDialog"
  >
    <div class="main">
      <div class="top">
        <div class="top-title">当前正在为</div>
        <div class="top-bar">
          <span v-if="form.userNameList.length > 0" style="color: #068324">
            {{ form.userNameList[0] }}
            <span v-if="form.userNameList.length > 1"
              >, {{ form.userNameList[1] }}</span
            >
          </span>
          <span
            ><span class="user-count">{{ form.userNameList.length }}</span
            >位</span
          >
          <span>用户授权</span>
        </div>
      </div>
      <div class="content">
        <div class="left-box">
          <el-scrollbar wrap-style="overflow-x: auto; white-space: nowrap;">
            <div class="role-title">角色列表</div>
            <el-input
              v-model.trim="form.rolesName"
              placeholder="搜索角色"
              @input="getRoleList"
              clearable
              @clear="form.rolesName = ''"
            />
            <el-scrollbar style="height: 500px; margin-top: 10px">
              <div class="role-item">
                <el-checkbox-group v-model="form.checkRoleIds">
                  <div
                    v-for="role in form.roleList"
                    :key="role.rolesId"
                    class="role-checkbox-item"
                    style="display: flex; align-items: center; gap: 5px"
                  >
                    <el-checkbox :value="role.rolesId" />
                    <span
                      class="role-name-text"
                      style="cursor: pointer; user-select: none"
                      @click="onRoleNameClick(role)"
                      >{{ role.rolesName }}</span
                    >
                  </div>
                </el-checkbox-group>
              </div>
            </el-scrollbar>
            <div
              style="
                height: 40px;
                overflow-x: auto;
                overflow-y: hidden;
                white-space: nowrap;
                scrollbar-width: thin;
                scrollbar-color: #dddee0 transparent;
              "
            >
              <div style="display: inline-block; min-width: 100%">
                <nd-pagination
                  size="small"
                  v-model:current-page="form.page"
                  v-model:page-size="form.size"
                  :total="form.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </el-scrollbar>
        </div>

        <div class="right-box">
          <div class="role-title">
            <div v-if="form.rolesNameDetail" class="current-role-name">
              {{ form.rolesNameDetail }}
            </div>
            <div>角色权限详情</div>
          </div>
          <el-scrollbar style="height: 500px; margin-top: 10px">
            <el-tree
              :data="page.roleTreeData"
              show-checkbox
              node-key="id"
              default-expand-all
              :props="defaultProps"
              :default-checked-keys="checkedKeys"
              v-model:checked-keys="checkedKeys"
            />
          </el-scrollbar>
        </div>
      </div>
    </div>
    <template #footer>
      <nd-button type="primary" @click="submit">保存</nd-button>
      <nd-button @click="closeDialog">取消</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, toRefs, onMounted, inject } from "vue";

import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndPagination from "@/components/ndPagination.vue";
import {
  ElInput,
  ElScrollbar,
  ElCheckbox,
  ElTree,
  ElMessage,
} from "element-plus";

const props = defineProps({
  selectedUsers: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["refresh"]);
const $axios = inject("$axios");

const form = reactive({
  page: 1,
  size: 30,
  total: 0,
  rolesName: "",
  roleList: [], //角色列表
  checkRoleIds: [], //选中的角色id
  userIdList: [], //选中用户的id
  userNameList: [], //选中用户的名称
  rolesNameDetail: "", //选中的角色名称
});

const dialogRef = ref(null);
const checkedKeys = ref([]); //默认展开
const page = reactive({
  roleTreeData: [],
}); //角色树

const defaultProps = {
  children: "children",
  label: "label",
};

//打开
const openDialog = (selected) => {
  form.userIdList = [];
  form.checkRoleIds = [];
  form.rolesNameDetail = "";
  page.roleTreeData = [];
  form.userIdList = selected.map((item) => item.userId);
  form.userNameList = selected.map((item) => item.userName);
  dialogRef.value.open();
  getAlreadyRole();
  getRoleList();
};

//获取角色列表接口
const getRoleList = async () => {
  try {
    const params = {
      page: form.page,
      size: form.size,
      rolesName: form.rolesName,
    };
    const response = await $axios.get("/supplierUser/rolesIndex", {
      params: params,
      serverName: "nd-base2",
    });
    if (response.data && response.data.code === 2000) {
      form.roleList = response.data.data.records || [];
      form.total = response.data.data.total || 0;
    } else {
      console.error(
        "获取角色列表接口返回错误:",
        response.data.message || response.data
      );
    }
  } catch (error) {
    console.error("获取角色列表接口请求失败:", error);
  }
};
// 分页每页数量改变
function handleSizeChange(params) {
  form.size = params;
  form.page = 1;
  getRoleList();
}

// 分页页码改变
function handleCurrentChange(params) {
  form.page = params;
  getRoleList();
}

//获取用户已有角色
const getAlreadyRole = async () => {
  const params = {
    ids: form.userIdList,
  };
  try {
    const response = await $axios.post("/supplierUser/existRoles", params, {
      serverName: "nd-base2",
    });

    if (response.data.code === 2000) {
      form.checkRoleIds = response.data.data || [];
    } else {
      console.error(
        "获取用户已有角色接口返回错误:",
        response?.data?.message || "未知错误"
      );
    }
  } catch (error) {
    console.error("获取用户已有角色接口请求失败:", error.message || error);
    form.checkRoleIds = [];
  }
};

//查看权限
const onRoleNameClick = (role) => {
  getRolesTreeData(role.rolesId);
  getRoleNameById(role.rolesId);
};
//角色名
const getRoleNameById = (id) => {
  form.rolesNameDetail =
    form.roleList.find((r) => r.rolesId === id)?.rolesName || "";
};

//权限详情
const getRolesTreeData = async (rolesId) => {
  try {
    const response = await $axios.get(
      `/supplierUser/rolesMenuIndex/${rolesId}`,
      { serverName: "nd-base2" }
    );
    if (response.data && response.data.code === 2000) {
      const treeData = mapApiDataToTree(response.data.data || []);
      page.roleTreeData.splice(0, page.roleTreeData.length, ...treeData);
      checkedKeys.value = extractCheckedKeys(response.data.data || []);
    } else {
      console.error(
        "获取角色权限接口返回错误:",
        response.data.message || response.data
      );
    }
  } catch (error) {
    console.error("获取角色权限接口请求失败:", error);
  }
};

//处理数据
const mapApiDataToTree = (nodes) => {
  return nodes.map((node) => ({
    id: node.id,
    label: node.name,
    disabled: true,
    check: true,
    children: node.children ? mapApiDataToTree(node.children) : [],
  }));
};

//默认展开
const extractCheckedKeys = (nodes) => {
  let keys = [];
  nodes.forEach((node) => {
    keys.push(node.id);

    if (node.children && node.children.length > 0) {
      keys = keys.concat(extractCheckedKeys(node.children));
    }
  });
  return keys;
};

const closeDialog = () => {
  page.roleTreeData = [];
  form.rolesNameDetail = "";
  dialogRef.value.close();
};

//授权
const submit = () => {
  $axios({
    url: "/supplierUser/updateRolesMenu",
    method: "post",
    serverName: "nd-base2",
    data: {
      rolesIds: form.checkRoleIds,
      userIds: form.userIdList,
    },
  }).then((res) => {
    if (res.data.code == 2000) {
      ElMessage.success("权限保存成功");
      closeDialog();

      emit("refresh");
    } else {
      ElMessage.success(res.data.message);
    }
  });
};

defineExpose({
  openDialog,
  closeDialog,
});
</script>

<style scoped>
.main {
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 12px;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;
}

.top {
  padding-bottom: 12px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

.top-title {
  font-weight: bold;
  margin-bottom: 0;
}

.top-bar {
  font-weight: normal;
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.user-count {
  color: red;
  margin-left: 5px;
}

.content {
  display: flex;
  gap: 20px;
}
.left-box {
  display: flex;
  flex-direction: column;
  width: 25%;
  padding: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;
}
.role-title {
  display: flex;
  gap: 10px;
  font-weight: bold;
  margin-bottom: 10px;
}
.current-role-name {
  color: #068324;
}
.role-item {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
}
.role-item a {
  cursor: pointer;
  color: #409eff;
  text-decoration: underline;
}
.right-box {
  flex: 1;
  padding: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;
}
:deep(.el-checkbox-group) {
  font-size: 14px;
}

::-webkit-scrollbar {
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #dddee0;
  border-radius: 3px;
}

::-webkit-scrollbar-button {
  display: none;
  width: 0;
  height: 0;
}
</style>
