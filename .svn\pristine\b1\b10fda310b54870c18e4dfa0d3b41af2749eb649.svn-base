<template>
  <nd-dialog ref="dialogRef" :title="page.title" width="60vw">
    <el-form
      :model="formData"
      :rules="rules"
      label-width="120px"
      ref="formRef"
      @submit.native.prevent
      style="
        background: #fff;
        padding: 12px;
        margin: 12px;
        border-radius: 5px;
        border: 1px solid #eaeaea;
      "
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="所属地区" prop="areaId">
            <nd-input
              v-model="formData.areaName"
              placeholder="自动获取当前地区"
              style="width: 100%"
              readonly
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="用户名称" prop="userName" required>
            <nd-input
              v-model="formData.userName"
              placeholder="请输入用户名称"
              clearable
              maxlength="20"
              @input="onUserNameInput"
              :disabled="isDetailMode"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="用户账号" prop="account" required>
            <nd-input
              v-model="formData.account"
              placeholder="请输入用户账号"
              clearable
              maxlength="20"
              @input="onAccountInput"
              :disabled="isDetailMode"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="所属供应商" prop="deptId">
            <nd-select
              v-model="formData.deptId"
              clearable
              style="width: 100%"
              :disabled="isDetailMode"
            >
              <el-option
                v-for="item in formData.userList"
                :key="item.deptId"
                :label="item.deptName"
                :value="item.deptId"
              />
            </nd-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="手机号码" prop="phone">
            <nd-input
              v-model="formData.phone"
              placeholder="请输入手机号码"
              clearable
              maxlength="11"
              :disabled="isDetailMode"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="身份证号" prop="card">
            <nd-input
              v-model="formData.card"
              placeholder="请输入身份证号"
              clearable
              maxlength="18"
              :disabled="isDetailMode"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <nd-select
              v-model="formData.status"
              clearable
              style="width: 100%"
              @change="onStatusChange"
              :disabled="isDetailMode"
            >
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </nd-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="page.status === 'detail'">
          <el-form-item label="角色权限" prop="roles">
            <div class="roles-display">{{ formattedRoles }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <nd-button
        v-if="page.status === 'add' || page.status === 'edit'"
        type="primary"
        @click="submitForm"
        >保存</nd-button
      >
      <nd-button
        v-if="page.status === 'add' || page.status === 'edit'"
        @click="close"
        >取消</nd-button
      >
      <nd-button v-if="page.status === 'detail'" @click="close">关闭</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";

import { ref, reactive, inject, onMounted, nextTick, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

const $axios = inject("$axios");

const dialogRef = ref(null);

const formData = reactive({
  userId: "",
  areaId: null,
  areaName: "",
  userName: "",
  account: "",
  deptId: "",
  phone: "",
  card: "",
  status: 1,
  type: "2",
  userList: [],
  roles: [],
});

const rules = {
  userName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
  account: [{ required: true, message: "用户账号不能为空", trigger: "blur" }],
  deptId: [{ required: true, message: "所属供应商不能为空", trigger: "blur" }],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const reg = /^1[3-9]\d{9}$/;
          if (!reg.test(value)) {
            callback(new Error("请输入有效的11位手机号码"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  card: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const reg =
            /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
          if (!reg.test(value)) {
            callback(new Error("请输入有效的身份证号码（15位或18位）"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

const emit = defineEmits(["before-close", "refresh"]);

const page = reactive({
  status: "",
  title: "",
  url: "",
});

function getUserList() {
  $axios
    .get("/common/getDept", {
      params: {
        type: "2",
        areaId: formData.areaId,
      },
      serverName: "nd-base2",
    })
    .then((res) => {
      console.log(res.data.data);
      nextTick(() => {
        formData.userList = res.data.data;
      });
    });
}

const open = async (params, str) => {
  if (str == "edit") {
    page.status = "edit";
    page.title = "编辑";
    page.url = "/supplierUser/update";
    if (params) {
      await fetchUserById(params);
    }
  } else if (str == "add") {
    page.status = "add";
    page.title = "新增";
    page.url = "/supplierUser/save";
    if (params) {
      console.log(params, "params");
      formData.areaId = params.areaId;
      formData.areaName = params.areaName;
    }
    resetFormData();
  } else if (str == "detail") {
    page.status = "detail";
    page.title = "详情";
    if (params) {
      await fetchUserById(params);
    }
  }
  getUserList();
  dialogRef.value.open();
};

const resetFormData = () => {
  formData.userName = "";
  formData.account = "";
  formData.deptId = "";
  formData.phone = "";
  formData.card = "";
  formData.userId = "";

  formData.status = 1;
  formData.roles = [];
};

const fetchUserById = async (userId) => {
  try {
    const response = await $axios.get(`/supplierUser/findById/${userId}`, {
      serverName: "nd-base2",
    });
    if (response.data && response.data.code === 2000) {
      const data = response.data.data;
      formData.areaId = data.areaId || null;
      formData.areaName = data.areaName || "";
      formData.account = data.account || "";
      formData.userName = data.userName || "";
      formData.userId = data.userId || "";
      formData.deptId = data.deptId || "";
      formData.phone = data.phone || "";
      formData.card = data.card || "";
      formData.status = data.status !== undefined ? data.status : 1;
      formData.roles = data.permission ? data.permission.split(",") : [];
    } else {
      ElMessage.error(response.data.message || "获取用户详情失败");
    }
  } catch (error) {
    console.error("获取用户详情接口请求失败:", error);
    ElMessage.error("获取用户详情失败");
  }
};

const close = () => {
  dialogRef.value.close();
  formData.value = { name: "" };
  emit("before-close");
};

defineExpose({
  open,
  close,
});

const formRef = ref(null);
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const params = {
          areaId: formData.areaId || null,
          account: formData.account,
          userName: formData.userName,
          deptId: formData.deptId,
          phone: formData.phone,
          card: formData.card,
          status: formData.status,
          userId: formData.userId,
        };
        const response = await $axios.post(page.url, params, {
          serverName: "nd-base2",
        });

        if (response.data && response.data.code === 2000) {
          if (page.status === "add") {
            ElMessage.success("新增成功！用户初始化密码为SYGYS@8888");
          } else {
            ElMessage.success(response.data.message);
          }
          emit("refresh");
          close();
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        console.error("保存接口请求失败:", error);
        ElMessage.error("保存失败");
      }
    }
  });
};

const onUserNameInput = (e) => {
  if (e.length > 20) {
    formData.userName = e.slice(0, 20);
  }
};

const onAccountInput = (e) => {
  if (e.length > 20) {
    formData.account = e.slice(0, 20);
  }
};

const onStatusChange = (val) => {
  if (val === 0) {
    ElMessageBox.confirm("账号禁用后将无法登录平台，确定禁用？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        formData.status = 0;
      })
      .catch(() => {
        formData.status = 1;
      });
  }
};

const formattedRoles = computed(() => {
  if (!formData.roles || formData.roles.length === 0) return "";
  return formData.roles.join(", ").replace(/, /g, ",\n");
});
const isDetailMode = computed(() => page.status === "detail");
</script>

<style lang="scss" scoped></style>
