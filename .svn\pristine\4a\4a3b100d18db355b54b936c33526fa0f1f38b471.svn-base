<template>
  <ndb-page-list>
    <template #tag>
      <nd-tag
        @click="tagClick('')"
        :checked="page.search.data.label === 'pending'"
        >全部</nd-tag
      >
      <nd-tag
        @click="tagClick(0)"
        :checked="page.search.data.label === 'approved'"
        >待出货</nd-tag
      >
      <nd-tag
        @click="tagClick(1)"
        :checked="page.search.data.label === 'initiated'"
        >待收货</nd-tag
      >
      <nd-tag
        @click="tagClick(2)"
        :checked="page.search.data.label === 'received'"
        >已完成</nd-tag
      >
      <nd-tag
        @click="tagClick(3)"
        :checked="page.search.data.label === 'completed'"
        >已取消</nd-tag
      >
    </template>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="订单编号" width="140px">
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-input
            type="text"
            v-model.trim="page.search.data.orderCodeLike"
            width="100%"
            placeholder="请输入"
            resize="none"
          />
        </nd-search-more-item>
        <nd-search-more-item title="买家(商户)" width="140px">
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-input
            type="text"
            v-model.trim="page.search.data.buyUserLike"
            width="100%"
            placeholder="请输入"
            resize="none"
          />
        </nd-search-more-item>
        <nd-search-more-item title="卖家(养殖户)" width="140px">
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-input
            type="text"
            v-model.trim="page.search.data.sellUserLike"
            width="100%"
            placeholder="请输入"
            resize="none"
          />
        </nd-search-more-item>
        <nd-search-more-item title="下单时间" width="140px">
          <nd-date-picker
            v-model="page.search.data.cycleArr"
            :empty-values="['', '']"
            :value-on-clear="''"
            range-separator="至"
            type="daterange"
            format="YYYY-MM-DD "
            value-format="YYYY-MM-DD"
            placeholder="请选择"
          ></nd-date-picker>
        </nd-search-more-item>

        <template #footer>
          <nd-button type="primary" @click="queryTable" authKey="">
            查询
          </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <div class="table-container">
        <!-- 表头行 -->
        <div class="table-row header-row">
          <div class="header-cell order-product">订单商品</div>
          <div class="header-cell order-product">单价/数量（斤）</div>
          <div class="header-cell">金额（元）</div>
          <div class="header-cell">买家（商户）</div>
          <div class="header-cell">状态</div>
          <div class="header-cell">操作</div>
        </div>

        <!-- 数据行 -->
        <div class="table-content">
          <div
            class="item-box"
            v-for="item in page.list.data"
            :key="item.orderCode"
          >
            <div class="row-indicator">
              <div class="row-item">
                <div class="row-code">
                  订单编号：
                  <div class="order-num" @click.stop="openDetail(item)">
                    {{ item.orderCode }}
                    <img
                      @click.stop="copy(item.orderCode)"
                      class="copy-icon"
                      src="../../assets/images/settlement/copy.png"
                      alt=""
                    />
                  </div>
                </div>
                <div>下单时间：{{ item.orderTime }}</div>
                <div>订单总金额：{{ item.ddzje }} 元</div>
              </div>
            </div>

            <div class="table-row">
              <div class="data-cell order-product">
                <div v-for="(good, index) in item.productVos" :key="index">
                  <el-tooltip placement="top" :content="good.productName">
                    <div class="title">{{ good.productName }}</div>
                  </el-tooltip>
                </div>
              </div>
              <!-- 单价数量 -->
              <div class="data-cell order-product">
                <div
                  class="djsl"
                  v-for="(good, index) in item.productVos"
                  :key="index"
                >
                  <div class="price">
                    ¥&nbsp;{{ good.priceShow }}
                    <span class="unit"></span>
                  </div>
                  <div class="amount">
                    x&nbsp;{{ good.amount }}
                    <span class="unit"></span>
                  </div>
                </div>
              </div>

              <!-- 金额 -->
              <div class="data-cell">
                <div
                  class="money"
                  v-for="(good, index) in item.productVos"
                  :key="index"
                >
                  <div class="money">
                    {{ good.sjje }}
                    <!-- {{
                      (
                        Number(good.priceShow).toFixed(2) *
                        Number(good.amount).toFixed(2)
                      ).toFixed(2)
                    }} -->
                  </div>
                </div>
              </div>

              <!-- 买家 -->
              <div class="data-cell">
                <div class="buyer">
                  <div class="name">{{ item.consumerName }}</div>
                </div>
              </div>

              <!-- 状态 -->
              <div class="data-cell order-product">
                <div>
                  <div class="status" v-if="item.status == 0">
                    <span class="round"></span>
                    <span>待出货</span>
                  </div>
                  <div class="status" v-if="item.status == 1">
                    <span class="round1"></span>
                    <span>待收货</span>
                  </div>
                  <div class="status" v-if="item.status == 2">
                    <span class="round2"></span>
                    <span>已完成</span>
                  </div>
                  <div class="status" v-if="item.status == 3">
                    <span class="round3"></span>
                    <span>已取消</span>
                  </div>
                </div>
              </div>

              <!-- 操作 -->
              <div class="data-cell">
                <div class="operation">
                  <div class="operationItem" @click="openDetail(item)">
                    详情
                  </div>
                  <div
                    @click="gotoJs(item)"
                    v-if="item.status == 2"
                    class="operationItem"
                  >
                    结算
                  </div>
                  <div
                    @click="openDeleteDialog(item)"
                    v-if="item.status == 0"
                    class="operationItem"
                  >
                    取消订单
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <nd-table
        border
        style="height: 100%"
        :data="page.list.data"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="#" type="selection" width="52px">
        </el-table-column>
        <el-table-column
          align="center"
          label="订单编号"
          prop="orderNo"
          width="240px"
        >
          <template #default="scoped">
            <span>{{ scoped.row.orderCode }}</span>
            <span class="copy" @click="copy(scoped.row.orderCode)"> 复制</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="原需求单号"
          prop="orderNo"
          width="240px"
        >
          <template #default="scoped">
        
            <div class="demandCode">
              <nd-button type="edit" @click="openxqDetail(scoped.row)">
                {{ scoped.row.demandCode }}
              </nd-button>
              <span class="copy" @click="copy(scoped.row.demandCode)">
                复制</span
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          class-name="orderInfoF"
          align="center"
          label="订单商品"
          prop="name"
          width="640px"
        >
          <template #default="scoped">
            <div class="padding orderInfo orderInfoFlex orderInfoaddselect">
              <div
                v-for="(good, index) in scoped.row.productVos"
                :key="index"
                class="goodsItemList"
              >
                <div class="goodImg">
                  <img :src="imgFile + good.productPic" alt="" />
                </div>
                <div class="goodInfo">
                  <div class="goodTitle">
                    <div class="left">
                      {{ good.productName }}
                    </div>
                    <div class="right">¥{{ good.price }}</div>
                  </div>
                  <div class="goodNum">
                    <div class="left">{{}}</div>
                    <div class="right">x{{ good.amount }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="金额" width="160px" prop="ddzje">
        </el-table-column>
        <el-table-column
          align="center"
          label="买家(商户)"
          width="160px"
          prop="consumerName"
        >
        </el-table-column>
        <el-table-column
          align="center"
          label="状态"
          width="160px"
          prop="status"
        >
          <template #default="scoped">
            <span v-if="scoped.row.status == 0">待出货</span>
            <span v-if="scoped.row.status == 1">待收货</span>
            <span v-if="scoped.row.status == 2">已完成</span>
            <span v-if="scoped.row.status == 3">已取消</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          class-name="orderInfoF"
          width="140px"
        >
          <template #default="scoped">
            <div class="padding tableOperation">
              <div class="operationItem" @click="openDetail(scoped.row)">
                详情
              </div>
              <div
                @click="gotoJs(scoped.row)"
                v-if="scoped.row.status == 2"
                class="operationItem"
              >
                结算
              </div>
              <div
                @click="openDeleteDialog(scoped.row)"
                v-if="scoped.row.status == 0"
                class="operationItem"
              >
                取消订单
              </div>
            </div>
          </template>
        </el-table-column>
      </nd-table> -->
    </template>
    <template #page>
      <nd-pagination
        :current-page="page.pager.pageIndex"
        :page-size="page.pager.pageSize"
        :total="page.pager.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </ndb-page-list>
  <ndb-import ref="importRef" type="PRO_TASK" titles="任务导入" projectId="1" />
  <ndb-export ref="exportRef" />
  <add-dialog ref="addDialogRef" @before-close="getTableData"></add-dialog>
  <!-- 需求详情 -->
  <xqDetail ref="xqdetailRef" @refresh="getTableData" />
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import ndbExport from "@/components/business/ndbExport/index.vue";
// 导入子组件
import addDialog from "./components/addDialog.vue";
import ndTreeSelect from "@/components/ndTreeSelect.vue";
// 需求详情
import xqDetail from "@/views2/purchaseRequestFormView/components/Detail/index.vue";
// 导入vue
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  computed,
  nextTick,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  ElMessage as elMessage,
  ElMessageBox as elMessageBox,
} from "element-plus";
const router = useRouter();
const route = useRoute();
// 定义axios
const $axios = inject("$axios");

// 定义ref
const addDialogRef = ref(null);
const shipmentDialogRef = ref(null);
const exportRef = ref(null);
const importRef = ref(null);
const orderProcessingRef = ref(null);
// 定义page
const page = reactive({
  tree: {
    data: [],
    defaultProps: {
      value: "id",
      label: "name",
      isLeaf: "leaf",
    },
  },
  search: {
    data: {
      label: "pending",
      orderCodeLike: "", //订单编号
      buyUserLike: "", //买家
      sellUserLike: "", //卖家（养殖户）
      cycleArr: ["", ""],
      status: "", //状态（0：待出货，1：待收货，2：已完成，3：已取消）  不传 查询全部
    },
    dict: {
      approveltypeList: [], //审批类型
      statusList: [], //状态
      splxList: [],
      datetypeList: [
        { modelId: "XD", name: "下单时间" },
        { modelId: "FK", name: "付款时间" },
        { modelId: "FH", name: "发货时间" },
        { modelId: "SH", name: "确认收货时间" },
      ],
    },
  },
  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 30,
    total: 0,
  },
});
// 加载节点
let placeholder = ref("请输入");
let defaultExpandedNodes = ref([]);
let checkStrictly = ref(true);
let isFirstExpandFlage = ref(false);
let isFirstExpandData = ref([]);
let benji = ref(true);
async function loadNode(node, resolve) {
  loadchildnode(node, resolve);
}
async function loadchildnode(node, resolve) {
  console.log(node, "--+++");
  let params = {
    benji: benji.value, //	是否返回本级
    // containDept: false, //是否包含部门
    // deptType: 1, //部门类型 1运营 2供应商
    // limitLevel: "", //最低级别，用来控制isLeaf的返回值
  };
  if (node.level == 0) {
    params.id = "";
  } else if (node.data.id) {
    params.id = node.data.id;
  }
  let res = null;
  if (isFirstExpandFlage.value) {
    res = isFirstExpandData.value;
    isFirstExpandFlage.value = false;
    benji.value = false;
  } else {
    res = await getNextAreaTree(params);
  }
  console.log("🚀 ~ loadchildnode ~ res:", params.benji);
  return resolve(res);
}
function getNextAreaTree(params) {
  return $axios({
    method: "get",
    data: params,
    url: "/common/areaTree",
    serverName: "nd-base2",
  }).then((res) => {
    console.log(res, "地区树");
    if (res.data.code === 2000) {
      isFirstExpandFlage.value = false;
      if (!params.id) {
        isFirstExpandData.value = res.data.data[0].children;
        isFirstExpandFlage.value = true;
        defaultExpandedNodes.value = [res.data.data[0].id];
      }
      return res.data.data || [];
    }
  });
}
function handleNodeClick(node) {
  console.log("🚀 ~ node:", node);
}
// 分类改变
function tagClick(index) {
  if (index === "") {
    page.search.data.label = "pending";
  }
  if (index === 0) {
    page.search.data.label = "approved";
  }
  if (index === 1) {
    page.search.data.label = "initiated";
  }
  if (index === 2) {
    page.search.data.label = "received";
  }
  if (index === 3) {
    page.search.data.label = "completed";
  }
  if (index === 50) {
    page.search.data.label = "cancellationRequest";
  }
  if (index === 51) {
    page.search.data.label = "closed";
  }
  page.search.data.status = index;
  // 获得表格数据
  getTableData();
}

// 获得审批类型
function getApprovalType() {
  return;
  $axios({
    url: "/oa/model/selectItem",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      page.search.dict.approveltypeList = res.data.data;
    }
  });
}
// 获得商品分类
const getGoodsType = () => {
  return;
  $axios({
    url: "/dict/getCategory",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      const allOption = { categoryId: "", name: "全部" };
      page.search.dict.splxList = [allOption, ...res.data.data];
    }
  });
};
// router
let memberId = ref("");
// 获得表格数据
function getTableData() {
  $axios({
    url: "/buy/order/page/findAll",
    serverName: "nd-base2",
    method: "get",
    data: {
      page: page.pager.pageIndex,
      size: page.pager.pageSize,
      orderCodeLike: page.search.data.orderCodeLike,
      buyUserLike: page.search.data.buyUserLike, //买家
      sellUserLike: page.search.data.sellUserLike, //卖家
      insertTimeStart: page.search.data.cycleArr
        ? page.search.data.cycleArr[0]
        : "", // 下单时间 - 开始时间
      insertTimeEnd: page.search.data.cycleArr
        ? page.search.data.cycleArr[1]
        : "", //下单时间 - 结束时间
      status: page.search.data.status, //状态（0：待出货，1：待收货，2：已完成，3：已取消）  不传 查询全部
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.records.map((item, index) => {
        return {
          ...item,
          checked: false, // 选中框
        };
      });
      page.pager.total = res.data.data.total; //总页数
      page.pager.pageIndex = res.data.data.current; //当前页
      page.pager.pageSize = res.data.data.size; //每页记录数
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 重置
function reset() {
  page.search.data.orderCodeLike = "";
  page.search.data.buyUserLike = "";
  page.search.data.sellUserLike = "";
  page.search.data.cycleArr = ["", ""];
  getTableData();
}

// 打开新增对话框
function openAddDialog() {
  addDialogRef.value.open("add", null, false);
}

// 选中框
const selectedPoints = ref([]);
const handleSelectionChange = (val) => {
  selectedPoints.value = val.map((item) => item.buildId);
};

// // 打开编辑dialog
// function openEditDialog(params) {
//   addDialogRef.value.open("detail", params);
// }
// 打开详情dialog
function openDetail(params) {
  addDialogRef.value.open(params, "detail");
}

// 取消
function openDeleteDialog(data) {
  elMessageBox
    .confirm("确定要取消订单?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      $axios({
        url: "/buy/order/cancel",
        method: "post",
        serverName: "nd-base2",
        data: {
          orderId: data.orderId,
          cancelRole: 3, // 用户类型 1:运营 2:供应商, //取消用户角色 1：商户 3：平台 4：系统
          cancelReason: "平台取消",
        },
      }).then((res) => {
        if (res.data.code === 2000) {
          elMessage({
            message: res.data.message,
            type: "success",
          });
          getTableData();
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    });
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

// 导入
function openImportDialog() {
  importRef.value.open();
}

// 导出
function openExportDialog() {
  let params = {
    label: "approved",
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    label: page.search.data.label,
    status: page.search.data.status, // 流程状态
    modelId: page.search.data.modelId, // 审批类型
    startTime: page.search.data.cycleArr
      ? searchData.cycleArrfq[0] + ":00"
      : "", // 发起开始时间
    endTime: page.search.data.cycleArr ? searchData.cycleArrfq[1] + ":00" : "", // 发起结束时间
  };
  exportRef.value.open(params, "OA_EXAMINE_LIET", "");
}
// 数字转为中文
function numberToChinese(num) {
  if (num === 0) return "零";
  const chineseNum = [
    "零",
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
  ];
  const chineseUnit = [
    "",
    "十",
    "百",
    "千",
    "万",
    "十",
    "百",
    "千",
    "亿",
    "十",
    "百",
    "千",
  ];
  const str = num.toString();
  let result = "";
  let zeroFlag = false;
  const len = str.length;
  for (let i = 0; i < len; i++) {
    const digit = parseInt(str[i], 10);
    const unitIndex = len - i - 1;

    if (digit === 0) {
      zeroFlag = true;
      if (unitIndex % 4 === 0) {
        // 万、亿位
        result += chineseUnit[unitIndex];
        zeroFlag = false;
      }
    } else {
      if (zeroFlag) {
        result += "零";
        zeroFlag = false;
      }
      result += chineseNum[digit] + chineseUnit[unitIndex];
    }
  }
  // 处理以零结尾的情况
  while (result.endsWith("零")) {
    result = result.slice(0, -1);
  }
  // 处理一十开头的情况
  if (result.startsWith("一十")) {
    result = result.slice(1);
  }
  return result;
}
// 全选反选
let checkedAll = ref(false);
// 全选/反选
const selectAll = (checked) => {
  page.list.data.forEach((item) => {
    item.checked = checked;
  });
};

// 单个选择
const selectItem = () => {
  checkedAll.value = page.list.data.every((item) => item.checked);
};
// 打开订单处理
function openOrderProcessingRef(params) {
  orderProcessingRef.value.open(params, "shipment");
}
// 跳转结算
function gotoJs(params) {
  router.push({
    path: "/settlementOrderView",
    query: {
      orderCode: params.orderCode,
    },
  });
}
// 查询
const queryTable = () => {
  page.search.data.cycleArr =
    page.search.data.cycleArr == null ? ["", ""] : page.search.data?.cycleArr;
  getTableData();
};
// onMounted

const imgFile = ref(""); //图片路径
onMounted(() => {
  imgFile.value =
    window.ipConfig.base2 +
    "/common/download?token=" +
    localStorage.getItem("syToken") +
    "&path=";
  let query = route.query;
  if (query && query.memberId) {
    memberId.value = query.memberId;
    console.log("🚀 ~ onMounted ~ memberId.value:", memberId.value);
  }
  // 获得审批类型
  getApprovalType();
  // 获得表格数据
  getTableData();
  //获得商品分类
  getGoodsType();

  ///
});
//new

const handlerDatas = (arr) => {
  let newArr = arr.map((el) => {
    return el.productVo.categoryName;
  });
  if (!Array.isArray(newArr) || newArr.length === 0) return newArr;

  // 使用Set去重判断是否所有元素相同
  const uniqueValues = new Set(newArr);
  if (uniqueValues.size === 1) {
    return [newArr[0]]; // 返回包含单个元素的数组
  }
  return newArr; // 元素不相同则返回原数组
};
// 复制
const copy = (value) => {
  let text = "";
  // 根据 item 的不同属性判断 text 的值
  text = value;
  const tempInput = document.createElement("textarea");
  tempInput.value = text;
  tempInput.style.position = "fixed";
  tempInput.style.opacity = 0;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand("copy");
  tempInput.remove();
  elMessage({
    message: "复制成功",
    type: "success",
  });
};
// 需求详情
let xqdetailRef = ref(null);
const openxqDetail = (row) => {
  console.log("🚀 ~ openxqDetail ~ row:", row);
  xqdetailRef && xqdetailRef.value.open(row);
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.workhour {
  color: #444444;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;

  .workhour-item-line {
    width: 4px;
    height: 15px;
    border-radius: 3px;
    background-color: red;
    margin-right: 12px;
  }

  .workhour-item {
    margin-right: 23px;

    .red {
      color: red;
    }

    .blue {
      color: #10a3e7;
    }
  }
}

:deep(.noPadding .el-table__cell) {
  padding: 0 !important;
}

:deep(.noPadding .cell) {
  padding: 0;
  height: 100%;
}

// 订单详情
.order_details {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .good {
    width: 100%;

    .goodItem {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 3px 5px;
      border-bottom: 1px solid #ebeef5;

      // 左
      .left {
        width: 80px;
        height: 80px;

        img {
          width: 80px;
          height: 80px;
        }
      }

      //中
      .mid {
        width: 240px;
        line-height: 40px;
        text-align: left;
        margin-left: 20px;
      }

      // 右
      .right {
        width: 180px;
        line-height: 40px;
        margin-left: auto;
      }
    }

    :last-of-type {
      border: none;
    }
  }
}

//商品类型
.goodType {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;

  // padding-top: 35px;
  .goodTypeItem {
    box-sizing: content-box;
    padding: 3px 5px;
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #ebeef5;
  }

  :last-of-type {
    border: none;
  }
}

//收货信息
.shippingInfo {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}

.top {
  width: 100%;
  height: 30px;
  background-color: #fdeded !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
}

.top2 {
  width: 100%;
  height: 30px;
  background-color: #fdeded !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
  position: absolute;
  top: 0;
}

//内容仿表格
:deep(.el-card__body) {
  border-left: 1px solid #ededed;
}

.tableMock {
  border: 1px solid #ededed;
  max-width: 1658px;
  background-color: #fff;
  height: 100%;
  overflow: hidden;
  position: relative;
  padding-top: 38px;
  display: flex;
  align-content: center;
  justify-content: center;
  align-content: center;
}

.tableHeader {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
  line-height: 38px;
  height: 38px;
  background: #f9f9f9;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  padding-right: 6px;
  box-sizing: border-box;
}

.tableHeader div {
  text-align: center;
  font-family: Microsoft YaHei UI;
  font-size: 14px;
  color: #303133;
  z-index: 0;
}

.tableContent {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  overflow: auto;
  height: 100%;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  /* 滚动条轨道背景颜色 */
}

::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  /* 滚动条滑块颜色 */
  border-radius: 3px;
  /* 滚动条滑块圆角 */
}

::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
  /* 鼠标悬停时滚动条滑块颜色 */
}

.borderLeft {
  border-left: 1px solid #ededed;
}

.borderRight {
  border-right: 1px solid #ededed;
}

.borderTop {
  border-top: 1px solid #ededed;
}

.borderBottom {
  border-bottom: 1px solid #ededed;
}

.select {
  width: 60px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.orderInfo {
  width: 890px;
}

.orderInfoFlex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.goodImg {
  width: 90px;
  height: 90px;
  border-radius: 4px;
  opacity: 1;
  padding: 0 !important;
}

.goodImg img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.goodInfo {
  width: 526px;
  height: 94px;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  flex-wrap: wrap;
  align-items: flex-start;
  align-content: flex-start;
}

.goodInfo .goodTitle {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.goodInfo .goodTitle .left {
  // width: 245px;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  z-index: 0;
  text-align: left;
  display: flex;
}

.goodInfo .goodTitle .right {
  width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: right;
}

.goodInfo .goodNum {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.goodInfo .goodNum .left {
  min-width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: left;
  // 省略
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goodInfo .goodNum .right {
  min-width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: right;
}

.orderInfoaddselect {
  width: 950px;
}

.productType {
  width: 180px;
  font-size: 14px;
  color: #555;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  display: grid;
  grid-template-rows: repeat(auto-fill, 1fr);
}

.productTypeItem {
  width: 100%;
  height: 107px;
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
}

.productTypeItem:last-of-type {
  border: none;
}

.tabelshippingInfo {
  width: 300px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.tabelshippingInfo .maijia,
.tabelshippingInfo .lxfs {
  width: 100%;
  height: 25px;
  line-height: 25px;
  margin-bottom: 10px;
}

.tableStatus {
  width: 106px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
}

.tableStatus .dot {
  width: 8px;
  height: 8px;
  border-radius: 100px;
  background: #ff0001;
  margin-right: 5px;
}

.tableOperation {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.tableOperation .operationItem {
  padding: 0 6px;
  height: 19px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #0b8df1;
  // margin-bottom: 6px;
  cursor: pointer;
}

.tableItem {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  margin-top: 20px;
}

.tableItemTop {
  width: 100%;
  height: 38px;
  background: #f8fffa;
  display: flex;
  flex-wrap: nowrap;
}

.tableItemTopOther {
  width: 1536px;
  font-size: 14px;
  line-height: 38px;
  color: #303133;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.tableItemContent {
  width: 100%;
  // width: 1596px;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
}

.goodsItem {
  width: 100%;
  box-sizing: border-box;
  min-height: 110px;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
}

.goodsItem .padding {
  padding: 10px 12px;
  text-align: center;
  font-size: 14px;
  color: #555;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  box-sizing: border-box;
  flex-wrap: wrap;
}

.goodsItemList {
  width: 100%;
  display: flex;
  // flex-wrap: wrap;
  border-bottom: 1px solid #ededed;
  padding: 0 12px;
  &:last-of-type {
    border: none;
  }
}

.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 2px;
  background: #20b203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;
}

.u-empty .uni-image {
  width: 400rpx !important;
  height: 400rpx !important;
}
:deep(.orderInfoF .cell) {
  padding: 0 !important;
}
.copy {
  margin-left: 5px;
  color: #0b8df1;
  text-decoration: underline;
  cursor: pointer;
}
.demandCode {
  display: flex;
  justify-content: center;
}
// 自定义表格
.table-container {
  width: 100%;
  margin: auto;
  border-collapse: collapse;
  border-bottom: 1px solid #ededed;
  border-left: 1px solid #ededed;
  border-right: 1px solid #ededed;
  height: calc(100% - 29px);
  background-color: #fff;
  .table-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: inset 0 -1px 0 0 #ededed, inset 0 1px 0 0 #ededed;
    box-sizing: border-box;
    .header-cell {
      color: #303133;
      font-size: 14px;
      width: 180px;
      // height: 38px;
      line-height: 38px;
      text-align: center;
      // background: #f9f9f9;
      box-sizing: border-box;
      // box-shadow: inset -1px -1px 0px 0px #ededed;
      // border-left: 1px solid #ededed;
      // 第一个
      &:first-of-type {
        border-left: none;
      }
    }

    .data-cell {
      color: #303133;
      width: 180px;
      padding: 8px 14px 14px 12px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      min-height: 100px;
      flex-grow: 1;
      border-right: 1px solid #ededed;
      .title {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .djsl {
        color: #555;
        box-sizing: border-box;
        text-align: right;
        font-size: 14px;
        .price,
        .amount {
          line-height: 18px;
          margin-bottom: 5px;
        }
      }
      .money {
        text-align: right;
        color: #555;
        font-size: 14px;
      }
      .buyer {
        text-align: left;
        color: #555;
        font-size: 14px;
      }
      .status {
        display: flex;
        color: #555;
        font-size: 14px;
        align-items: center;
        justify-content: center;
        min-height: 19px;
        .round {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ff8a00;
          margin-right: 10px;
        }
        .round1 {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ffda71;
          margin-right: 10px;
        }
        .round2 {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #068324;
          margin-right: 10px;
        }
        .round3 {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ff0001;
          margin-right: 10px;
        }
      }
      .operation {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: center;
        text-align: center;
        .operationItem {
          padding: 0 6px;
          height: 19px;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: #0b8df1;
          // margin-bottom: 6px;
          cursor: pointer;
        }
      }
      .remark-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        min-height: 19px;
      }
    }
    .order-product {
      width: 300px;
    }
  }
  .header-row {
    background-color: #f9f9f9;
    height: 38px;
  }
  .table-content {
    height: calc(100% - 29px);
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0bcbc;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #cfcdcd;
    }
    .row-indicator {
      margin-top: 20px;
      background-color: #f8fffa;
      border-top: 1px solid #ededed;
      height: 38px;
      width: 100%;
      .row-item {
        display: flex;
        font-size: 14px;
        gap: 16px;
        height: 100%;
        padding-left: 10px;
        align-items: center;
        color: #303133;
        .row-code {
          display: flex;
          height: 100%;
          align-items: center;
          .order-num {
            color: #0098ff;
            text-decoration: underline;
            display: flex;
            cursor: pointer;
            .copy-icon {
              width: 18px;
              height: 18px;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }
}
</style>
