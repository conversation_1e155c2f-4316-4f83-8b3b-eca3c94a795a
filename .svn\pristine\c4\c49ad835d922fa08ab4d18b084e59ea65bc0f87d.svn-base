<template>
  <!-- // 编辑地址 -->
  <nd-dialog
    ref="editAddressRef"
    width="60vw"
    height="48vh"
    :title="title"
    align-center
  >
    <div class="mainBox">
      <el-form
        ref="addFormRef"
        :model="form"
        :rules="rules"
        class="add-box"
        label-position="left"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="收货人" label-width="80px" prop="name">
              <nd-input v-model="form.name" placeholder=" " width="90%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" label-width="80px" prop="phone">
              <nd-input v-model="form.phone" placeholder=" " width="90%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配送地址" label-width="80px" prop="classis">
              <!-- <el-cascader
              @change="cascaderChenage"
              :props="cascaderProps"
              width="90%"
              style="width: 90%"
            /> -->
              <!-- ///////// -->
              <el-tree-select
                width="90%"
                style="width: 90%"
                v-model="form.cascaderValue"
                lazy
                :load="loadNode"
                :props="cascaderProps"
                node-key="id"
                @change="cascaderChenage"
                :default-expanded-keys="expandedKeys"
                :render-after-expand="false"
              >
                <!-- <template #default="{ node }">
                <span>{{ getFullPath(node) }}</span>
              </template> -->
              </el-tree-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="详细地址"
              label-width="80px"
              prop="detailAddress"
            >
              <nd-input
                type="textarea"
                v-model="form.detailAddress"
                placeholder=" "
                width="95.5%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <nd-button type="" icon="Pointer" @click="submit('1')"
        >确&nbsp;定</nd-button
      >
      <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vuea
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义属性
const props = defineProps({});

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const editAddressRef = ref(null);
// 定义对话框的标题
let title = ref("");
// 定义表单数据
const addFormRef = ref(null);
let tableContent = ref(null);
let tableData = ref([]);
let orderId = ref("");
let form = ref({
  wlgs: "",
  wlbh: "",
  remake: "",
  provinceId: "",
  province: "",
  cityId: "",
  city: "",
  districtId: "",
  district: "",
  detailAddress: "",
  townId: "",
  town: "",
  villageId: "",
  village: "",
  cascaderValue: [], // 新增级联选择器的值
});

const handleMenuNameChange = (value) => {
  console.log(value);
  if (form.value.menuName) {
    console.log("有值");
  } else {
    console.log("没值了");
    // form.value.menuName = ''
    form.value.functionName = "";
  }
};

// 定义校验规则 ==========================================================

// 自定义校验规则 --- 正则 --- 功能菜单名称最长不得超过10个汉字
const validateFunctionName = (rule, value, callback) => {
  const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
  let chineseCharacters = form.value.menuName.match(chineseReg); // 匹配输入字符串中的汉字
  if (chineseCharacters && chineseCharacters.length > 10) {
    return callback(new Error("error"));
  } else {
    return callback();
  }
};

// 表单校验规则
const rules = reactive({
  name: [{ required: true, message: "请输入收货人" }],
  phone: [{ required: true, message: "请输入拒联系电话" }],
  detailAddress: [{ required: true, message: "请输入详细地址" }],
  functionName: [
    { required: true, message: "请输入功能点名称", trigger: "blur" },
    // { min: 1, max: 50, message: "最长不得超过50个汉字", trigger: "blur" },
    {
      validator: validateFunctionName,
      trigger: "blur",
      message: "最长不得超过50个汉字",
    },
  ],
});

// 打开弹窗 ==============================================================
async function open(dzglVo) {
  console.log("🚀 ~ open ~ dzglVo:", dzglVo);
  if (dzglVo) {
    // 深拷贝 dzglVo 避免修改原始数据
    const newForm = { ...dzglVo };
    // 构建级联选择器的值
    const value = [
      newForm.provinceId,
      newForm.cityId,
      newForm.districtId,
      newForm.townId,
      newForm.villageId,
    ].filter((id) => id);

    // 只保留前3级用于显示
    const displayValue = value.slice(0, 3);
    newForm.cascaderValue = value[value.length - 1]; // 只绑定最后一级的值
    expandedKeys.value = value; // 设置需要展开的节点

    // 预加载数据
    for (const id of value) {
      if (!loadedDataCache.value[id]) {
        await loadNode({ level: 0, data: { id } }, () => {});
      }
    }

    form.value = newForm;
    console.log("🚀 ~ open ~ form.value:", form.value);
  }
  title.value = "编辑地址";
  editAddressRef.value.open();
}
// 获得详情
function getDetail() {
  $axios({
    url: "/order/manage/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: orderId.value,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      tableContent.value = res.data.data;
      tableData.value = tableContent.value.detailVos;
    }
  });
}

// 清空表单
const clear = () => {
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
};

// 关闭弹窗 ==============================================================
const close = () => {
  emit("before-close");
  editAddressRef.value.close();
  clear();
};

// 保存提交 ==============================================================
const submit = async () => {
  await addFormRef.value.validate((valid, fields) => {
    if (valid) {
      // 编辑
      if (form.value.pointId !== null) {
        let params = form.value;
        $axios({
          url: "/order/manage/dzgl/update",
          method: "post",
          serverName: "nd-base2",
          data: params,
        }).then((res) => {
          if (res.data.code === 2000) {
            // 轻提示
            ElMessage({
              message: "保存成功",
              type: "success",
            });
            close();
          } else {
            ElMessage({
              message: res.data.message,
              type: "warning",
            });
          }
        });
      } else {
        // 新建
        let params = form.value;
        $axios({
          url: "/projectPoint/save",
          method: "post",
          // serverName: 'zjd-base',
          data: params,
        }).then((res) => {
          console.log(form.value.projectId, "项目id");
          if (res.data.code === 2000) {
            // 轻提示
            ElMessage({
              message: "保存成功",
              type: "success",
            });
            myEmit("refreshTable");
            // 关闭弹框
            close();
          } else {
            ElMessage({
              message: res.data.message,
              type: "warning",
            });
          }
        });
      }
    } else {
      console.log("校验失败!", fields);
    }
  });
};
// 定义一个缓存对象来存储已加载的数据
const loadedDataCache = ref({});
let expandedKeys = ref([]); // 新增 expandedKeys 变量
// 动态加载
const cascaderProps = {
  lazy: true,
  label: "name",
  value: "id",
  leaf: "leaf",
  lazyLoad(node, resolve) {
    const { level, data } = node;
    const parentId = data?.id || "";
    // 只加载前3级
    if (level >= 3) {
      resolve([]);
      return;
    }

    $axios({
      url: "/common/area",
      method: "get",
      serverName: "nd-base2",
      data: { id: parentId },
    })
      .then((res) => {
        if (res.data.code === 2000) {
          // 为每个节点添加parentId
          const nodes = res.data.data.map((item) => ({
            ...item,
            parentId,
          }));
          loadedDataCache.value[parentId] = nodes;
          resolve(nodes);
        }
      })
      .catch((error) => {
        console.error("级联数据加载出错:", error);
        resolve([]);
      });
  },
};

// 定义 tree-select 的加载方法
const loadNode = async (node, resolve) => {
  const { level, data } = node;
  const parentId = data?.id || "";

  try {
    const res = await $axios({
      url: "/common/area",
      method: "get",
      serverName: "nd-base2",
      data: { id: parentId },
    });

    if (res.data.code === 2000) {
      loadedDataCache.value[parentId] = res.data.data;
      resolve(res.data.data);
    } else {
      resolve([]);
    }
  } catch (error) {
    console.error("加载地区数据失败:", error);
    resolve([]);
  }
};
const getFullPath = (node) => {
  const path = [];
  let current = node;
  while (current) {
    path.unshift(current.data.name);
    current = current.parent;
  }
  return path.join(" / ");
};
//
const cascaderChenage = (value) => {
  console.log("选中项的 id:", value);
  const pathIds = [];
  const names = [];

  // 递归查找完整路径
  const findPath = (id) => {
    for (const parentId in loadedDataCache.value) {
      const item = loadedDataCache.value[parentId]?.find((i) => i.id === id);
      if (item) {
        pathIds.unshift(item.id);
        names.unshift(item.name);
        if (item.parentId) {
          findPath(item.parentId);
        }
        break;
      }
    }
  };

  findPath(value);

  // 更新表单数据
  form.value.provinceId = pathIds[0] || "";
  form.value.province = names[0] || "";
  form.value.cityId = pathIds[1] || "";
  form.value.city = names[1] || "";
  form.value.districtId = pathIds[2] || "";
  form.value.district = names[2] || "";
  form.value.townId = pathIds[3] || "";
  form.value.town = names[3] || "";
  form.value.villageId = pathIds[4] || "";
  form.value.village = names[4] || "";

  console.log("完整地址路径:", pathIds, names);
};
// 暴露方法给父组件 =======================================================
defineExpose({
  open,
  clear,
});
</script>

<style lang="scss" scoped>
.goodstotal {
  margin-bottom: 20px;
}
.mainBox {
  padding: 12px;
  width: 100%;
  height: 100%;
}
.add-box {
  padding: 12px;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  border: 1px solid #eaeaea;
  background-color: #fff;
  :deep(.el-textarea__inner) {
    height: 80px;
  }
}
</style>
