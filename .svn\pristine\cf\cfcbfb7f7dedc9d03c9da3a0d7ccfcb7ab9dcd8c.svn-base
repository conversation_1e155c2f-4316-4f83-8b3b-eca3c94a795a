<template>
  <nd-dialog ref="dialogRef" :title="title" width="60vw" height="70vh">
    <div class="form-container">
      <div class="form-row">
        <div class="form-item">
          <div class="form-label">所属地区</div>
          <div class="form-content">{{ page.form.areaName }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">供应商名称</div>
          <div class="form-content">{{ page.form.name }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">供应商编码</div>
          <div class="form-content">{{ page.form.code }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">联系人</div>
          <div class="form-content">{{ page.form.contactName }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">手机号码</div>
          <div class="form-content">{{ page.form.mobile }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">身份证号</div>
          <div class="form-content">{{ page.form.idNumber }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">状态</div>
          <div class="form-content" v-if="page.form.status == 1">正常</div>
          <div class="form-content" v-if="page.form.status == 0">禁用</div>
        </div>
      </div>
      <el-form :disabled="isReadOnly">
        <div class="form-item1 full-width">
          <div class="form-label">服务地区</div>
          <nd-tree
            :default-checked-keys="page.form.areaIds"
            :default-expanded-keys="page.form.areaIds"
            :data="page.treeData"
            show-checkbox
            node-key="id"
            :props="treeProps"
            style="width: 100%"
            :disabled="isReadOnly"
            class="tree-container"
          />
        </div>
      </el-form>
    </div>

    <template #footer>
      <nd-button @click="close">关闭</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndTree from "@/components/ndTree.vue";

import { ref, reactive, inject } from "vue";
// 定义axios
const $axios = inject("$axios");

const dialogRef = ref(null);

const page = reactive({
  form: {
    areaName: "",
    name: "",
    code: "",
    contactName: "",
    mobile: "",
    idNumber: "",
    status: "",
    serviceArea: "",
    areaIds: [],
  },
});

const emit = defineEmits(["before-close"]);

//
const title = ref("供应商详情");
const isReadOnly = ref(true);

const open = (mode, row) => {
  //  打开对话框
  dialogRef.value?.open();
  getDetail(row.supplierId);
  getTreeData();
};

function getDetail(val) {
  $axios({
    url: "/supplier/details",
    method: "get",
    serverName: "nd-base2",
    data: {
      supplierId: val,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.form.areaName = res.data.data.areaAllPathName;
      page.form.name = res.data.data.name;
      page.form.code = res.data.data.code;
      page.form.contactName = res.data.data.contactName;
      page.form.mobile = res.data.data.mobile;
      page.form.status = res.data.data.status;
      page.form.areaIds = res.data.data.areaIds;
      page.form.idNumber = res.data.data.idNumber;
    }
  });
}
// 修改树配置
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "leaf",
  disabled: "disabled",
};
//服务地区
const getTreeData = () => {
  $axios({
    url: "/supplier/chooseAreaTree",
    method: "get",
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      const data = res.data.data;
      if (isReadOnly.value) {
        const disableNodes = (nodes) => {
          nodes.forEach(node => {
            node.disabled = true; 
            if (node.children && node.children.length > 0) {
              disableNodes(node.children);
            }
          });
        };
        disableNodes(data);
      }
      
      page.treeData = data;
    }
  });
};

const close = () => {
  dialogRef.value.close();
  emit("before-close");
};

// 暴露方法需要对应修改
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.form-container {
  padding: 12px;
  background-color: #fff;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #EAEAEA;
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
  }

  .form-item {
    flex: 0 0 50%;
    padding: 8px;
    display: flex;
    align-items: center;

    .form-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      color: #303133;
    }
  }
  .form-item1 {
    flex: 0 0 50%;
    padding: 8px;
    display: flex;

    &.full-width {
      flex: 0 0 100%;
    }

    .form-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      color: #303133;
    }
  }
}
.tree-container:deep(.is-disabled) {
  .el-checkbox__inner,
  .el-tree-node__content {
    cursor: not-allowed;
    opacity: 0.7;
  }
  
  .el-checkbox__inner {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
  }
}
</style>
