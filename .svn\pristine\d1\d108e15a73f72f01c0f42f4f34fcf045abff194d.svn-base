<template>
  <div class="ndb-page-box">
    <ndb-header :title="title" @title-click="titleClick">
      <slot name="header"></slot>
    </ndb-header>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import ndbHeader from './components/ndbHeader.vue';
import { ref,  reactive } from 'vue';
import { ElMessage as elMessage } from 'element-plus'
import { useRouter } from 'vue-router';
const $router = useRouter();

const emit = defineEmits(['title-click'])

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: "智慧工时系统"
  },
})

function titleClick() {
  emit("title-click");
}

// if (!localStorage.getItem("syToken") || localStorage.getItem("syToken") === "") {
//   elMessage.error('登录超时，请重新登录');
//   $router.push("/");
// }

// defineExpose({
//   showOrHideSearcher
// })
</script>

<style lang='scss' scoped>
.ndb-page-box {
  width: 100%;
  height: 100%;

  .content {
    width: 100%;
    height: calc(100% - 58px);
    background-color: #f7f7f7;
    padding-top: 14px;
    padding-left: 14px;
    padding-right: 14px;
    padding-bottom: 14px;
    overflow-y: auto;
  }
}
</style>