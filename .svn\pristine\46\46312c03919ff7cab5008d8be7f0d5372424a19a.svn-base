import { ElMessageBox } from "element-plus";
import { reactive } from "vue";

export class FormData {
    /** @type {String} */
    nickname;
    /** @type {String} */
    account;
    /** @type {String} */
    realname;
    /** @type {String} */
    lxdh;
    /** @type {String} */
    lxdhJm;
    /** @type {String} */
    idCard;
    /** @type {String} */
    idCardJm;
    /** @type {Number} */
    status;
    /** @type {String} */
    phone;
    /** @type {String} */
    openid;
}

export function useForm() {
    const formData = reactive({
        nickname: "",
        account: "",
        realname: "",
        lxdh: "",
        lxdhJm: "",
        idCard: "",
        idCardJm: "",
        status: 1,
        phone: "",
        openid: ""
    })

    const rulesData = {
        nickname: [
            { required: true, message: "账户名称不能为空", trigger: "blur" },
        ],
        account: [{ required: true, message: "账号不能为空", trigger: "blur" }],
        realname: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        lxdh: [
            { required: true, message: "联系电话不能为空", trigger: "blur" },
            {
                pattern: /^1[3-9]\d{9}$/,
                message: "手机号格式不正确",
                trigger: ["blur", "change"],
            },
        ],
        idCard: [
            { required: true, message: "身份证号不能为空", trigger: "blur" },
            {
                pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X)$)/,
                message: "请输入正确的身份证号",
                trigger: ["blur", "change"],
            },
        ],
        // 添加 status 校验规则
        status: [{ required: true, message: "状态不能为空", trigger: "change" }],
    };

    /**
     * 状态切换二次确认
     * 
     * @returns {void}
     */
    const onStatusInput = (e) => {
        // 判断当前状态
        if (formData.status === 1) {
            ElMessageBox.confirm("账号禁用后将无法登录小程序，确定禁用？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                formData.status = 0;
            }).catch(() => { })
        } else {
            formData.status = 1;
        }
    }

    /**
     * 重置数据
     * 
     * @returns {void}
     */
    const resetData = () => {
        formData.nickname = "";
        formData.account = "";
        formData.realname = "";
        formData.lxdh = "";
        formData.lxdhJm = "";
        formData.idCard = "";
        formData.idCardJm = "";
        formData.status = 1;
        formData.phone = "";
        formData.openid = "";
    }

    return {
        formData,
        rulesData,
        onStatusInput,
        resetData
    }
}