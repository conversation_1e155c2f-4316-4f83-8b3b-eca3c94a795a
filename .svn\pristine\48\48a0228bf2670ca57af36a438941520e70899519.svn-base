<template>
  <nd-dialog ref="dialogRef" width="1232px" height="726px" title="售后详情" align-center>
    <div class="main">
      <!-- ---订单状态--- -->
      <div class="statusMain">
        <div class="left">
          <div class="code">
            <span class="text1">订单编号：{{ page.goodsInfo.ddbh }}</span>
            <span class="text2" @click="copy(page.goodsInfo.ddbh)">复制</span>
          </div>
          <div class="code">
            <span class="text1">售后编号：{{ page.goodsInfo.shbh }}</span>
            <span class="text2" @click="copy(page.goodsInfo.shbh)">复制</span>
          </div>
          <div class="status">
            <span style="color: #FF8D02;" v-if="page.goodsInfo.status===1">处理中</span>
            <span style="color: #068324;" v-if="page.goodsInfo.status===2">售后完成</span>
            <span style="color: #999999;" v-if="page.goodsInfo.status===3">售后关闭</span>
          </div>
          <div class="dateline" v-if="page.goodsInfo.status===1">买家已发起售后申请，<span class="min"> 请处理</span> </div>
          <!-- <div class="dateline" v-if="page.goodsInfo.status===2"> 退款金额：<span class="min">{{ page.goodsInfo.ddje }}</span>  </div> -->
          <div class="dateline" v-if="page.goodsInfo.status===3"> 买家撤销售后申请</div>
          <div class="btn" v-if="detailType ===1 && page.goodsInfo.status===1">
            <div class="button" @click="openAgreeRefund(page.goodsInfo)">
              同意退款
            </div>
            <div class="button red-button"  @click="openRefuseRefund(page.goodsInfo)">
              拒绝退款
            </div>
          </div>
        </div>
        <div class="line" v-if="false"></div>
        <div class="right" v-if="false">
          <div class="textarea">
            <span class="label">备注</span>
            <el-input v-model="textarea" class="textareaContent" type="textarea" placeholder="请输入内容" show-word-limit maxlength="500"/>
          </div>
        </div>
      </div>
      <!-- ---售后信息--- -->
      <div class="orderMain">
        <div class="title">
          <div class="br"></div>
          <div class="text">售后信息</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>申请信息</span>
            </div>
            <div class="item2">
              <span>客户信息</span>
            </div>
            <div class="item3">
              <span>订单信息</span>
            </div>
          </div>
          <div class="body">
            <div class="item1">
              <div class="infoItem">售后类型：{{ page.goodsInfo.type===1?'仅退款':page.goodsInfo.type===2?'退货退款':'' }}</div>
              <div class="infoItem">售后编号：{{ page.goodsInfo.shbh }}<span class="text2" @click="copy(page.goodsInfo.shbh)">复制</span></div>
              <template v-for="(item,index) in page.goodsInfo.sqyyList" :key="index">
                <div class="infoItem">申请原因{{index?index:''}}：{{ item.reason }}</div>
                <div class="infoItem">申请描述{{index?index:''}}：
                  {{item.remark}}
                  <div class="img-box">
                    <div class="infoItem-img" v-for="(img,imgIndex) in item.fjList" :key="imgIndex">
                      <img :src="imgFile+img" alt="" />
                    </div>
                  </div>
                </div>
                
              </template>
             
            </div>
            <div class="item2">
              <div class="infoItem">申请人：{{ page.goodsInfo.memberName }}</div>
              <div class="infoItem">联系电话：{{ page.goodsInfo.memberPhone }}</div>
              <div class="infoItem">申请时间：{{ page.goodsInfo.sqsj }}</div>
            </div>
            <div class="item3">
              <div class="infoItem">订单编号：{{ page.goodsInfo.ddbh }}<span class="text2" @click="copy(page.goodsInfo.ddbh)">复制</span></div>
              <div class="infoItem">实付款：￥{{ page.goodsInfo.ddje }}
                <span v-if="page.goodsInfo.status===2">（已退款:￥{{ page.goodsInfo.sjtkje }}）</span>
              </div>
              <div class="infoItem">物流信息：
                <span v-if="page.goodsInfo.wlzt === 0 ">待收件</span>
                <span v-else-if="page.goodsInfo.wlzt === 1 ">已收件</span>
                <span v-else-if="page.goodsInfo.wlzt == 2 ">已发出</span>
                <span v-else-if="page.goodsInfo.wlzt === 3">已签收</span>
                <span  v-else-if="page.goodsInfo.status === 10">丢件</span>
                <span  v-else-if="page.goodsInfo.status === 20">无法联系收货人</span>
                <span @click="lookWl()" class="operationItem">查看物流</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 退款原因 -->
      <div class="tukuanMain" v-if="page.goodsInfo.sfsqsh == 1">
        <div class="title">
          <div class="br"></div>
          <div class="text">退款原因</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>申请原因</span>
            </div>
            <div class="item2">
              <span>申请描述</span>
            </div>
          </div>
          <div class="body" v-for="(reason, index) in page.goodsInfo?.shhVo?.shhDetailVos" :key="index">
            <div class="item1">
              {{ reason.remark }}
            </div>
            <div class="item2">
              {{ reason.remark }}
            </div>
          </div>
        </div>
      </div>
      <!-- ---商品信息--- -->
      <div class="goodMain">
        <div class="title">
          <div class="br"></div>
          <div class="text">商品信息</div>
        </div>
        <div class="card">
          <div class="header">
            <div class="item1">
              <span>商品信息</span>
            </div>
            <div class="item2">
              <span>单价</span>
            </div>
            <div class="item3">
              <span>数量</span>
            </div>
            <div class="item4">
              <span>实付款</span>
            </div>
            <div class="item4">
              <span>发货状态</span>
            </div>
            <div class="item4">
              <span>退货数量</span>
            </div>
            <div class="item5">
              <span>退款金额</span>
            </div>
          </div>
          <div class="body" v-for="(item, index) in page.goodsInfo.skuList" :key="index">
            <div class="item1">
              <div class="goodImg">
                <img :src="imgFile+item.pic" alt="" />
              </div>
              <div class="goodInfo">
                <div class="title">
                  <div class="mallorder-card-center-details-tag">
                    <span v-for="(tag, index) in item.tagVos" :key="index" class="titleLv" :style="`background-color: ${tag.color}`">
                      {{
                        tag.name }} </span>
                  </div>
                  {{ item.name }}
                </div>
                <div class="gg">{{ item.spData }}</div>
              </div>
            </div>
            <div class="item2">
              <div class="infoItem">¥{{ item.dj }}</div>
            </div>
            <div class="item3">
              <div class="infoItem">{{ item.spsl }}</div>
            </div>
            <div class="item4">
              <div class="infoItem">¥{{ item.sfk }}</div>
            </div>
            <div class="item4">
              <div class="infoItem">
                <span style="color: #FF8D02;" v-if="item.fhzt === 0">待收件</span>
                <span style="color: #FF8D02;" v-if="item.fhzt === 1">已收件</span>
                <span style="color: #068324;" v-if="item.fhzt === 2">已发货</span>
                <span style="color: #068324;" v-if="item.fhzt === 3">已签收</span>
                <span style="color: #068324;" v-if="item.fhzt === 10">丢件</span>
                <span style="color: #068324;" v-if="item.fhzt === 20">无法联系收货人</span>
              </div>
            </div>
            <div class="item4">
              <div class="infoItem">{{ item.thsl }}</div>
            </div>
            <div class="item5">
              <div class="infoItem">¥{{ item.tkje }}</div>
            </div>
          </div>
        </div>
         <div class="priceAll">
          <div class="text1">订单总金额：</div>
          <div class="text2">￥{{ page.goodsInfo.ddje }}</div>
        </div>
      </div>
      <!-- 协商记录 -->
      <div class="negotiationRecord">
        <div class="title">
          <div class="br"></div>
          <div class="text">协商记录</div>
        </div>
        <div class="record-content">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in page.goodsInfo.xsList" :key="index" :icon="item.icon" :color="item.color" :size="item.size" :style="{paddingBottom:item.paddingData+'px'}">
              {{ item.name }}
              <div class="left-record">
                <span>{{ item.time }}</span>
              </div>
              <div class="top-record">
                <template v-if="item.node === 1">
                  <span style="padding: 0">售后类型：
                    {{item.type===1?'仅退款':item.type===2?'退货退款':''}}
                  </span>
                  <span style="padding: 0">售后编号：
                    {{item.shhNo}}
                  </span>
                  <span style="padding: 0">申请原因：
                    {{item.reason}}
                  </span>
                </template>
                <!-- 售后完成 -->
                <template v-if="page.goodsInfo.status===2 && item.node === 2">
                  <span style="padding: 0" v-if="item.shWay===1">退款金额：
                    ￥{{item.sjAmount}}
                  </span>
                </template>
              </div>
             
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <template #footer>
      <!-- <nd-button type="primary" icon="FolderChecked" @click="save()">保&nbsp;存</nd-button> -->
      <nd-button type="" icon="Back" @click="close()">返&nbsp;回</nd-button>
    </template>
    <agree-refund ref="agreeRefundRef" @refreshTable="refreshFn"></agree-refund>
    <refuse-refund  ref="refuseRefundRef" @refreshTable="refreshFn"> </refuse-refund>
    <logistics ref="logisticsRef"> </logistics>
  </nd-dialog>

</template>

<script setup>
// 导入公共组件
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch, onUnmounted } from "vue";
// 导入element-plus
import { ElMessage as elMessage } from "element-plus";
import agreeRefund from "./agreeRefund.vue";
import refuseRefund from "./refuseRefund.vue";
import logistics from "./logistics.vue";
// 定义axios
const $axios = inject("$axios");
// 定义ref
const dialogRef = ref(null);
const addFormRef = ref(null);
const agreeRefundRef = ref(null);
const refuseRefundRef = ref(null)
// 定义方法
let myEmit = defineEmits(["refresh"]);
// 同意退款
function openAgreeRefund(data) {
  agreeRefundRef.value.open(data);
}
// 拒绝退款
function openRefuseRefund(data) {
  refuseRefundRef.value.open(data);
}
// 定义page
const page = reactive({
  status: "",
  shhId: '',
  orderId: '',
  goodsInfo: {
    ddbh: '',   //订单编号
    shbh: '',   //售后编号
    status: 1,
    sfsqsh: 0,
    sfqx: 0,
    sqyyList:[],
    skuList:[],
    xsList:[]
  },
  activities:[
    {
      nodeName:'卖家处理',
      submitUserName:'submitUserName',
      dateTime:'2024-12-09 18:12:07',
      dataType:'dataType',
      checkReason:'checkReason',
      icon:'AlarmClock',
      color: "#FF8A00",
    },
    {
      nodeName:'卖家处理',
      submitUserName:'submitUserName',
      dateTime:'2024-12-09 14:12:07',
      dataType:'dataType',
      checkType:'退货',
      icon:'CircleCheck',
      color: "#068324",
    },
    {
      nodeName:'买家取消申请',
      submitUserName:'submitUserName',
      dateTime:'2024-12-09 14:12:07',
      dataType:'dataType',
      checkType:'退货',
      icon:'close',
      color: "#E50001",
    },
  ]
});

// 表单校验规则
const rules = reactive({
  name: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
});
const imgFile=ref('')
const detailType=ref('')
// 打开弹窗
function open(row,type) {
  imgFile.value=window.ipConfig.base2+'/common/download?token='+localStorage.getItem("syToken")+'&path='
  page.shhId = row.shhId;
  page.orderId = row.orderId;
  detailType.value=type
  getDetail();
  dialogRef.value.open();
}

// 获得详情
function getDetail() {
  $axios({
    url: "/sh/shDetails",
    serverName: 'nd-base2',
    method: "get",
    data: {
      shhId: page.shhId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      res.data.data.xsList[0].icon='CircleCheck'
      res.data.data.xsList[0].color='#068324'
      if(res.data.data.xsList[1].shWay===2){
        // 不予处理
        res.data.data.xsList[1].icon='close'
        res.data.data.xsList[1].color='#E50001'
      }else if(res.data.data.status===1){
        // 处理中
        res.data.data.xsList[1].icon='AlarmClock'
        res.data.data.xsList[1].color='#FF8A00'
      }else if(res.data.data.status===2){
        // 售后完成
        res.data.data.xsList[1].icon='CircleCheck'
        res.data.data.xsList[1].color='#068324'
      }else if(res.data.data.status===3){
        // 售后关闭
        res.data.data.xsList[1].icon='CircleCheck'
        res.data.data.xsList[1].color='#068324'
      }
      res.data.data.xsList.forEach(item => {
        if(item.reason){
          item.paddingData=60+(Math.ceil(item.reason.length/65))*20
        }else{
          item.paddingData=60
        }
      });
      page.goodsInfo = res.data.data;
    }
  });
}
// 更新列表
const refreshFn=()=>{
  getDetail()
  // 关闭弹框
  myEmit("refresh");
}
const logisticsRef=ref(null)
// 查看物流
function lookWl() {
  logisticsRef.value.open(page.orderId);
}
// 关闭
const close = () => {
  dialogRef.value.close();
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 清空表单
  page.goods = {
    id: "",
    name: "",
    files: [],
  };
};

// 复制
const copy = (text) => {
  const tempInput = document.createElement('textarea');
  tempInput.value = text;
  tempInput.style.position = 'fixed';
  tempInput.style.opacity = 0;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand('copy');
  tempInput.remove();
  alert('复制成功！');
}
// 定义倒计时的 ref
const countdown = ref('');
let timer = null;
// 获取订单待支付时间
const getOrderTime = (time) => {
  if (timer) {
    clearInterval(timer);
  }
  // 假设订单在下单后 30 分钟内未支付则关闭，可根据实际情况修改
  const orderCreateTime = new Date(time).getTime();
  const expireTime = orderCreateTime + 30 * 60 * 1000;
  const updateCountdown = () => {
    const now = new Date().getTime();
    const remainingTime = expireTime - now;
    if (remainingTime <= 0) {
      clearInterval(timer);
      // countdown.value = '00:00';
      countdown.value = '0 分 0 秒';
      return;
    }
    const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);
    // countdown.value = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    countdown.value = `${minutes} 分 ${seconds} 秒`;
  };
  // 立即更新一次倒计时
  updateCountdown();
  // 每秒更新一次倒计时
  timer = setInterval(updateCountdown, 1000);
  return countdown.value;
}
// 暴露方法给父组件
defineExpose({
  open,
});
// 在组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style lang="scss" scoped>
:deep(.nd-dialog-box) {
  padding: 0 !important;
}

:deep(.el-dialog__body) {
  background-color: #f7f7f7;
}

.main {
  width: 100%;
  min-height: 866px;
  background-color: #f7f7f7;
  display: flex;
  align-content: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  padding: 12px;

  // 订单状态
  .statusMain {
    width: 100%;
    min-height: 206px;
    border-radius: 5px;
    padding: 12px 24px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    display: flex;
    align-content: center;
    align-items: center;

    .left {
      width: 284px;
      height: 182px;

      .code {
        width: 100%;
        height: 24px;
        font-size: 14px;

        .text1 {
          color: #999999;
        }

        .text2 {
          margin-left: 6px;
          color: #0098FF;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .status {
        width: 100%;
        height: 24px;
        font-size: 22px;
        font-weight: 600;
        color: red;
        margin-top: 20px;
      }

      .dateline {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 13px;
        font-weight: 400;
        color: #333;
        margin-top: 10px;
        letter-spacing: 0px;

        .min {
          color: #FF0001;
        }
      }

      .btn {
        width: 100%;
        height: 32px;
        margin-top: 10px;
        display: flex;


        .button {
          width: 80px;
          height: 32px;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          // padding: 4px 16px;
          color: #FFFFFF;
          gap: 8px;
          background: #068324;
          z-index: 2;
          cursor: pointer;
        }
        .red-button{
          background: #E50001;
          margin-left: 6px;
        }
      }
    }

    .line {
      width: 1px;
      height: 110px;
      background-color: #ebeef5;
      margin: 0 50px;
    }

    .right {
      width: 740px;
      height: 182px;
      display: flex;
      flex-wrap: wrap;


      .textarea {
        width: 100%;
        display: flex;
        align-items: center;

        .label {
          width: 66px;
          height: 22px;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          text-align: right;
          color: #555555;
          margin-right: 10px;
          z-index: 1;
        }

        .textareaContent {
          width: 658px;
          height: 80px !important; // 强制设置容器高度

          :deep(.el-textarea__inner) {
            height: 80px !important;
            min-height: 80px !important;
          }
        }
      }
    }
  }

  // 售后信息
  .orderMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #303133;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #F9F9F9;
      }

      .body {
        width: 100%;
        min-height: 112px;
        display: flex;
      }

      .item1 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;

        .edit {
          margin-left: 6px;
          color: #0098FF;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .item3 {
        flex: 1;
        padding: 8px 12px;
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;
        .text2 {
          margin-left: 6px;
          color: #0098FF;
          text-decoration: underline;
          cursor: pointer;
        }
        .operationItem {
          color: #0b8df1;
          margin-bottom: 6px;
          margin-left: 10px;
          cursor: pointer;
          text-decoration: underline;
        }

        &:last-child {
          margin-bottom: 0px;
        }
      }
      .img-box{
        display: flex;
        width: 350px;
        gap: 10px;
        flex-wrap: wrap;
        .infoItem-img{
          width: 90px;
          height: 90px;
          min-width: 90px;
          min-height: 90px;
          img{
            width:100%;
            height: 100%;
          }
        }
      }
    }
  }

  //退款原因
  .tukuanMain {
    width: 100%;
    min-height: 208px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #303133;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #F9F9F9;
      }

      .body {
        width: 100%;
        min-height: 112px;
        display: flex;
      }

      .item1 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;

        .edit {
          margin-left: 6px;
          color: #0098FF;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }


      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;


        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
  }

  // 商品信息
  .goodMain {
    width: 100%;
    min-height: 245px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;

    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .card {
      border: 1px solid #ebeef5;
      font-size: 14px;
      color: #303133;

      .header {
        width: 100%;
        height: 38px;
        display: flex;
        border-bottom: 1px solid #ebeef5;
        background-color: #F9F9F9;
        text-align: center;
      }

      .body {
        width: 100%;
        height: 112px;
        display: flex;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }
      }

      .item1 {
        flex: 4;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
        display: flex;

        .goodImg {
          width: 90px;
          height: 90px;
          border-radius: 4px;

          img {
            width: 100%;
            height: 100%;

          }
        }

        .goodInfo {
          // width: 100%;
          text-align: left;
          margin-left: 10px;
          flex: 1;
          .title {
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 10px;
            display: flex;
            max-height: 40px;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-all;
          }
        }

      }

      .item2 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5;
      }

      .item3 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5
      }

      .item4 {
        flex: 1;
        padding: 8px 12px;
        border-right: 1px solid #ebeef5
      }

      .item5 {
        flex: 1;
        padding: 8px 12px;
      }

      .infoItem {
        width: 100%;
        line-height: 20px;
        margin-bottom: 10px;
        text-align: center;

        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
    .priceAll {
      width: 100%;
      height: 24px;
      display: flex;
      justify-content: flex-end;
      align-content: center;
      align-items: center;

      .text1 {
        font-size: 14px;
        color: #333333;
        z-index: 0;
      }

      .text2 {
        opacity: 1;
        font-family: Microsoft YaHei;
        font-size: 22px;
        font-weight: bold;
        color: #ff0001;
        margin-left: 30px;
      }
    }
  }

  .negotiationRecord {
    width: 100%;
    min-height: 150px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
    gap: 16px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    margin-top: 12px;
    .title {
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;

      .br {
        width: 2px;
        height: 16px;
        background-color: #068324;
        margin-right: 6px;
      }

      .text {
        height: 24px;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: #444444;
        z-index: 1;
      }
    }

    .record-content {
      height: 100%;
      margin-left: 115px;
      margin-top: 15px;
      position: relative;
      font-size: 14px;
      .left-record{
        position: absolute;
        top: 0;
        left: -115px;
      }

      .top-record {
        position: absolute;
        top: 22px;
        left: 28px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        color: #999;
        font-size: 14px;

        span {
          font-size: 14px;
          padding: 0 8px;
          line-height: 1.4;
        }
      }

      .bottom-record {
        position: absolute;
        top: 42px;
        left: 26px;
        display: flex;
        justify-content: flex-end;
        color: #999;
        font-size: 12px;

        span {
          padding: 0 2px;
        }
      }

      .el-timeline {
        height: 100%;
      }
      /* .el-timeline-item {
        padding-bottom: 60px;
      } */

      :deep(.el-timeline-item__content) {
        font-size: 14px;
        color: #333;
        display: flex;
        align-items: center;
      }

      :deep(.el-timeline-item__timestamp) {
        font-size: 14px;
        color: #999;
      }
      :deep(.el-timeline-item__tail) {
        left: 5px;
      }

      :deep(.el-timeline-item__node--large) {
        left: -3px;
        width: 16px;
        height: 16px;
      }

      :deep(.el-timeline-item__node) {
        width: 14px;
        height: 14px;
      }
      :deep(.el-timeline-item__icon) {
        font-size: 12px;
      }
    }
  }
}

.mallorder-card-center-details-tag {
  display: flex;

}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 2px;
  background: #20B203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;

}

.gg {
  // 省略
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}
</style>
