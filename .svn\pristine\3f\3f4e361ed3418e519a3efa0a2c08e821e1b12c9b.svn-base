import { reactive } from "vue";
import axios from "@/http/index";
import { ElMessage } from "element-plus";

export class DialogData {
    /** @type {string} */
    title;
}

export function useDialog({ dialogRef = null, cancelRef = null, dispatchFormRef = null, emit = null }) {

    const dialogData = reactive({
        title: "派单",
        yzhList: [],
    })

    const dispatchParam = reactive({
        memberId: "",
        demandId: "",
    })

    const dispatchRules = reactive({
        memberId: [
            { required: true, message: "请选择养殖户", trigger: "change" }
        ],
    })

    /**
     * open
     * 
     * @returns {void}
     */
    const open = (demandId) => {
        dispatchParam.demandId = demandId;
        getYzhList();
        dialogRef && dialogRef.value.open();
    }

    /**
     * close
     * 
     * @returns {void}
     */
    const close = () => {
        console.log("close");
        dispatchParam.memberId = "";
        dispatchParam.demandId = "";
        dialogData.yzhList = [];
        dialogRef && dialogRef.value.close();
    }

    /**
     * cancel
     * 
     * @returns {void}
     */
    const cancel = () => {
        console.log("cancel");

        cancelRef && cancelRef.value.open(dispatchParam.demandId);
    }

    /**
     * 获取养殖户列表
     * 
     * @returns {void}
     */
    const getYzhList = (query = null) => {
        axios({
            url: "/buy/demand/yzh/findAll",
            method: "GET",
            serverName: "nd-base2",
            params: {
                nicknameLike: query?.trim() ?? ""
            }
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return;
            }

            dialogData.yzhList = r.data.data;
        })
    }

    /**
     * 派单
     * 
     * @returns {void}
     */
    const dispatch = () => {

        dispatchFormRef && dispatchFormRef.value.validate(e => {
            if (e) {
                axios({
                    url: "/buy/demand/assign",
                    method: "POST",
                    serverName: "nd-base2",
                    data: {
                        demandId: dispatchParam.demandId,
                        memberId: dispatchParam.memberId,
                    }
                }).then(r => {
                    if (r.data.code !== 2000) {
                        ElMessage.error(r.data.message);
                        return;
                    }

                    emit && emit("refresh");

                    close();
                })
            }
        })
    }

    return {
        dialogData,
        dispatchParam,
        dispatchRules,
        open,
        close,
        cancel,
        getYzhList,
        dispatch
    }
}