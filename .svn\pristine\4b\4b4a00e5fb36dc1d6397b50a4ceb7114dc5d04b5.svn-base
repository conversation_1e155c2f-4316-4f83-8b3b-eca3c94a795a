<template>
  <div class="nd-search-more-item-box">
    <div class="left-box" :style="{ width: width }"  :class="letterSpacing ? 'letter-spacing' : ''">
      <span>{{title}}</span>
    </div>
    <div class="right-box" :style="{ width: 'calc(100% - ' + width + ')' }">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "未命名",
    },
    width: {
      type: String,
      default: "100px",
    },
  },
  data() {
    return {
      letterSpacing: false,
    };
  },
  methods: {
  }
};
</script>

<style scoped lang="scss">
.nd-search-more-item-box {
  width: auto;
  height: 100%;
  display: flex;
  flex-direction: row;
  // justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  .left-box {
    // width: 58px;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding-right: 10px;
    font-size: 14px;
    color: #555555;
    padding-left: 34px;
    line-height: 14px;
  }
  .letter-spacing {
    letter-spacing: -1.03px;
  }
  .right-box {
    // width: calc(100% - 58px);
    height: 100%;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
  }
}
</style>