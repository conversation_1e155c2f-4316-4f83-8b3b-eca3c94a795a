import { reactive } from "vue";
import axios from "@/http/index";
import { FormData } from "./useForm";
import { ElMessage } from "element-plus";

/**
 * 
 * @param {Object} param0
 * @param {FormData} param0.formData 
 * @param {Function} param0.clear
 * @returns 
 */
export function useDialog({
    dialogRef = null,
    formData = null,
    clear = null,
    emits = null
}) {

    const dialogData = reactive({
        title: "",
        memberId: "",
        type: ""
    })

    /**
     * open
     * 
     * @param {Object} param0
     * @param {String} param0.type
     * @returns {void}
     */
    const open = async ({ type, memberId }) => {
        dialogData.type = type;
        dialogData.memberId = memberId;
        if (type === "add") {
            dialogData.title = "新增";
        }

        dialogRef && dialogRef.value.open()
    }

    /**
     * close
     * 
     * @returns {void}
     */
    const close = () => {
        emits && emits("refresh");
        clear && clear();
        dialogRef && dialogRef.value.close();
    }

    /**
     * 提交
     * 
     * @returns {void}
     */
    const submit = ({ formRef }) => {
        formRef && formRef.validate(async (valid) => {
            console.log(valid);
            if (valid) {
                if (await submitApi()) {
                    close();
                };
            }
        });
    }

    /**
     * 提交接口
     * 
     * @returns {boolean}
     */
    const submitApi = () => {
        const data = {
            memberId: dialogData.memberId,
            pactName: formData.pactName,
            beginDate: formData.beginDate,
            endDate: formData.endDate,
            fileList: formData.fileList
        }

        return axios({
            url: "/yhgl/yzhgl/savePact",
            method: "POST",
            serverName: "nd-base2",
            data
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return false;
            }
            return true;
        }).catch(e => {
            return false;
        })
    }

    return {
        dialogData,
        open,
        close,
        submit
    }
}