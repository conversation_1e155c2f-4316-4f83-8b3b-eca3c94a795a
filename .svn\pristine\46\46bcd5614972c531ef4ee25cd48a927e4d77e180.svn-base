<template>
    <nd-dialog
        ref="dialogRef"
        :title="dialogData.title"
        width="60vw"
        :before-close="close"
    >
        <div class="box" v-loading="dialogData.loading">
            <div class="main">
                <el-form
                    :model="formData"
                    :rules="dialogData.type === 'detail' ? null : rulesData"
                    label-width="120px"
                    ref="formRef"
                >
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="账户名称" prop="nickname">
                                <template v-if="dialogData.type !== 'detail'">
                                    <nd-input
                                        v-model.trim="formData.nickname"
                                        placeholder="请输入账户名称"
                                        maxlength="10"
                                        clearable
                                        style="width: 100%"
                                    />
                                </template>
                                <template v-else>
                                    {{ formData.nickname }}
                                </template>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="账号" prop="account">
                                <template v-if="dialogData.type !== 'detail'">
                                    <nd-input
                                        v-model.trim="formData.account"
                                        placeholder="请输入账号"
                                        maxlength="20"
                                        clearable
                                        style="width: 100%"
                                    />
                                </template>
                                <template v-else>
                                    {{ formData.account }}
                                </template>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="姓名" prop="realname">
                                <template v-if="dialogData.type !== 'detail'">
                                    <nd-input
                                        v-model.trim="formData.realname"
                                        placeholder="请输入姓名"
                                        maxlength="10"
                                        clearable
                                        style="width: 100%"
                                    />
                                </template>
                                <template v-else>
                                    {{ formData.realname }}
                                </template>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="联系电话" prop="lxdh">
                                <template v-if="dialogData.type !== 'detail'">
                                    <nd-input
                                        v-model.trim="formData.lxdh"
                                        placeholder="请输入联系电话"
                                        clearable
                                        style="width: 100%"
                                        maxlength="11"
                                        type2="number8"
                                    />
                                </template>
                                <template v-else>
                                    <!-- {{ formData.lxdh }} -->
                                    <EncryptionDecryption
                                        :ciphertext="formData.lxdh"
                                        :keyText="formData.lxdhJm"
                                        justifyContent="flex-start"
                                    />
                                </template>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="身份证号" prop="idCard">
                                <template v-if="dialogData.type !== 'detail'">
                                    <nd-input
                                        v-model.trim="formData.idCard"
                                        maxlength="18"
                                        placeholder="请输入身份证号"
                                        clearable
                                        style="width: 100%"
                                    />
                                </template>
                                <template v-else>
                                    <!-- {{ formData.idCard }} -->
                                    <EncryptionDecryption
                                        :ciphertext="formData.idCard"
                                        :keyText="formData.idCardJm"
                                        justifyContent="flex-start"
                                    />
                                </template>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="状态" prop="status">
                                <ndRadioGroup
                                    :model-value="formData.status"
                                    @input.stop="onStatusInput"
                                    :disabled="dialogData.type === 'detail'"
                                >
                                    <ndRadio :value="1">正常</ndRadio>
                                    <ndRadio :value="0">禁用</ndRadio>
                                </ndRadioGroup>
                            </el-form-item>
                        </el-col>

                        <template v-if="dialogData.type === 'detail'">
                            <el-col :span="12">
                                <el-form-item label="微信号">
                                    {{ formData.openid }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="手机号码">
                                    <template #label>
                                        <div
                                            style="
                                                display: flex;
                                                align-items: center;
                                            "
                                        >
                                            手机号码
                                            <el-tooltip
                                                content="指小程序微信授权获取的手机号码"
                                                placement="top"
                                            >
                                                <el-icon
                                                    style="
                                                        margin-left: 5px;
                                                        cursor: pointer;
                                                    "
                                                >
                                                    <QuestionFilled />
                                                </el-icon>
                                            </el-tooltip>
                                        </div>
                                    </template>
                                    {{ formData.phone }}
                                </el-form-item>
                            </el-col>
                        </template>
                    </el-row>
                </el-form>
                <div class="table-header">
                    <div class="table-title">协议附件</div>
                    <nd-button
                        v-if="dialogData.type !== 'detail'"
                        type="primary"
                        @click="
                            openAddAgreementDialog({
                                dialogRef: addAgreementDialogRef,
                                param: {
                                    memberId: dialogData.memberId,
                                    type: dialogData.type,
                                },
                            })
                        "
                        >新增协议</nd-button
                    >
                </div>
                <nd-table
                    :data="argeementData.list"
                    style="margin-top: 10px"
                    v-loading="argeementData.loading"
                >
                    <el-table-column
                        show-overflow-tooltip
                        type="index"
                        label="序号"
                        width="80"
                        fixed="left"
                    />
                    <el-table-column
                        show-overflow-tooltip
                        prop="pactName"
                        label="协议名称"
                        min-width="120"
                    />
                    <el-table-column
                        show-overflow-tooltip
                        prop="beginDate"
                        label="协议开始日期"
                        align="center"
                        min-width="150"
                    />
                    <el-table-column
                        show-overflow-tooltip
                        prop="endDate"
                        label="协议结束日期"
                        align="center"
                        min-width="150"
                    />
                    <el-table-column
                        show-overflow-tooltip
                        prop="attachment"
                        label="附件"
                        min-width="80"
                        align="center"
                    >
                        <template #default="scope">
                            <div
                                @click="
                                    previewFile({
                                        ref: previewDialogRef,
                                        param: {
                                            files: scope.row.fileList ?? [],
                                        },
                                    })
                                "
                                class="link-text"
                            >
                                {{ scope.row.fileList.length }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        show-overflow-tooltip
                        prop="status"
                        label="状态"
                        min-width="80"
                        align="center"
                    >
                        <template #default="scope">
                            {{ statusMap.get(scope.row.status) }}
                        </template>
                    </el-table-column>
                    <!-- 添加操作列 -->
                    <el-table-column
                        label="操作"
                        align="center"
                        fixed="right"
                        v-if="dialogData.type !== 'detail'"
                    >
                        <template #default="scope">
                            <nd-button
                                type="edit"
                                @click="
                                    deleteArgeement({
                                        pactId: scope.row.pactId,
                                        callback: () =>
                                            getArgeementList({
                                                memberId: dialogData.memberId,
                                            }),
                                    })
                                "
                                >删除</nd-button
                            >
                        </template>
                    </el-table-column>
                </nd-table>
            </div>
        </div>
        <template #footer>
            <template v-if="dialogData.type !== 'detail'">
                <nd-button type="primary" @click="submit({ formRef: formRef })"
                    >提交</nd-button
                >
                <nd-button @click="close">取消</nd-button>
            </template>
            <template v-else>
                <nd-button @click="close">关闭</nd-button>
            </template>
        </template>
    </nd-dialog>
    <!-- 引入新增协议弹窗组件 -->
    <AddAgreementDialog
        ref="addAgreementDialogRef"
        @refresh="getArgeementList({ memberId: dialogData.memberId })"
    />
    <previewAttanchment ref="previewDialogRef" />
</template>

<script setup>
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndTable from "@/components/ndTable.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import AddAgreementDialog from "../AddAgreement/index.vue"; // 引入新增协议弹窗组件
import previewAttanchment from "../PreviewAttanchment/index.vue";
import EncryptionDecryption from "../EncryptionDecryption/index.vue";

import { ref, reactive } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";

// hooks
import { useDialog } from "../../hooks/addOrEdit/useDialog";
import { useForm } from "../../hooks/addOrEdit/useForm";
import { useArgeement } from "../../hooks/addOrEdit/useArgeement";

// emit
const emits = defineEmits(["addRefresh", "upDateRefresh"]);

// ref
const dialogRef = ref(null);
const formRef = ref(null);
const addAgreementDialogRef = ref(null);
const previewDialogRef = ref(null);

// useForm
const { formData, rulesData, onStatusInput, resetData } = useForm();

// useArgeement
const {
    argeementData,
    statusMap,
    getArgeementList,
    deleteArgeement,
    previewFile,
} = useArgeement();

// useDialog
const { dialogData, open, close, openAddAgreementDialog, submit } = useDialog({
    dialogRef: dialogRef,
    getArgeementList: getArgeementList,
    formData: formData,
    resetData: resetData,
    emits: emits,
});

defineExpose({
    open,
    close,
});
</script>

<style lang="scss" scoped>
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 10px;
}

.table-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}
.link-text {
    cursor: pointer;
    color: #068324;
}

.box {
    width: 100%;
    height: 100%;
    padding: 12px;

    .main {
        background: #ffffff;
        border: 1px solid #eaeaea;
        border-radius: 5px;
        padding: 12px;
    }
}

:deep(.nd-radio-box .el-radio) {
    height: 100%;
}

:deep(.el-form-item--label-right .el-form-item__label) {
    color: #555;
}

:deep(.el-form-item__content) {
    color: #333;
}

:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
