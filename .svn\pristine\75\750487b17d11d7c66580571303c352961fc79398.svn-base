var webScan;
var device = "";
$(function () {
  webScan = new WebScan({
    url: 'http://127.0.0.1:18989/WebScan',
    wsUrl: 'http://127.0.0.1:28989/',
    licence: ''
  })
  window.webScanner = webScan;
})
function getParams() {
  webScan.getParams(function (result) {
    if (result.code != 200) {
      alert("获取参数失败，返回错误：" + result.msg);
      return;
    }
    console.log(result.data);
  })
}
  // //扫描之前需要先设定参数，在参数中选定扫描仪
  // function startScan() {
  //   if (!isInit) {
  //     alert('请先进行初始化');
  //     return;
  //   }
  //   webScan.startScan(scanCallBack, null);
  // }
  // function scanCallBack(result) {
  //   var code = result.code;
  //   if (code == 201) {
  //     $("#images").append('<img src="' + result.image + '" style="width:200px;margin-left:20px">')
  //   }
  //   if (code == 500) {
  //     alert(result.msg);
  //   }
  // }