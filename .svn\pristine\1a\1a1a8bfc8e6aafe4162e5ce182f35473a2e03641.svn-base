<template>
  <el-tooltip effect="dark" placement="top-start" v-bind="$attrs">
    <div class="icon" :style="{ marginLeft: marginLeft }"></div>
    <template v-slot:content>
      <slot></slot>
    </template>
  </el-tooltip>
</template>

<script setup>
// props
const props = defineProps({
  marginLeft: {
    type: String,
    default: "0px",
  },
});
</script>

<style lang="scss" scoped>
.icon {
  width: 14px;
  height: 14px;
  background: url("@/assets/images/formSetup/questionFilled.png") no-repeat center center;
  background-size: 100% 100%;
}
</style>
