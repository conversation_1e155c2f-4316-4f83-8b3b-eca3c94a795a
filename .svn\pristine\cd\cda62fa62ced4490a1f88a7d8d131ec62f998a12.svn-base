<template>
  <div class="nd-pagination-box">
    <el-pagination :page-sizes="pagination.pageSizes" layout="total, sizes, prev, pager, next, jumper" v-bind="$attrs" popper-class="nd-pagination-popper"> </el-pagination>
  </div>
</template>

<script setup>
import { reactive } from "vue";
const props = defineProps({
  // total: {
  //   type: Number,
  //   default: 100,
  // },
});
const pagination = reactive({
  pageSizes: [10, 30, 50, 100],
});
</script>

<style lang="scss">
.nd-pagination-popper {
  .el-select-dropdown__item.selected {
    color: #068324 !important;
  }

  .el-select .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px #068324 inset !important;
  }
  .el-scrollbar{
    .el-select-dropdown__wrap{
      .is-selected{
        color: #068324 !important;
      }
      .is-hovering{
        background-color: #f0fef5 !important;
      }
    }
  }

}
</style>
<style lang="scss" scoped>
.nd-pagination-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  font-size: 12px;
  background-color: #f9f9f9;

  :deep(.el-pagination) {
    --el-pagination-font-size: 12px;
  }

  :deep(.el-input) {
    height: 26px;
    font-size: 12px;
  }

  :deep(.el-pager li) {
    margin-right: 10px;
    height: 26px;

    &:hover {
      color: #068324;
    }
  }

  :deep(.el-pager li.is-active) {
    color: #ffffff;
    background: #068324;
  }

  :deep(.el-pager li:focus-visible) {
    outline: 1px solid transparent;
  }

  :deep(.el-pagination .btn-prev) {
    margin-right: 10px;
    height: 26px;

    &:hover {
      color: #068324;
    }
  }

  :deep(.el-pagination .btn-next) {
    height: 26px;

    &:hover {
      color: #068324;
    }
  }

  // :deep(.el-input__wrapper .is-focus) {
  //   box-shadow: 0 0 0 1px #068324 inset !important;
  // }
  :deep(.is-focused){
    box-shadow: 0 0 0 1px #068324 inset !important;
  }
}
</style>
