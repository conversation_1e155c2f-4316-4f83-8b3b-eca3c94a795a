<template>
  <el-dropdown-item :disabled="_disabled" v-bind="$attrs">
    <slot></slot>
  </el-dropdown-item>
</template>
<script setup>
import { ref, watch, onMounted } from "vue";

const props = defineProps({
  // type: { // 类型
  //   type: String,
  //   default: "default",
  // },
  disabled: {
    // 只读
    type: Boolean,
    default: false,
  },
  authKey: {
    // 权限key
    type: String,
    default: "",
  },
});

// 是否禁用
var _disabled = ref(false);

// 监视disabled
watch(() => props.disabled, (newValue, oldValue) => {
  _disabled.value = newValue;
}, {
  immediate: true,
});

// 判断按钮权限
if (localStorage.getItem("syAuths")) {
  var auth = localStorage.getItem("syAuths").split(",");
  auth.map((item) => {
    if (props.authKey === item) {
      _disabled.value = true;
    }
  });
}
</script>
<style lang="scss" scoped>
// .nd-checkbox-box {
//   :deep(.el-checkbox__label) {
//     padding-left: 6px;
//     font-size: 14px;
//   }

//   :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
//     font-family: Microsoft YaHei;
//     font-weight: 400;
//     color: #555555;
//   }
// }
</style>
