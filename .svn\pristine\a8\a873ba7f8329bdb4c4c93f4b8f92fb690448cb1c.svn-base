<template>
  <nd-dialog ref="dialogRef" width="60vw" height="48vh" :title="title" align-center>
    <nd-tabs :tabList="tabList" @tab-click="changeTab" />

    <el-form ref="addFormRef" :model="form" :rules="rules" class="add-box" v-if="tabActive == 0">
      <el-row>
        <el-col :span="12">
          <el-form-item label="项目名称" label-width="140px" prop="projectName">
            {{ form.projectName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="功能模块" label-width="140px" prop="modelName" :debounce="0">
            <nd-autocomplete v-model="form.modelName" :fetch-suggestions="querySearchModelName" style="width: 100%" @select="handleModelNameSelect" @change="handleModelNameChange" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="功能菜单名称" label-width="140px" prop="menuName" :debounce="0">
            <nd-autocomplete
              :disabled="form.modelName ? false : true"
              v-model="form.menuName"
              :fetch-suggestions="querySearchMenuName"
              style="width: 100%"
              @select="handleMenuNameSelect"
              @change="handleMenuNameChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="功能点名称" label-width="140px" prop="functionName">
            <nd-input :disabled="form.modelName && form.menuName ? false : true" v-model="form.functionName" width="100%"></nd-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="功能点介绍" label-width="140px" style="display: flex; align-items: center" prop="remarks">
            <nd-input type="textarea" v-model="form.remarks" maxlength="200" show-word-limit width="100%"> </nd-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="业务类别" label-width="140px" prop="classis">
            <nd-select filterable v-model="form.classis" placeholder=" " width="100%">
              <el-option label="EIF" value="EIF"></el-option>
              <el-option label="ILF" value="ILF"></el-option>
              <el-option label="EI" value="EI"></el-option>
              <el-option label="EO" value="EO"></el-option>
              <el-option label="EQ" value="EQ"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="复用度" label-width="140px" prop="repeat">
            <nd-select v-model="form.repeat" placeholder=" " width="100%">
              <el-option label="高" :value="1"></el-option>
              <el-option label="中" :value="2"></el-option>
              <el-option label="低" :value="3"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 需求 type=1 -->
      <el-row style="position: relative">
        <div style="position: absolute; top: 6px; left: 38px">需求</div>
        <el-col :span="12">
          <el-form-item label="复杂度" label-width="140px" prop="complex1">
            <nd-select v-model="form.pointDtoList[0].complex" placeholder=" " width="100%" clearable>
              <el-option label="高" :value="1"></el-option>
              <el-option label="普" :value="2"></el-option>
              <el-option label="低" :value="3"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人" label-width="140px" prop="dutyId1">
            <nd-select v-model="form.pointDtoList[0].dutyId" placeholder=" " width="100%" clearable filterable>
              <el-option v-for="item in props.testDutyList" :key="item.dictId" :label="item.dictName" :value="item.dictId"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 前端 type=2 -->
      <el-row style="position: relative">
        <div style="position: absolute; top: 6px; left: 38px">前端</div>
        <el-col :span="12">
          <el-form-item label="复杂度" label-width="140px">
            <nd-select v-model="form.pointDtoList[1].complex" placeholder=" " width="100%" clearable>
              <el-option label="高" :value="1"></el-option>
              <el-option label="普" :value="2"></el-option>
              <el-option label="低" :value="3"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人" label-width="140px">
            <nd-select v-model="form.pointDtoList[1].dutyId" placeholder=" " width="100%" clearable filterable>
              <el-option v-for="item in props.testDutyList" :key="item.dictId" :label="item.dictName" :value="item.dictId"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 后端 type=3 -->
      <el-row style="position: relative">
        <div style="position: absolute; top: 6px; left: 38px">后端</div>
        <el-col :span="12">
          <el-form-item label="复杂度" label-width="140px">
            <nd-select v-model="form.pointDtoList[2].complex" placeholder=" " width="100%" clearable>
              <el-option label="高" :value="1"></el-option>
              <el-option label="普" :value="2"></el-option>
              <el-option label="低" :value="3"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人" label-width="140px">
            <nd-select v-model="form.pointDtoList[2].dutyId" placeholder=" " width="100%" clearable filterable>
              <el-option v-for="item in props.testDutyList" :key="item.dictId" :label="item.dictName" :value="item.dictId"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 测试 type=4 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="测试责任人" label-width="140px" prop="testDutyId">
            <nd-select v-model="form.pointDtoList[3].dutyId" placeholder=" " width="100%" clearable filterable>
              <el-option v-for="item in props.testDutyList" :key="item.dictId" :label="item.dictName" :value="item.dictId"></el-option>
            </nd-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="附件" label-width="140px">
            <nd-upload v-model="form.sourceList" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <nd-button type="primary" icon="FolderChecked" @click="submit('1')">保&nbsp;存</nd-button>
      <nd-button type="" icon="Close" @click="close">关&nbsp;闭</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义方法
let myEmit = defineEmits(["refreshTable"]);
// 定义属性
const props = defineProps({
  testDutyList: Array,
});

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const dialogRef = ref(null);
// 定义对话框的标题
let title = ref("");
// 定义表单数据
const addFormRef = ref(null);
let form = ref({
  pointId: null, // 主键
  projectId: null, // projectId
  projectName: "", // 项目名称
  modelName: "", // 功能模块
  menuName: "", // 功能菜单名称
  functionName: "", // 功能点名称
  remarks: "", // 功能点描述
  classis: "", // 类别 EIF、ILF、EI、EO、EQ，
  repeat: null, // 复用度 1高，2中，3低
  // testDutyId: null, // 测试责任人id
  // testDutyName: "", // 测试责任人姓名
  // 功能类别
  pointDtoList: [
    {
      type: 1, // 功能所属类别 1需求，2前端，3后端，4测试
      complex: null, // 复杂度 1高，2普，3低
      dutyId: "",
      // dutyName: '', // 责任人姓名
    },
    {
      type: 2,
      complex: null,
      dutyId: "",
      // dutyName: '',
    },
    {
      type: 3,
      complex: null,
      dutyId: "",
      // dutyName: '',
    },
    {
      type: 4,
      complex: null,
      dutyId: "",
      // dutyName: '',
    },
  ],
  sourceList: [], // 附件
});

// 获取下拉框需要显示的列表 ===============================================
// 定义连接的列表
let linksModelName = ref([]);
let linksMenuName = ref([]);

const tabActive = ref(0);
const tabList = ref(["招标信息", "公示信息", "立项信息"]);
const changeTab = (e) => tabActive.value = e.index;

// 定义模糊下拉数据列表
let findModelName = ref({
  selectName: [], // 功能模块列表
});
let findMenuName = ref({
  selectName: [], // 功能模块列表
});
// 保存选中的功能模块
let selectedModelName = ref("");
// 获取功能模块模糊下拉数据列表
function getModelNameList() {
  $axios({
    url: "/projectPoint/findPointModeName",
    method: "get",
    // serverName: 'zjd-base',
    data: { projectId: form.value.projectId },
  }).then((res) => {
    // console.log('后端功能模块模糊下拉数据列表', res)
    if (res.data.code === 2000) {
      findModelName.value = res.data.data;
      // 处理数据为el-autocomplete可识别的格式
      if (findModelName.value.selectName) {
        findModelName.value.selectName = findModelName.value.selectName.map((item) => ({ value: item }));
      } else {
        findModelName.value.selectName = [];
      }
      // console.log('本地模糊下拉功能模块列表', findModelName.value.selectName);

      // 保存自动补全输入框模糊搜索的数据列表
      linksModelName.value = findModelName.value.selectName;
    }
  });
}
// 获取功能菜单名称模糊下拉数据列表
function getMenuNameListByModelName() {
  $axios({
    url: "/projectPoint/findPointByModeName",
    method: "get",
    // serverName: 'zjd-base',
    data: { projectId: form.value.projectId, modelName: selectedModelName.value },
  }).then((res) => {
    // console.log('后端功能菜单名称模糊下拉数据列表', res)
    if (res.data.code === 2000) {
      findMenuName.value = res.data.data;
      // 处理数据为el-autocomplete可识别的格式
      if (findMenuName.value.selectName) {
        findMenuName.value.selectName = findMenuName.value.selectName.map((item) => ({ value: item }));
      } else {
        findMenuName.value.selectName = [];
      }
      // console.log('本地模糊下拉功能菜单名称列表', findMenuName.value.selectName);

      // 保存自动补全输入框模糊搜索的数据列表
      linksMenuName.value = findMenuName.value.selectName;
    }
  });
}

// 保存自动补全输入框模糊搜索的数据列表
// 功能模块自动补全搜索
const querySearchModelName = (queryString, cb) => {
  const results = queryString ? linksModelName.value.filter(createFilter(queryString)) : linksModelName.value;
  cb(results);
};
// 功能菜单名称自动补全搜索
const querySearchMenuName = (queryString, cb) => {
  const results = queryString ? linksMenuName.value.filter(createFilter(queryString)) : linksMenuName.value;
  cb(results);
};
const createFilter = (queryString) => {
  return (links) => {
    return links.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
  };
};
// 处理选中的定位搜索点
const handleModelNameSelect = (item) => {
  console.log(item);
  selectedModelName.value = item.value;
  // console.log(selectedModelName.value);
  // 获取功能菜单名称模糊下拉数据
  getMenuNameListByModelName();
};
const handleMenuNameSelect = (item) => {
  // console.log(item)
};
// 处理定位搜索点Input值改变
const handleModelNameChange = (value) => {
  console.log(value);
  if (form.value.modelName) {
    // console.log('有值');
  } else {
    // console.log('没值了');
    form.value.menuName = "";
    form.value.functionName = "";
  }
};
const handleMenuNameChange = (value) => {
  console.log(value);
  if (form.value.menuName) {
    console.log("有值");
  } else {
    console.log("没值了");
    // form.value.menuName = ''
    form.value.functionName = "";
  }
};

// 定义需求，后端，前端的的ref名
const dutyRef1 = ref(null);
const dutyRef2 = ref(null);
const dutyRef3 = ref(null);

// 定义校验规则 ==========================================================
// 自定义校验规则 --- 需求责任人校验
const dutyRule1 = (rule, value, callback) => {
  if (form.value.pointDtoList[0].dutyId) {
    return callback();
  } else {
    return callback(new Error("error"));
  }
};
// 自定义校验规则 --- 需求复杂度校验
const complexRule1 = (rule, value, callback) => {
  if (form.value.pointDtoList[0].complex) {
    return callback();
  } else {
    return callback(new Error("error"));
  }
};
// 自定义校验规则 --- 测试责任人校验
const dutyRule2 = (rule, value, callback) => {
  if (form.value.pointDtoList[3].dutyId) {
    return callback();
  } else {
    return callback(new Error("error"));
  }
};
// 自定义校验规则 --- 正则 --- 功能模块最长不得超过10个汉字
const validateModelName = (rule, value, callback) => {
  const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
  let chineseCharacters = form.value.modelName.match(chineseReg); // 匹配输入字符串中的汉字
  if (chineseCharacters && chineseCharacters.length > 10) {
    return callback(new Error("error"));
  } else {
    return callback();
  }
};
// 自定义校验规则 --- 正则 --- 功能菜单名称最长不得超过10个汉字
const validateMenuName = (rule, value, callback) => {
  const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
  let chineseCharacters = form.value.menuName.match(chineseReg); // 匹配输入字符串中的汉字
  if (chineseCharacters && chineseCharacters.length > 10) {
    return callback(new Error("error"));
  } else {
    return callback();
  }
};
// 自定义校验规则 --- 正则 --- 功能点名称最长不得超过10个汉字
const validateFunctionName = (rule, value, callback) => {
  const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
  let chineseCharacters = form.value.functionName.match(chineseReg); // 匹配输入字符串中的汉字
  if (chineseCharacters && chineseCharacters.length > 50) {
    return callback(new Error("error"));
  } else {
    return callback();
  }
};
// 表单校验规则
const rules = reactive({
  projectName: [{ required: true }],
  modelName: [
    { required: true, message: "请输入功能模块", trigger: "blur" },
    // { min: 1, max: 10, message: "最长不得超过10个汉字", trigger: "blur" },
    { validator: validateModelName, trigger: "blur", message: "最长不得超过10个汉字" },
  ],
  menuName: [
    { required: true, message: "请输入功能菜单名称", trigger: "blur" },
    // { min: 1, max: 10, message: "最长不得超过10个汉字", trigger: "blur" },
    { validator: validateMenuName, trigger: "blur", message: "最长不得超过10个汉字" },
  ],
  functionName: [
    { required: true, message: "请输入功能点名称", trigger: "blur" },
    // { min: 1, max: 50, message: "最长不得超过50个汉字", trigger: "blur" },
    { validator: validateFunctionName, trigger: "blur", message: "最长不得超过50个汉字" },
  ],
  classis: [{ required: true, message: "请选择业务类别", trigger: "blur" }],
  repeat: [{ required: true, message: "请选择复用度", trigger: "blur" }],
  remarks: [
    { required: true, message: "请输入功能点介绍", trigger: "blur" },
    { min: 1, max: 200, message: "最长不得超过200个汉字", trigger: "blur" },
  ],
  dutyId1: [{ required: true, message: "请选择需求责任人", trigger: "blur", validator: dutyRule1 }],
  complex1: [{ required: true, message: "请选择需求复杂度", trigger: "blur", validator: complexRule1 }],
  testDutyId: [{ required: true, message: "请选择测试责任人", trigger: "blur", validator: dutyRule2 }],
});

// 打开弹窗 ==============================================================
const open = (val, pointId, projectId) => {
  // 获取项目id
  form.value.projectId = projectId;
  // 获取项目名称
  $axios({
    url: "/common/userProject",
    method: "get",
  }).then((res) => {
    // console.log(res, '后端返回的项目列表');
    res.data.data.forEach((item) => {
      if (item.projectId === projectId) {
        form.value.projectName = item.projectName;
      }
    });
  });

  // 获取功能模块模糊下拉数据
  getModelNameList();

  // 新建
  if (val === 1) {
    title.value = "新建";
  } else {
    // 编辑
    title.value = "编辑";
    form.value.pointId = pointId;
    $axios({
      url: "/projectPoint/find",
      method: "get",
      // serverName: 'zjd-base',
      data: { pointId: pointId },
    }).then((res) => {
      // console.log('后端返回的编辑数据', res.data.data)
      if (res.data.code === 2000) {
        // delete res.data.data.testDutyName
        form.value = res.data.data;
        // form.value.testDutyId = String(form.value.testDutyId)
        form.value.pointId = pointId;
        for (let i = 0; i < form.value.pointDtoList.length; i++) {
          // 处理默认返回0的数据
          if (form.value.pointDtoList[i].complex === 0) {
            form.value.pointDtoList[i].complex = null;
          }
          if (form.value.pointDtoList[i].dutyId === "0") {
            form.value.pointDtoList[i].dutyId = null;
          }
          // 按需求，前端，后端，测试的type值重排列表（ 1需求，2前端，3后端，4测试）
          form.value.pointDtoList = form.value.pointDtoList.sort((a, b) => a.type - b.type);
        }
        form.value.projectId = res.data.data.projectId;
        console.log("点编辑后，表单的数据为", form.value);
      }
    });
  }
  dialogRef.value.open();
};

// 清空表单
const clear = () => {
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 清空表单
  form.value.pointId = null;
  // form.value.projectId = null;
  form.value.projectName = "";
  form.value.modelName = "";
  form.value.menuName = "";
  form.value.functionName = "";
  form.value.remarks = "";
  form.value.classis = "";
  form.value.repeat = "";
  // form.value.testDutyId = "";
  // form.value.testDutyName = "";
  form.value.pointDtoList = [
    {
      type: 1, // 功能所属类别 默认1， 1需求，2后端，3前端
      complex: null, // 复杂度 默认1，1高，2中，3低
      dutyId: "",
      // dutyName: '', // 责任人姓名
    },
    {
      type: 2,
      complex: null,
      dutyId: "",
      // dutyName: '',
    },
    {
      type: 3,
      complex: null,
      dutyId: "",
      // dutyName: '',
    },
    {
      type: 4,
      complex: null,
      dutyId: "",
      // dutyName: '',
    },
  ];
};

// 关闭弹窗 ==============================================================
const close = () => {
  dialogRef.value.close();
  clear();
};

// 保存提交 ==============================================================
const submit = async () => {
  await addFormRef.value.validate((valid, fields) => {
    // delete form.value.testDutyName
    console.log(form.value.pointDtoList);
    if (valid) {
      // 编辑
      if (form.value.pointId !== null) {
        let params = form.value;
        $axios({
          url: "/projectPoint/update",
          method: "post",
          // serverName: 'zjd-base',
          data: params,
        }).then((res) => {
          if (res.data.code === 2000) {
            console.log("编辑成功");
            // 轻提示
            ElMessage({
              message: "保存成功",
              type: "success",
            });
            // 关闭弹框
            myEmit("refreshTable");
            close();
          } else {
            ElMessage({
              message: res.data.message,
              type: "warning",
            });
          }
        });
      } else {
        // 新建
        let params = form.value;
        $axios({
          url: "/projectPoint/save",
          method: "post",
          // serverName: 'zjd-base',
          data: params,
        }).then((res) => {
          console.log(form.value.projectId, "项目id");
          if (res.data.code === 2000) {
            // 轻提示
            ElMessage({
              message: "保存成功",
              type: "success",
            });
            myEmit("refreshTable");
            // 关闭弹框
            close();
          } else {
            ElMessage({
              message: res.data.message,
              type: "warning",
            });
          }
        });
      }
    } else {
      console.log("校验失败!", fields);
    }
  });
};

// 暴露方法给父组件 =======================================================
defineExpose({
  open,
  clear,
});
</script>

<style lang="scss" scoped>
.add-box {
  padding: 0 60px;

  :deep(.el-textarea__inner) {
    height: 80px;
  }
}
</style>
