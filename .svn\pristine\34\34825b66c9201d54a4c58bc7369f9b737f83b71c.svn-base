<template>
  <div class="nd-input-box" :style="{ width: width }">
    <el-input ref="inputRef" v-bind="$attrs" @input="handleInput"> </el-input>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";

const emits = defineEmits(["input", "update:modelValue"]);

const prop = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px",
  },
  // 类型2（具体参考 handleInput 里的说明）
  type2: {
    type: String,
    default: "",
  },
  // 限制输入的临界值（具体参考 handleInput 里的说明）
  limitData: {
    type: String,
    default: "",
  },
  // // v-model绑定对象属性
  // modelValue: {
  //   type: String,
  //   default: "",
  // },
});

const inputRef = ref(null);
// const inputValue = ref("");

// watch(
//   () => prop.modelValue,
//   (newVal, oldVal) => {
//     inputValue.value = newVal;
//   },
//   {
//     // deep: true,
//     immediate: true,
//   }
// );

// watch(
//   () => inputValue.value,
//   (newVal, oldVal) => {
//     emits("update:modelValue", inputValue.value);
//   }
//   // {
//   //   deep: true,
//   //   immediate: true,
//   // }
// );

const setCursorPos = (start, end) => {
  inputRef.value.focus();
  inputRef.value.selectionStart = start;
  inputRef.value.selectionEnd = end;
};

const focus = () => {
  inputRef.value.focus();
};

function handleInput(params) {
  // 正负数字，带两位小数，比如：-1.23,2.36可以(9位小数)
  if (prop.type2 === "number1") {
    params = caseNumber1(params);
  }
  // 正数字，带两位小数，比如：1.23可以，-1.23不可以(9位小数)
  if (prop.type2 === "number2") {
    params = caseNumber2(params);
  }
  // 正数字，带两位小数，比如：1.23可以，-1.23不可以(7位小数)
  if (prop.type2 === "number02") {
    params = caseNumber02(params);
  }
  // 非负整数，比如：0，1，2可以，-1，-2，1.23，-1.23不可以(9位小数)
  if (prop.type2 === "number3") {
    params = caseNumber3(params);
  }
  // 正整数，比如：1，2可以，0，-1，-2，1.23，-1.23不可以(9位小数)
  if (prop.type2 === "number4") {
    params = caseNumber4(params);
  }
  // 运单号，规则限制英文大小写加数字，长度限制50字
  if (prop.type2 === "number5") {
    params = caseNumber5(params);
  }
  // 正数字，带两位小数,并且小于等于某一个值，比如：1.23可以，-1.23不可以(9位小数)
  if (prop.type2 === "number6") {
    params = caseNumber6(params);
  }
  // 正数，两位小数，整数位最大10位
  if (prop.type2 === "number7") {
    params = caseNumber7(params);
  }
  // 数字（不限制位数）
  if (prop.type2 === "number8") {
    params = caseNumber8(params);
  }
  emits("input", params);
  emits("update:modelValue", params);
}

function caseNumber1(params) {
  var isMinus = true;
  if (params === "") {
    return "";
  }
  if (params == null || params == undefined || params === "") {
    return "";
  }
  // 找到负号
  if (params.indexOf("-") > -1) {
    var b = params.toString().split("-");
    var b1 = b[0];
    var b2 = b[1];
    params = b1 + b2;
    isMinus = true;
  } else {
    isMinus = false;
  }
  // 清除整数部分前面多余的0
  var numbers = params.toString().split("");
  if (numbers.length > 1) {
    if (numbers[0] === "0" && numbers[1] !== ".") {
      params = params.toString().substring(1);
    }
  }
  // 获得整数部分和小数部分
  var a = params.toString().split(".");
  var a1 = a[0];
  var a2 = a[1];
  // 整数只能输入9位
  if (a2 == undefined) {
    if (a1.length > 9) {
      params = a1.substring(0, 9);
    }
  } else {
    if (a1.length > 9) {
      params = a1.substring(0, 9) + "." + a2;
    }
  }
  // 清除"数字"和"."以外的字符
  params = params.replace(/[^-\d.]/g, "");
  // 清除多余的"."
  params = params.replace(/\.{2,}/g, ".");
  params = params.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  // 只能输入两个小数
  params = params.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
  // 最后补上负号
  if (isMinus) {
    params = "-" + params;
  }
  return params;
}

function caseNumber2(params) {
  // 清除整数部分前面多余的0
  var numbers = params.toString().split("");
  if (numbers.length > 1) {
    if (numbers[0] === "0" && numbers[1] !== ".") {
      params = params.toString().substring(1);
    }
  }

  var a = params.toString().split(".");
  var a1 = a[0];
  var a2 = a[1];
  // 整数只能输入9位
  if (a2 == undefined) {
    if (a1.length > 9) {
      params = a1.substring(0, 9);
    }
  } else {
    if (a1.length > 9) {
      params = a1.substring(0, 9) + "." + a2;
    }
  }
  return params
    .replace(/^0[0-9]+/, (params) => params[1])
    .replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, "$1");
}

function caseNumber02(params) {
  // 清除整数部分前面多余的0
  var numbers = params.toString().split("");
  if (numbers.length > 1) {
    if (numbers[0] === "0" && numbers[1] !== ".") {
      params = params.toString().substring(1);
    }
  }

  var a = params.toString().split(".");
  var a1 = a[0];
  var a2 = a[1];
  // 整数只能输入7位
  if (a2 == undefined) {
    if (a1.length > 7) {
      params = a1.substring(0, 7);
    }
  } else {
    if (a1.length > 7) {
      params = a1.substring(0, 7) + "." + a2;
    }
  }
  return params
    .replace(/^0[0-9]+/, (params) => params[1])
    .replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, "$1");
}

function caseNumber3(params) {
  // 清除整数部分前面多余的0
  var numbers = params.toString().split("");
  if (numbers.length > 0) {
    if (numbers[0] === "0" && numbers[1]) {
      params = params.toString().substring(1);
    }
  }

  var a = params.toString().split(".");
  var params = a[0];

  // 整数只能输入9位
  if (params.length > 9) {
    params = params.substring(0, 9);
  }
  // 清除"数字"以外的字符
  return params.replace(/[^\d]/g, "");
}

function caseNumber4(params) {
  // 清除整数部分最前面的0
  var numbers = params.toString().split("");
  if (numbers.length > 0) {
    if (numbers[0] === "0") {
      params = params.toString().substring(1);
    }
  }

  var a = params.toString().split(".");
  var params = a[0];

  // 整数只能输入9位
  if (params.length > 9) {
    params = params.substring(0, 9);
  }
  // 清除"数字"以外的字符
  return params.replace(/[^\d]/g, "");
}
// 运单号
function caseNumber5(params) {
  // 清除非英文大小写字母和数字的字符
  params = params.replace(/[^a-zA-Z0-9]/g, "");
  // 限制长度为 50 字
  if (params.length > 30) {
    params = params.substring(0, 30);
  }
  return params;
}
// 保留两位小数，限制临界值
function caseNumber6(value) {
  if (Number(value) > Number(prop.limitData)) {
    return prop.limitData;
  } else {
    return value
      .replace(/^(?:[0][1-9]?)$/g, (val) => val[1])
      .replace(/^(\.)+/, "")
      .replace(/[^\d.]/g, "")
      .replace(/^(-)*(\d\d\d\d\d\d\d\d\d\d)*$/, "$1")
      .replace(/\.+/, ".")
      .replace(".", "$#$")
      .replace(/\./g, "")
      .replace("$#$", ".")
      .replace(/0{3,}/g, "0")
      .replace(/00\./, "0")
      .replace(/0(\d{2,})/g, "0")
      .replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
  }
}

function caseNumber7(params) {
  // 清除整数部分前面多余的0
  var numbers = params.toString().split("");
  if (numbers.length > 1) {
    if (numbers[0] === "0" && numbers[1] !== ".") {
      params = params.toString().substring(1);
    }
  }

  var a = params.toString().split(".");
  var a1 = a[0];
  var a2 = a[1];
  // 整数只能输入10位
  if (a2 == undefined) {
    if (a1.length > 10) {
      params = a1.substring(0, 10);
    }
  } else {
    if (a1.length > 10) {
      params = a1.substring(0, 10) + "." + a2;
    }
  }
  return params
    .replace(/^0[0-9]+/, (params) => params[1])
    .replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, "$1");
}

function caseNumber8(params) {
  // 清除"数字"以外的字符
  return params.replace(/[^\d]/g, "");
}
defineExpose({
  setCursorPos,
  focus,
});
</script>
<style lang="scss" scoped>
.nd-input-box {
  :deep(.el-input) {
    width: 100%;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    padding-top: 0px;
    padding-bottom: 0px;
    font-size: 14px;
    background-color: #ffffff;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
    padding-left: 0;
  }

  :deep(.el-input__inner) {
    color: #606266;
    padding-left: 12px;
    padding-right: 12px;
  }
}
</style>
