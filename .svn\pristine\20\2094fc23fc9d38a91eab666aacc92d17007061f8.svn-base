<template>
  <el-check-tag v-bind="$attrs">
    <slot></slot>
  </el-check-tag>
</template>
<script setup>
// emit
// const emit = defineEmits(['change'])

// const props = defineProps({
//   // 是否选中
//   checked: {
//     type: <PERSON>olean,
//     default: false
//   }
// })

// function changeHandler(params) {
//   emit("change", params);
// }
</script>
<style lang="scss" scoped>
.el-check-tag {
  border: 1px solid #c7c9cc;
  padding: 6px 15px;
  font-weight: normal;
  background-color: #ffffff;
  color: #909399;
}

.el-check-tag.is-checked {
  border: 1px solid #068324;
  background-color: #068324;
  color: #fff;
}

.el-check-tag + .el-check-tag {
  margin-left: 10px;
}
.el-check-tag.el-check-tag--primary.is-checked:hover {
  background-color: #068324 !important;
}
</style>
