<template>
  <nd-dialog
    ref="dialogRef"
    width="60vw"
    height="53.5vh"
    :title="title"
    align-center
    :before-close="close"
    style="padding: 16px 0 0"
    class="custom-dia"
  >
    <div class="main-cont">
      <div class="status-cont">
        <div class="status-item">
          <div class="status-text">订单状态：</div>
          <div class="status-text">
            <!-- 补充订单状态显示 -->
            <span v-if="form.orderStatus === 0">待付款</span>
            <span v-else-if="form.orderStatus === 1">待发货</span>
            <span v-else-if="form.orderStatus === 2">待收货</span>
            <span v-else-if="form.orderStatus === 3">已完成</span>
            <span v-else-if="form.orderStatus === 50">订单取消中</span>
            <span v-else-if="form.orderStatus === 51">订单已取消</span>
            <span v-else-if="form.orderStatus === 60">已关闭</span>
            <span v-else-if="form.orderStatus === 70">已申请售后</span>
          </div>
        </div>
        <span>；&nbsp;</span>
        <div class="status-item">
          <div class="status-text">售后状态：</div>
          <div class="status-text">
            <!-- 补充售后状态显示 -->
            <span v-if="form.shStatus === 1">处理中</span>
            <span v-else-if="form.shStatus === 2">售后完成</span>
            <span v-else-if="form.shStatus === 3">售后关闭</span>
          </div>
        </div>
      </div>
      <div class="top-cont">
        <div class="card-title">
          <div class="rect"></div>
          <div class="text">开票信息</div>
        </div>
        <div class="info-cont" style="margin-bottom: 16px;">
          <div class="info-item">
            <div class="info-left">发票抬头</div>
            <div class="info-text">
              {{ form?.ttmc || "--"
              }}<span class="copy-text" @click="copyInfo">复制</span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-left">发票类型</div>
            <div class="info-text">
              {{ form?.fplx === 1 ? "电子普通发票" : "专用发票" }}
            </div>
          </div>
        </div>
        <div class="info-cont">
          <div class="info-item">
            <div class="info-left">订单金额</div>
            <div class="info-text">{{ form.amount }}</div>
          </div>
          <div class="info-item">
            <div class="info-left">申请开票金额</div>
            <div class="info-text">{{ form.kpje }}</div>
          </div>
        </div>
      </div>
      <div class="bottom-cont">
        <div class="card-title">
          <div class="rect"></div>
          <div class="text">发票信息</div>
        </div>
        <div class="form-cont">
          <el-form
            ref="addFormRef"
            :model="form"
            :rules="rules"
            class="add-box"
            :validate-on-rule-change="false"
          >
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="电子发票"
                  label-width="100px"
                  prop="sources"
                >
                  <nd-upload
                    :files="form.sources"
                    fzgs="INVOICE"
                    :limit="1"
                    tip1="建议尺寸：800*800像素，最多上传1张；"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="票据编号" label-width="100px" prop="fphm">
                  <nd-input
                    v-model="form.fphm"
                    width="100%"
                    placeholder="请输入"
                    maxlength="20"
                    type2="number8"
                    clearable
                  ></nd-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开票金额" label-width="100px" prop="kpje">
                  <nd-input
                    v-model="form.kpje"
                    width="100%"
                    placeholder="请输入"
                    clearable
                    type2="number7"
                    @change="kpjeChange"
                  ></nd-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="开票日期"
                  label-width="100px"
                  prop="kpTime"
                  style="margin-bottom: 0px"
                >
                  <nd-date-picker
                    v-model="form.kpTime"
                    type="datetime"
                    format="YYYY-MM-DD HH:mm:ss"
                    clearable
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择时间"
                    width="100%"
                  ></nd-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <nd-button type="primary" icon="DocumentChecked" @click="submit" :disabled="isSubmitting"
        >保&nbsp;存</nd-button
      >
      <nd-button type="" icon="Close" @click="close">取&nbsp;消</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";

// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义方法
let myEmit = defineEmits(["refreshDetail"]);
// 定义属性

// 定义对话框组件的ref
const dialogRef = ref(null);

onMounted(() => {});
// 定义对话框的标题
let title = ref("开票");
// 打开弹窗
const open = (query) => {
  console.log(query, "接收的参数");
  // 开票信息详情
  form.fpId = query.fpId;
  getMakeInvoiceInfo();
  form.kpTime = getCurrentDate();
  dialogRef.value.open();
};

// 定义表单数据
const addFormRef = ref(null);

let form = reactive({
  orderId: "",
  memberId: "",
  fpId: "",
  ttmc: "", //发票抬头
  fplx: "", //发票类型
  amount: "", //订单金额,申请开票金额
  fphm: "", //票据编号
  kpje: "", //开票金额
  kpTime: "", //开票日期
  sources: [], //电子发票,
  orderStatus: "", //订单状态,
  shStatus: "", //售后状态,
});
// 自定义校验附件
const validateSources = (rule, value, callback) => {
  if (form.sources.length < 1) {
    return callback(new Error("电子发票不能为空"));
  } else {
    return callback();
  }
};

// 表单校验规则
const rules = reactive({
  fphm: [{ required: true, message: "票据编号不能为空", trigger: "blur" }],
  kpje: [{ required: true, message: "开票金额不能为空", trigger: "blur" }],
  kpTime: [{ required: true, message: "开票日期不能为空", trigger: "blur" }],
  sources: [{ required: true, validator: validateSources, trigger: "blur" }],
});

// 清空表单
const clear = () => {
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 清空表单
  form.fphm = "";
  form.kpje = "";
  form.kpTime = "";
  form.sources = [];
};

// 关闭弹窗 ==============================================================
const close = () => {
  dialogRef.value.close();
  clear();
};

const isSubmitting = ref(false);

// 保存提交 ==============================================================
const submit = async () => {
  if (isSubmitting.value) return; 

  isSubmitting.value = true; 

  await addFormRef.value.validate((valid, fields) => {
    if (valid) {
      // 新增
      let params = {
        orderId: form.orderId,
        memberId: form.memberId,
        fpdz: form.sources[0].sourcePath,
        fpId: form.fpId,
        fphm: form.fphm,
        kpje: form.kpje,
        kpTime: form.kpTime,
      };
      console.log(params, "保存参数");
      $axios({
        url: "/invoice/invoiceSave",
        method: "post",
        serverName: "nd-base2",
        data: params,
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage({
            message: res.data.message,
            type: "success",
          });
          // 关闭弹框
          myEmit("refreshDetail");
          close();
        } else {
          ElMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      }).finally(() => {
        isSubmitting.value = false; 
      });
    } else {
      console.log("校验失败!", fields);
      isSubmitting.value = false; 
    }
  });
};

const getMakeInvoiceInfo = () => {
  let params = { fpId: form.fpId };
  $axios({
    url: "/invoice/invoiceDetail",
    method: "get",
    serverName: "nd-base2",
    params,
  }).then((res) => {
    console.log(res, "开票信息");
    if (res.data.code === 2000) {
      form.orderId = res.data.data.orderId;
      form.memberId = res.data.data.memberId;
      form.ttmc = res.data.data.ttmc;
      form.fplx = res.data.data.fplx;
      form.amount = res.data.data.amount;
      form.kpje = res.data.data.kpje;
      form.orderStatus = res.data.data.orderStatus;
      form.shStatus = res.data.data.shStatus;
    }
  });
};

// 获取当前时间
function getCurrentDate() {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，所以要+1
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  // return `${year}-${month}-${day}`;
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 复制
const copyInfo = () => {
  // \r\n 是换行符号
  let copyText = `${form.ttmc}`;
  var textarea = document.createElement("textarea"); // 创建textarea对象
  textarea.value = copyText; // 设置复制内容
  document.body.appendChild(textarea); // 添加临时实例
  textarea.select(); // 选择实例内容
  document.execCommand("Copy"); // 执行复制
  document.body.removeChild(textarea); // 删除临时实例
  ElMessage.success("复制成功");
};

const kpjeChange = (val) => {
  if (val !== null && val !== undefined && val !== "") {
    form.kpje = parseFloat(form.kpje).toFixed(2);
  }
};

// 暴露方法给父组件
defineExpose({
  open,
  clear,
});
</script>

<style lang="scss" scoped>
.add-box {
  padding: 0 60px;
}

.main-cont {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .card-title {
    display: flex;
    align-items: center;
    margin-bottom: 17px;

    .rect {
      width: 2px;
      height: 16px;
      background: #068324;
      margin-right: 8px;
    }

    .text {
      font-family: Microsoft YaHei;
      font-size: 16px;
      font-weight: bold;
      line-height: 24px;
      letter-spacing: 0px;
      font-variation-settings: "opsz" auto;
      color: #444444;
    }
  }

  .status-cont {
    padding: 6px 12px;
    background: #fff;
    margin-bottom: 12px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    font-family: Microsoft YaHei;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    text-align: right;
    letter-spacing: 0px;
    color: #ff0001;

    .status-item {
      display: flex;
      align-items: center;
    }

    .status-text {
    }
  }

  .top-cont {
    padding: 12px;
    background: #fff;
    margin-bottom: 12px;
    border-radius: 5px;
    border: 1px solid #EAEAEA;

    .info-cont {
      display: flex;
      width: 100%;
      .info-item {
        width: 50%;
        display: flex;
        gap: 10px;
        .info-left {
          width: 100px;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          text-align: right;
          color: #888888;
        }
        .info-text {
          flex: 1;
          font-size: 14px;
          font-weight: normal;
          line-height: 24px;
          color: #000000;
          .copy-text {
            margin-left: 6px;
            color: #0098ff;
            border-bottom: 1px solid #0098ff;
          }
        }
      }
    }
  }

  .bottom-cont {
    flex: 1;
    padding: 12px;
    background: #fff;
    width: 100%;
    border-radius: 5px;
    border: 1px solid #EAEAEA;

    .form-cont {
      width: 100%;

      :deep(.el-form-item) {
        align-items: center;
      }
    }
  }
}
</style>

<style lang="scss">
.custom-dia {
  .dialog-content-box .dialog-content {
    padding: 16px;
    background: #f7f7f7 !important;
    border-top: 1px solid #eaeaea;
  }

  .el-scrollbar__view {
    height: 100%;
  }
}
</style>
