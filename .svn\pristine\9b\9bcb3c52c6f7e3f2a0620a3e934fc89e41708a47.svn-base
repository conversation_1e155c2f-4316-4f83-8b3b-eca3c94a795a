{"name": "ndsc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "^1.2.0", "@vueuse/integrations": "^10.2.1", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.4.2", "element-plus": "^2.9.6", "file-saver": "^2.0.5", "lodash": "^4.17.21", "qrcode.vue": "^3.4.0", "quill": "^1.3.6", "vue": "^3.2.45", "vue-router": "^4.1.3"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "axios": "^1.8.2", "sass": "^1.89.0", "sass-loader": "^13.3.3", "vite": "^4.5.13"}}