<template>
  <!-- // 订单处理 -->
  <nd-dialog
    ref="shipmentDialogRef"
    width="60vw"
    height="48vh"
    :title="title"
    align-center
  >
    <div class="mainBox">
      <el-form
        ref="addFormRef"
        :model="form"
        :rules="rules"
        class="add-box"
        label-position="left"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="处理方案" label-width="80px" prop="shWay">
              <nd-select
                filterable
                v-model="form.shWay"
                placeholder=" "
                width="90%"
              >
                <el-option label="同意退款" value="1">同意退款</el-option>
                <!-- <el-option label="不予处理" value="1">不予处理</el-option> -->
                <el-option label="拒绝退款" value="3">拒绝退款</el-option>
              </nd-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.shWay == 3">
            <el-form-item label="拒绝原因" label-width="80px" prop="shReject">
              <!-- <nd-input v-model="form.shReject" placeholder=" " width="95.5%" /> -->
              <nd-select
                filterable
                v-model="form.shReject"
                placeholder=" "
                width="90%"
              >
                <el-option label="商品已发货" value="商品已发货"
                  >商品已发货</el-option
                >
                <el-option label="待上传发货信息" value="待上传发货信息"
                  >待上传发货信息</el-option
                >
                <el-option
                  label="定制商品不支持无理由退换"
                  value="定制商品不支持无理由退换"
                  >定制商品不支持无理由退换</el-option
                >
              </nd-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="说明" label-width="80px" prop="remark">
              <nd-input
                type="textarea"
                maxlength="500"
                v-model="form.remark"
                placeholder=" "
                width="95.5%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <nd-button type="primary" icon="Pointer" @click="submit()"
        >确&nbsp;定</nd-button
      >
      <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义方法
let emit = defineEmits(["before-close"]);
// 定义属性
const props = defineProps({});

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const shipmentDialogRef = ref(null);
// 定义对话框的标题
let title = ref("");
// 定义表单数据
const addFormRef = ref(null);
let tableContent = ref(null);
let tableData = ref([]);
let orderId = ref("");
let form = ref({
  orderId: "",
  shWay: "",
  shReject: "",
  remark: "",
});

// 定义校验规则 ==========================================================

// 自定义校验规则 --- 正则 --- 功能菜单名称最长不得超过10个汉字
const validateFunctionName = (rule, value, callback) => {
  const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
  let chineseCharacters = form.value.menuName.match(chineseReg); // 匹配输入字符串中的汉字
  if (chineseCharacters && chineseCharacters.length > 10) {
    return callback(new Error("error"));
  } else {
    return callback();
  }
};

// 表单校验规则
const rules = reactive({
  shWay: [{ required: true, message: "请选择处理方案" }],
  shReject: [{ required: true, message: "请输入拒绝原因" }],
  functionName: [
    { required: true, message: "请输入功能点名称", trigger: "blur" },
    // { min: 1, max: 50, message: "最长不得超过50个汉字", trigger: "blur" },
    {
      validator: validateFunctionName,
      trigger: "blur",
      message: "最长不得超过50个汉字",
    },
  ],
});

// 打开弹窗 ==============================================================
// 打开弹窗
function open(params, status) {
  console.log("🚀 ~ open ~ params:", params);
  if (status === "shipment") {
    title.value = "订单处理";
    orderId.value = params.orderId;
    form.value.orderId = params.orderId;
    getDetail();
    clear();
    shipmentDialogRef.value.open();
  }
}
// 获得详情
function getDetail() {
  $axios({
    url: "/order/manage/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: orderId.value,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      tableContent.value = res.data.data;
      tableData.value = tableContent.value.detailVos;
    }
  });
}

// 清空表单
const clear = () => {
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  form.value = {
    orderId: "",
    shWay: "",
    shReject: "",
    remark: "",
  };
};

// 关闭弹窗 ==============================================================
const close = () => {
  emit("before-close");
  shipmentDialogRef.value.close();
  clear();
};

// 保存提交 ==============================================================
const submit = async () => {
  await addFormRef.value.validate((valid, fields) => {
    if (valid) {
      let params = {
        orderId: orderId.value,
        shWay: form.value.shWay,
        shReject: form.value.shReject,
        remark: form.value.remark,
      };
      $axios({
        url: "/order/manage/deal/cancel",
        method: "post",
        serverName: "nd-base2",
        data: params,
      }).then((res) => {
        if (res.data.code === 2000) {
          // 轻提示
          ElMessage({
            message: "处理成功",
            type: "success",
          });
          // 关闭弹框
          emit("before-close");
          close();
        } else {
          ElMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    } else {
      console.log("校验失败!", fields);
    }
  });
};

// 暴露方法给父组件 =======================================================
defineExpose({
  open,
  clear,
});
</script>

<style lang="scss" scoped>
.goodstotal {
  margin-bottom: 20px;
}
.mainBox {
  width: 100%;
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
}
.add-box {
  height: 100%;
  padding: 12px;
  border-radius: 5px;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #ebeef5;
  :deep(.el-textarea__inner) {
    height: 80px;
  }
}
</style>
