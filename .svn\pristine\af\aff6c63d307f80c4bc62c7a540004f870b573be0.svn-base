import { createRouter, createWebHashHistory } from "vue-router";

// 子菜单
const routesZjd1 = [
  {
    // 欢迎页
    path: "/welcomeView",
    name: "welcomeView",
    component: () => import("@/views2/welcomeView/index.vue"),
  },
  {
    // 用于刷新
    path: "/reloadView",
    name: "reloadView",
    component: () => import("@/views2/reloadView/index.vue"),
  },
  {
    // 订单管理
    path: "/orderView",
    name: "orderView",
    component: () => import("@/views2/orderView/index.vue"),
  },
  {
    // 采购订单
    path: "/purchaseOrderView",
    name: "purchaseOrderView",
    component: () => import("@/views2/purchaseOrderView/index.vue"),
  },
  {
    // 售后管理
    path: "/afterSalesView",
    name: "afterSalesView",
    component: () => import("@/views2/afterSalesView/index.vue"),
  },
  {
    // 发票管理
    path: "/invoicesView",
    name: "invoicesView",
    component: () => import("@/views2/invoicesView/index.vue"),
  },
  {
    // 商品管理
    path: "/goodsView",
    name: "goodsView",
    component: () => import("@/views2/goodsView/index.vue"),
  },
  {
    // 养殖户管理
    path: "/farmersView",
    name: "farmersView",
    component: () => import("@/views2/farmersView/index.vue"),
  },
  {
    // 客服中心
    path: "/customerServiceCenterView",
    name: "customerServiceCenterView",
    component: () => import("@/views2/customerServiceCenterView/index.vue"),
  },
  {
    // 供应商管理
    path: "/supplierView",
    name: "supplierView",
    component: () => import("@/views2/supplierView/index.vue"),
  },
  {
    // 运营机构管理
    path: "/orgView",
    name: "orgView",
    component: () => import("@/views2/orgView/index.vue"),
  },
  {
    // 供应商用户管理
    path: "/supplierUserManagementView",
    name: "supplierUserManagementView",
    component: () => import("@/views2/supplierUserManagementView/index.vue"),
  },
  {
    // 运营用户管理
    path: "/operationsUserManagementView",
    name: "operationsUserManagementView",
    component: () => import("@/views2/operationsUserManagementView/index.vue"),
  },
  {
    // 数据字典管理
    path: "/dataAictionaryView",
    name: "dataAictionaryView",
    component: () => import("@/views2/dataAictionaryView/index.vue"),
  },
  {
    // 角色管理
    path: "/roleManagementView",
    name: "roleManagementView",
    component: () => import("@/views2/roleManagementView/index.vue"),
  },
  {
    // 菜单管理
    path: "/menuManagementView",
    name: "menuManagementView",
    component: () => import("@/views2/menuManagementView/index.vue"),
  },
  {
    // 知识管理
    path: "/knowledgeManagementView",
    name: "knowledgeManagementView",
    component: () => import("@/views2/knowledgeManagementView/index.vue"),
  },
  {
    // 采购需求单
    path: "/purchaseRequestFormView",
    name: "purchaseRequestFormView",
    component: () => import("@/views2/purchaseRequestFormView/index.vue"),
  },
  {
    //商户管理
    path: "/merchantView",
    name: "merchantView",
    component: () => import("@/views2/merchantView/index.vue"),
  },
  {
    // 采购订单结算
    path: "/settlementOrderView",
    name: "settlementOrderView",
    component: () => import("@/views2/settlementOrderView/index.vue"),
  },
];

// 主菜单
const routesZjd = [
  {
    // 根
    path: "/",
    component: () => import("@/views/loginView/index.vue"),
  },
  {
    // 登录
    path: "/loginView",
    component: () => import("@/views/loginView/index.vue"),
  },
  {
    // 首页
    path: "/mainView",
    component: () => import("@/views/mainView/index.vue"),
    // meta: {
    //   keepAlive: true, // 组件需要缓存
    // },
    children: [...routesZjd1],
  },
];

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [...routesZjd],
});

export default router;
