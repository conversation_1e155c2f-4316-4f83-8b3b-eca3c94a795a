<template>
    <ndDialog
        ref="dialogRef"
        :title="dialogData.title"
        :before-close="close"
        width="1200px"
        height="600px"
        align-center
    >
        <div class="view" v-loading="dialogData.loading">
            <!-- 待分配、待接单、已取消 -->
            <template v-if="[0, 1, 3].includes(dialogData.status)">
                <!-- 基础信息 -->
                <BasicInformation class="card" />
                <!-- 商品信息 -->
                <CommodityInformation class="card" />
            </template>

            <!-- 已接单 -->
            <template v-if="dialogData.status === 2">
                <!-- 基础信息 -->
                <BasicInformation class="card" />
                <!-- 商品信息 -->
                <CommodityInformation class="card" />
                <!-- 接单信息 -->
                <OrderAcceptanceInformation class="card" />
                <!-- 议价信息 -->
                <BargainingInformation
                    class="card"
                    v-if="dialogData.detailData?.sfyj === 1"
                />
            </template>
        </div>
        <template #footer>
            <!-- 待分配 -->
            <template v-if="dialogData.status === 0">
                <ndButton type="primary" @click="dispatch(dialogData.demandId)">
                    <img
                        class="button-icon"
                        src="@/assets/images/purchaseRequestFormView/dispatch.png"
                        alt=""
                    />
                    派 单
                </ndButton>
            </template>

            <ndButton @click="close">
                <el-icon style="margin-right: 8px"><Back /></el-icon>
                返 回
            </ndButton>
        </template>
    </ndDialog>

    <Dispatch
        ref="dispatchRef"
        @refresh="
            emit('refresh');
            close();
        "
    />
</template>

<script setup>
// 公共组件
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";

// 自定义组件
import BasicInformation from "./BasicInformation.vue";
import CommodityInformation from "./CommodityInformation.vue";
import OrderAcceptanceInformation from "./OrderAcceptanceInformation.vue";
import BargainingInformation from "./BargainingInformation.vue";
import Dispatch from "../Dispatch/index.vue";

// hooks
import { useDialog } from "../../hooks/detail/useDialog";

import { provide, ref, watch } from "vue";

// emit
const emit = defineEmits(["refresh"]);

// ref
const dialogRef = ref(null);
const dispatchRef = ref(null);

// useDialog
const { dialogData, open, close, dispatch } = useDialog({
    dialogRef,
    dispatchRef,
});

provide("$purchaseRequestFormDetailData", dialogData);

defineExpose({
    open,
    close,
});
</script>

<style lang="scss" scoped>
.view {
    padding: 12px;
    // height: 100%;
    // overflow: hidden;

    .card {
        margin-bottom: 12px;

        &:nth-last-of-type(1) {
            margin-bottom: 0;
        }
    }
}

.button-icon {
    display: block;
    width: 12px;
    height: 14px;
    margin-right: 9px;
}
</style>
