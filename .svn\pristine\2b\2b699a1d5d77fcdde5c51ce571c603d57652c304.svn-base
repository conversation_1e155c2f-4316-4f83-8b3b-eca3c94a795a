<template>
  <nd-dialog ref="dialogRef" :title="page.title" width="60vw" height="60vh">
    <div class="main">
      <div class="title-item">
        <div class="line"></div>
        <div class="title">意见反馈</div>
      </div>
      <div class="box-item">
        <el-row :gutter="0">
          <el-col :span="12">
            <div class="cell">
              <div class="label">账户名称</div>
              <div class="content">{{ page.data.nickname }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="cell">
              <div class="label">账号</div>
              <div class="content">{{ page.data.account }}</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :span="24">
            <div class="cell">
              <div class="label">反馈内容</div>
              <div class="content">{{ page.data.fknr }}</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :span="24">
            <div class="cell">
              <div class="label">图片</div>
              <div class="content1">
                <img
                  v-for="(item, index) in page.data.fileList"
                  :key="index"
                  :src="imgFile + item.sourcePath"
                  class="img-box"
                />
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :span="24">
            <div class="cell">
              <div class="label">提出时间</div>
              <div class="content">{{ page.data.tcsj }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="main">
      <div class="title-item">
        <div class="line"></div>
        <div class="title">回复记录</div>
      </div>
      <nd-table :data="page.data.hfList" style="margin-top: 10px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column
          prop="hfnr"
          label="回复内容"
          max-width="220"
          align="left"
          header-align="center"
        />
        <el-table-column
          prop="hfsj"
          label="回复时间"
          width="180"
          align="center"
        />
        <el-table-column
          prop="userName"
          label="操作人"
          width="150"
          align="center"
        />
        <el-table-column fixed="right" align="center" label="操作" width="60px">
          <template #default="scoped">
            <div style="display: flex; gap: 8px; justify-content: center">
              <nd-button type="delete" @click="handleDelete(scoped.row)"
                >删除</nd-button
              >
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </div>

    <template #footer>
      <nd-button icon="Close" @click="close">关&nbsp;&nbsp;闭</nd-button>
      <nd-button type="primary" icon="ChatDotSquare" @click="openReply"
        >回&nbsp;&nbsp;复</nd-button
      >
    </template>
  </nd-dialog>
  <reply ref="replyRef" @before-close="getDetail" />
</template>

<script setup>
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndTable from "@/components/ndTable.vue";
// 导入子组件
import reply from "./reply.vue";

import { ref, reactive, inject } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");

const dialogRef = ref(null);
const emit = defineEmits(["before-close"]);

const page = reactive({
  title: "",
  opinionId: "",
  data: {},
});
const imgFile = ref("");

//打开
const open = (val) => {
  imgFile.value =
    window.ipConfig.base2 +
    "/common/download?token=" +
    localStorage.getItem("syToken") +
    "&path=";
  page.title = "详情";
  page.opinionId = val.opinionId;
  dialogRef.value.open();
  getDetail();
};

//回显
const getDetail = () => {
  $axios({
    url: "/kfzx/find",
    method: "get",
    serverName: "nd-base2",
    data: {
      opinionId: page.opinionId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.data = res.data.data;
    }
  });
};

//打开回复
const replyRef = ref(null);
const openReply = () => {
  replyRef.value.open(page);
};

//删除
function handleDelete(row) {
  let params = {
    hfId: row.hfId,
  };
  ElMessageBox.confirm("确定删除?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    confirmButtonClass: "ExitConfirmButton",
    cancelButtonClass: "ExitCancelButton",
    customClass: "ExitCustomClass",
  }).then(() => {
    $axios({
      url: "/kfzx/delHf",
      method: "post",
      serverName: "nd-base2",
      data: params,
    }).then((res) => {
      if (res.data.code === 2000) {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        getDetail();
      } else {
        ElMessage({
          message: res.data.message,
          type: "warning",
        });
      }
    });
  });
}

const close = () => {
  dialogRef.value.close();
  emit("before-close");
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.main {
  padding: 12px;
  background-color: #fff;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;
  .title-item {
    display: flex;
    gap: 6px;
    align-items: center;
    .line {
      width: 2px;
      height: 16px;
      background: #068324;
    }
    .title {
      font-family: Microsoft YaHei;
      font-size: 16px;
      font-weight: bold;
      line-height: 24px;
      color: #444;
    }
  }
  .box-item {
    padding: 12px;
    .cell {
      margin-bottom: 10px;
      display: flex;
      gap: 10px;
      .label {
        width: 100px;
        text-align: right;
        font-family: Microsoft YaHei;
        font-size: 14px;
        line-height: 24px;
        font-weight: normal;
        color: #888;
      }
      .content {
        flex: 1;
        font-size: 14px;
        font-weight: normal;
        line-height: 24px;
        color: #000;
        overflow-wrap: break-word;
        word-break: break-all;
        white-space: normal;
      }
      .content1 {
        flex: 1;
        font-size: 14px;
        font-weight: normal;
        line-height: 24px;
        color: #000;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .img-box {
          width: 80px;
          height: 80px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.ExitConfirmButton {
  background: #068324 !important;
  border-color: #068324 !important;

  &:hover {
    opacity: 0.8;
  }
}

.ExitCancelButton {
  &:hover {
    background-color: rgba(133, 224, 154, 0.2) !important;
    color: #38864a !important;
    border-color: #38864a !important;
  }
}
.ExitCustomClass {
  .el-message-box__headerbtn:hover {
    .el-message-box__close {
      color: #068324;
    }
  }
}
</style>
