import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";
import router from "./router";
import axios from "@/http/index";
// element
import elementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";

// 引入Icon图标
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

const app = createApp(App);
app.config.globalProperties.$menus = [];
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 引入富文本编辑器QuillEditor
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';
app.component('QuillEditor', QuillEditor)

app.use(router);
app.use(elementPlus, {
  locale: zhCn,
});

app.provide("$axios", axios);

// // 导航守卫
// router.beforeEach((to, from, next) => {
//   next();
// });

app.mount("#app");