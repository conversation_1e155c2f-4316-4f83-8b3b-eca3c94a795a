<template>
  <div class="nd-second-menu-box" v-if="page.show">
    <div v-for="(item, index) in data" class="item" :class="item.selected ? 'highlight' : ''" @click="secondMenuClick(item.id)">
      <span v-if="!item.child">{{ item.name }}</span>
      <template v-if="item.child">
        <el-dropdown @command="thirdMenuClick($event, item)">
          <span class="el-dropdown-link">
            {{ item.name }}
            <el-icon>
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="(subItem, subIndex) in item.child" :command="subItem.id">{{ subItem.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </div>
  </div>
</template>

<script setup>
// 导入vue
import { ref, watch, computed, reactive, onMounted, nextTick } from "vue";
// emit
const emit = defineEmits(["menu-click", "update:modelValue"]);

// 定义属性
const props = defineProps({
  // 数据源
  data: {
    type: Array,
    default: [],
  },
  // 当前选中菜单(v-model)
  modelValue: {
    type: String,
    default: "",
  },
});

// watch modelValue
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    // 高亮菜单
    props.data.map((item) => {
      item.selected = false;
    });
    var find = false;
    // 如果点击的是二级菜单，则直接高亮二级菜单
    props.data.map((item) => {
      if (item.id === newValue) {
        item.selected = true;
        find = true;
      }
    });
    // 如果点击的是三级菜单，则高亮其二级父菜单
    if (!find) {
      props.data.map((item) => {
        if (item.child) {
          item.child.map((subItem) => {
            if (subItem.id === newValue) {
              item.selected = true;
              item.name = subItem.name;
            }
          });
        }
      });
    }
  },
  {
    immediate: true,
  }
);

// mounted
onMounted(() => {
  // if (props.data.length > 0) {
  //   menu.current = props.data[0].id;
  // }
});

// 二级菜单点击
function secondMenuClick(id) {
  props.data.map((item) => {
    item.selected = false;
  });
  props.data.map((item) => {
    if (item.id === id) {
      item.selected = true;
    }
  });
  emit("update:modelValue", id);
  emit("menu-click", id);
}

// 三级菜单点击
function thirdMenuClick(command, firstMenu) {
  var menuID = command;
  props.data.map((item) => {
    item.selected = false;
  });
  firstMenu.selected = true;
  firstMenu.child.map((item) => {
    if (item.id === command) {
      firstMenu.id = item.id;
      firstMenu.name = item.name;
    }
  });
  emit("update:modelValue", menuID);
  emit("menu-click", menuID);
}

var page = reactive({
  show: true,
});
function reload() {
  page.show = false;
  nextTick(() => {
    page.show = true;
  });
}

defineExpose({
  reload,
});
</script>
<style scoped lang="scss">
.nd-second-menu-box {
  width: auto;
  height: 100%;
  padding-left: 15px;
  padding-right: 15px;
  display: flex;
  flex-direction: row;
  // justify-content: center;
  align-items: center;

  .item {
    width: auto;
    height: 100%;
    padding-left: 8px;
    padding-right: 8px;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    color: #3d3d3d;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      color: #0b8df1;
    }
  }

  .highlight {
    background-color: #f4f5f7;
    border-top: 3px solid #0b8df1;
  }
}
</style>
