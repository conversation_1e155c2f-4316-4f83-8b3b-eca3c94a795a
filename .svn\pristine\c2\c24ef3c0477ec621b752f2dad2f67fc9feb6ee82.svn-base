<template>
    <div class="viiew" :style="`justify-content: ${props.justifyContent}`">
        <div class="text">
            {{
                encryptionDecryptionData.isDecrypt
                    ? encryptionDecryptionData.plaintext
                    : encryptionDecryptionData.ciphertext
            }}
        </div>
        <div class="icon" style="margin-left: 10px;">
            <img
                v-show="encryptionDecryptionData.isDecrypt"
                src="@/assets/images/openEye.png"
                alt=""
                srcset=""
                @click="showCiphertext"
            />
            <img
                v-show="!encryptionDecryptionData.isDecrypt"
                src="@/assets/images/closeEye.png"
                alt=""
                srcset=""
                @click="showPlaintext"
            />
        </div>
    </div>
</template>

<script setup>
// hooks
import { useEncryptionDecryption } from "../../hooks/encryptionDecryption/useEncryptionDecryption";

// props
const props = defineProps({
    ciphertext: {
        type: String,
        default: "",
    },
    keyText: {
        type: String,
        default: "",
    },
    justifyContent: {
        type: String,
        default: "space-between",
    },
});

// useEncryptionDecryption
const { encryptionDecryptionData, showPlaintext, showCiphertext } =
    useEncryptionDecryption({
        props,
    });
</script>

<style lang="scss" scoped>
.viiew {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;

    .text {
        font-size: 14px;
        color: #555555;
    }

    .icon {
        display: flex;
        align-items: center;

        img {
            width: 14px;
            height: 14px;
            cursor: pointer;
            user-select: none;
        }
    }
}
</style>
