<template>
  <ndb-page-list>
    <template #button>
      <nd-button type="primary" @click="handleAdd" authKey="ORG_VIEW_ADD"
        >新增</nd-button
      >
      <nd-button @click="handleBatchDelete" authKey="ORG_VIEW_BATCH_DELETE">
        删除
      </nd-button>
    </template>

    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="运营机构名称">
          <nd-input
            v-model.trim="page.searchForm.nameLike"
            placeholder="请输入运营机构名称"
          />
        </nd-search-more-item>
        <nd-search-more-item title="运营机构编码">
          <nd-input
            v-model.trim="page.searchForm.codeLike"
            placeholder="请输入运营机构编码"
          />
        </nd-search-more-item>
        <nd-search-more-item title="联系人">
          <nd-input
            v-model.trim="page.searchForm.contactNameLike"
            placeholder="请输入联系人"
          />
        </nd-search-more-item>
        <nd-search-more-item title="手机号码">
          <nd-input
            v-model.trim="page.searchForm.contactPhoneLike"
            placeholder="请输入手机号码"
          />
        </nd-search-more-item>
        <nd-search-more-item title="身份证号">
          <nd-input
            v-model.trim="page.searchForm.idNumberLike"
            placeholder="请输入身份证号"
          />
        </nd-search-more-item>
        <nd-search-more-item title="状态">
          <nd-select v-model.trim="page.searchForm.status" clearable>
            <el-option label="全部" :value="2" />
            <el-option label="禁用" :value="0" />
            <el-option label="正常" :value="1" />
          </nd-select>
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="getTableData">查询</nd-button>
          <nd-button @click="resetSearch">重置</nd-button>
        </template>
      </nd-search-more>
    </template>

    <template #table>
      <nd-table
        style="height: 100%"
        :data="page.tableData"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column
          prop="name"
          label="运营机构名称"
          align="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span class="goto-detail" @click="openDetailDialog(row)">{{
              row.name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="运营机构编码" align="center" />
        <el-table-column
          prop="contactName"
          label="联系人"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column prop="mobile" label="手机号码" align="center" />
        <el-table-column prop="idNumber" label="身份证号" align="center" />
        <el-table-column prop="status" label="状态" align="center">
          <template #default="{ row }">
            <span>
              {{ row.status == 1 ? "正常" : "禁用" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="userNum" label="机构用户" align="center">
          <template #default="{ row }">
            <span class="goto-detail" @click="openOtherPage(row)">{{
              row.userNum
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <div
              class="action-buttons"
              style="display: flex; gap: 8px; justify-content: center"
            >
              <nd-button type="edit" @click="handleEdit(row)">编辑</nd-button>
              <nd-button type="delete" @click="handleDelete(row.supplierId)"
                >删除</nd-button
              >
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>

    <template #page>
      <nd-pagination
        v-model:current-page="pager.page"
        v-model:page-size="pager.size"
        :total="pager.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </ndb-page-list>

  <add-dialog ref="dialogRef" @before-close="getTableData"></add-dialog>
</template>

<script setup>
// 公共组件导入
import ndButton from "@/components/ndButton.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";

// 导入自定义组件
import addDialog from "./components/addDialog.vue";
// 导入vue
import { reactive, ref, onMounted, inject, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
// 定义axios
const $axios = inject("$axios");

// 导入element-plus
import { ElMessageBox, ElMessage } from "element-plus";

// 原有逻辑保持，主要调整数据获取方式
const pager = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 搜索表单
const page = reactive({
  tableData: [],
  searchForm: {
    nameLike: "",
    codeLike: "",
    contactNameLike: "",
    contactPhoneLike: "",
    idNumberLike: "",
    status: 2,
  },
  table: {
    ids: [],
    selectedRows: [],
    data: [],
  },
  dict: {
    num: "",
    userTotal: "",
    count: "",
  },
});

// 添加初始化加载逻辑
onMounted(() => {
  getTableData();
});

// 查询
const getTableData = () => {
  let params = {
    page: pager.page,
    size: pager.size,
    nameLike: page.searchForm.nameLike,
    codeLike: page.searchForm.codeLike,
    contactNameLike: page.searchForm.contactNameLike,
    contactPhoneLike: page.searchForm.contactPhoneLike,
    idNumberLike: page.searchForm.idNumberLike,
    status: page.searchForm.status === 2 ? "" : page.searchForm.status,
  };
  $axios({
    url: "/operate/page",
    method: "get",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.tableData = res.data.data.records;
      pager.total = res.data.data.total;
      pager.page = res.data.data.current;
      pager.size = res.data.data.size;
    } else {
      ElMessage.error(res.data.message);
    }
  });
};

// 修改选中事件处理
const handleSelectionChange = (rows) => {
  page.table.selectedRows = rows;
  page.table.ids = rows.map((item) => item.supplierId);
  console.log("选中的行：", page.table.ids);
};
// 检验关联用户批量删除
function getBatchUser() {
  let params = Object.values(page.table.ids);
  $axios({
    url: "/operate/checkDeleteBatch",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.dict.num = res.data.data;
      page.dict.userTotal = page.table.ids.length;
      if (page.dict.num > 0 && page.dict.num != page.dict.userTotal) {
        ElMessageBox.confirm(
          `勾选的${page.dict.userTotal}条数据中有${
            page.dict.num
          }条数据已创建用户，确定将剩下的${
            page.dict.userTotal - page.dict.num
          }条删除？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            batchDelete();
          })
          .catch(() => {});
      } else if (page.dict.num === page.dict.userTotal) {
        ElMessage.error(
          `勾选的${page.dict.num}条数据均已创建用户，无法直接删除！`
        );
      } else if (page.dict.num === 0) {
        ElMessageBox.confirm(
          `确定将勾选的${page.dict.userTotal}条数据全部删除？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            batchDelete();
          })
          .catch(() => {});
      }
    } else {
      console.log(res.data.message);
    }
  });
}

// 批量删除方法判断条件
const handleBatchDelete = async () => {
  if (page.table.ids.length === 0) {
    ElMessage.error("请先勾选记录");
    return;
  }
  getBatchUser();
};

// 批量删除方法
const batchDelete = () => {
  let params = Object.values(page.table.ids);
  $axios({
    url: "/operate/deleteBatch",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("删除成功");
      getTableData();
    }
  });
};

//删除
const handleDelete = (val) => {
  ElMessageBox.confirm("确定删除？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      $axios({
        url: "/operate/delete?operateId=" + val,
        method: "post",
        serverName: "nd-base2",
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success("删除成功");
          getTableData();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
    .catch(() => {
      console.log("取消删除");
    });
};

//检查菜单
function findMenuInTree(tree, target) {
  if (tree.name === target) {
    return true;
  }
  if (tree.child && tree.child.length > 0) {
    return tree.child.some((child) => findMenuInTree(child, target));
  }
  return false;
}

// 跳转其他页面
const openOtherPage = (row) => {
  let authArr = JSON.parse(localStorage.getItem("syMenus"));
  const menu = "运营用户管理";
  const hasMenu = authArr.some((root) => findMenuInTree(root, menu));
  if (hasMenu) {
    router.push({
      path: "/operationsUserManagementView",
      query: { supplierId: row.supplierId },
    });
  } else {
    ElMessage.info("暂无菜单权限");
  }
};
// 新增
const handleAdd = () => {
  dialogRef.value.open("add");
};

// 编辑
const handleEdit = (row) => {
  dialogRef.value.open("edit", row);
};

const openDetailDialog = (row) => {
  dialogRef.value?.open("detail", row);
};

// 对话框引用
const dialogRef = ref(null);

// 分页每页数量改变
function handleSizeChange(params) {
  pager.pageSize = params;
  pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  pager.pageIndex = params;
  getTableData();
}

const resetSearch = () => {
  page.searchForm.nameLike = "";
  page.searchForm.codeLike = "";
  page.searchForm.contactNameLike = "";
  page.searchForm.contactPhoneLike = "";
  page.searchForm.idNumberLike = "";
  page.searchForm.status = 2;
  getTableData();
};
</script>
<style lang="scss" scoped>
.goto-detail {
  cursor: pointer;
  color: #0b8df1;
}
:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
