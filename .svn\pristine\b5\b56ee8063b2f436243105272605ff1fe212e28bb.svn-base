<template>
  <ndb-page-list>
    <template #tag>
      <nd-tag @click="tagClick('')" :checked="page.search.data.label === 'pending'">全部</nd-tag>
      <nd-tag @click="tagClick(1)" :checked="page.search.data.label === 'approved'">处理中</nd-tag>
      <nd-tag @click="tagClick(2)" :checked="page.search.data.label === 'initiated'">售后完成</nd-tag>
      <nd-tag @click="tagClick(3)" :checked="page.search.data.label === 'received'">售后关闭</nd-tag>
    </template>
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="日期搜索" width="120px">
          <nd-select v-model="page.search.data.rqlx" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.datetypeList" :label="item.name" :key="item.modelId" :value="item.modelId" />
          </nd-select>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-date-picker v-model="page.search.data.cycleArr" range-separator="至" type="daterange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择" width="100%"></nd-date-picker>
        </nd-search-more-item>
        <nd-search-more-item title="售后类型">
          <nd-select v-model="page.search.data.afterSalesType" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.afterSalesList" :label="item.label" :key="item.value" :value="item.value" />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="关键字搜索" width="120px">
          <nd-input type="text" v-model.trim="page.search.data.gjzValue" width="100%" placeholder="请输入" resize="none" />
        </nd-search-more-item>
        <template #footer>
          <nd-button type="primary" @click="getTableData" authKey=""> 查询 </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <nd-table :data="page.list.data" style="width: 100%;height: 100%;" row-class-name="home-service-work-no-row" cell-class-name="home-service-work-no-cell" :header-cell-style="{ 'text-align': 'center' }" @selection-change="handleSelectionChange" v-loading="page.loading">
        <el-table-column align="center" label="#" type="selection" width="52px"></el-table-column>
        <el-table-column label="订单编号" width="200" prop="ddbh" align="center">
        </el-table-column>
        <el-table-column label="订单信息" min-width="616">
          <template #default="scope">
            <template v-for="(item, index) in scope.row.skuList" :key="index">
              <div v-if="index < scope.row.skuList.length - 1" class="customTable custom-box">
                <div class="good-img"><img :src="imgFile + item.pic" alt="" /></div>
                <div class="good-info">
                  <div class="info-name">
                    <div>{{ item.name }}</div>
                    <div>{{ item.spData }}</div>
                  </div>
                  <!-- <div class="info-num">
                    <div>￥{{ item.price }}</div>
                    <div>x{{ item.count }}</div>
                  </div> -->
                </div>
              </div>
              <div v-if="index === scope.row.skuList.length - 1" class="customTableEnd custom-box">
                <div class="good-img"><img :src="imgFile + item.pic" alt="" /></div>
                <div class="good-info">
                  <div class="info-name">
                    <div>{{ item.name }}</div>
                    <div>{{ item.spData }}</div>
                  </div>
                  <!-- <div class="info-num">
                    <div>￥{{ item.price }}</div>
                    <div>x{{ item.count }}</div>
                  </div> -->
                </div>
              </div>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="售后编号" width="200" prop="shbh" align="center">
        </el-table-column>
        <el-table-column label="订单金额" width="200" prop="ddje" align="center">
          <template #default="scope">
            ￥{{ scope.row.ddje }}
          </template>
        </el-table-column>
        <el-table-column label="退款金额" width="200" prop="tkje" align="center">
          <template #default="scope">
            <template v-if="scope.row.tkje==='-'">{{ scope.row.tkje }}</template>
            <template v-else>￥{{ scope.row.tkje }}</template>
          </template>
        </el-table-column>
        <el-table-column label="售后类型" width="150" prop="type" align="center">
          <template #default="scope">
            {{ scope.row.type === 1 ? '仅退款' : scope.row.type === 2 ? '退货退款' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="售后状态" width="150" prop="status" align="center">
          <template #default="scope">
            <div v-if="scope.row.status === 1" class="dataStatus">
              <el-badge :isDot="true" type="warning"></el-badge><span>处理中</span>
            </div>
            <div v-if="scope.row.status === 2" class="dataStatus">
              <el-badge :isDot="true" type="success"></el-badge><span>售后完成</span>
            </div>
            <div v-if="scope.row.status === 3" class="dataStatus">
              <el-badge :isDot="true" type="info" /><span>售后关闭</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请时间" width="200" prop="sqsj" align="center">
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <div>
              <div class="operation">
                <div @click="openDetailDialog(scope.row,1)" v-if="scope.row.status === 1 && !scope.row.justLook">去处理</div>
                <div @click="openDetailDialog(scope.row,2)">查看详情</div>
                <!-- <div @click="openRemarkDialog()">备注</div> -->
              </div>
            </div>
          </template>
        </el-table-column>
      </nd-table>
    </template>
    <template #page>
      <nd-pagination :current-page="page.pager.pageIndex" :page-size="page.pager.pageSize" :total="page.pager.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </template>
  </ndb-page-list>
  <detail-dialog ref="detailDialogRef" @refresh="getTableData"></detail-dialog>
  <remark-dialog ref="remarkDialogRef"></remark-dialog>
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import ndbExport from "@/components/business/ndbExport/index.vue";

// 导入子组件
import detailDialog from "./components/detailDialog.vue";
import remarkDialog from "./components/remarkDialog.vue";

// 导入vue
import { onMounted, reactive, ref, inject, watch, computed, nextTick } from "vue";
import { ElMessage as elMessage, ElMessageBox as elMessageBox } from "element-plus";

// 定义axios
const $axios = inject("$axios");

// 定义ref
const detailDialogRef = ref(null);
const remarkDialogRef = ref(null);

// 定义page
const page = reactive({
  loading:false,
  search: {
    data: {
      status: "",  //状态
      label: "pending",
      rqlx: "",//日期类型 XD:下单时间 FK:付款时间 FH:发货时间 SH:收货时间
      cycleArr: ["", ""],
      afterSalesType: 0,  //售后类型
      gjzValue: '',//关键字-过滤值
    },
    dict: {
      datetypeList: [
        { modelId: "1", name: "下单时间" },
        { modelId: "2", name: "付款时间" },
        { modelId: "3", name: "发货时间" },
        { modelId: "4", name: "确认收货时间" },
        { modelId: "5", name: "售后申请时间" },
      ],//日期类型
      afterSalesList: [
        { label: "全部", value: 0 },
        { label: "仅退款", value: 1 },
        { label: "退货退款", value: 2 },
      ], //售后类型
    },
  },
  list: {
    data: [
      {
        quantity: 111,
        price: 222,
        examineNo: 333,
      },
    ],
  },
  pager: {
    pageIndex: 1,
    pageSize: 30,
    total: 0,
  },
});
const cartItems = ref([
  {
    quantity: 111,
    price: 222,
    examineNo: 333,
    goodsList: [
      {
        name: '商品1',
        count: 1,
        spic: '10kg',
        price: 20,

      },
    ],
    state: 2,
  },
  {
    quantity: 111,
    price: 222,
    examineNo: 333,
    goodsList: [
      {
        name: '商品2',
        count: 2,
        spic: '10kg',
        price: 20,
      },
    ],
    state: 1,
  },
  {
    quantity: 111,
    price: 88222425,
    examineNo: 333,
    goodsList: [
      {
        name: '商品3',
        count: 3,
        spic: '10kg',
        price: 20,
      },
    ],
    state: 0,
  },
])
// 分类改变
function tagClick(index) {
  if (index === '') {
    page.search.data.label = "pending";
  }
  if (index === 1) {
    page.search.data.label = "approved";
  }
  if (index === 2) {
    page.search.data.label = "initiated";
  }
  if (index === 3) {
    page.search.data.label = "received";
  }
  page.search.data.status = index
  // 获得表格数据
  getTableData();
}


// 获得表格数据
function getTableData() {
  if(page.search.data.rqlx && ( !page.search.data.cycleArr || !page.search.data.cycleArr[0])){
    elMessage({
        message: '请选择日期范围',
        type: "warning",
      });
    return
  }else if(!page.search.data.rqlx && (page.search.data.cycleArr && page.search.data.cycleArr[0])){
    elMessage({
        message: '请选择日期类型',
        type: "warning",
      });
    return
  }
  page.loading=true
  page.list.data=[]
  $axios({
    url: "/sh/shList",
    method: "get",
    serverName: 'nd-base2',
    data: {
      page: page.pager.pageIndex,
      size: page.pager.pageSize,
      status: page.search.data.status,
      type: page.search.data.afterSalesType ? page.search.data.afterSalesType : '',
      rqlx: page.search.data.rqlx?page.search.data.rqlx:'',
      kssj: (page.search.data.cycleArr && page.search.data.cycleArr[0]) ? page.search.data.cycleArr[0] : "", // 开始时间
      jssj: (page.search.data.cycleArr && page.search.data.cycleArr[1]) ? page.search.data.cycleArr[1] : "", // 结束时间
      gjz: page.search.data.gjzValue,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.records;
      page.pager.total = res.data.data.total; //总页数
      page.pager.pageIndex = res.data.data.current; //当前页
      page.pager.pageSize = res.data.data.size; //每页记录数
      page.loading=false
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
      page.loading=false
    }
  }).catch(()=>{
    page.loading=false
  });
}

// 重置
function reset() {
  page.search.data.rqlx = "";
  page.search.data.afterSalesType = "";
  page.search.data.gjzValue='';
  page.search.data.cycleArr = ["", ""];
  getTableData();
}

// 查看详情
function openDetailDialog(row,type) {
  detailDialogRef.value.open(row,type);
}

// 备注
function openRemarkDialog() {
  remarkDialogRef.value.open();
}

// 选中框
const selectedPoints = ref([]);
const handleSelectionChange = (val) => {
  selectedPoints.value = val.map((item) => item.buildId);
};

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

const imgFile = ref('')
// onMounted
onMounted(() => {
  imgFile.value = window.ipConfig.base2 + '/common/download?token=' + localStorage.getItem("syToken") + '&path='
  // 获得表格数据
  getTableData();
});
function handleCheckboxChange() {

}
</script>

<style lang="scss" scoped>
.customTable {
  // display: flex;
  // flex-direction: row;
  // justify-content: center;
  // align-items: center;
  width: calc(100% + 20px);
  position: relative;
  left: -10px;
  border-bottom: 1px solid #ebeef5;
  height: 44px;
  line-height: 44px;
  min-height: 44px;
}

.customTableEnd {
  // display: flex;
  // flex-direction: row;
  // justify-content: center;
  // align-items: center;
  width: calc(100% + 20px);
  position: relative;
  left: -10px;
  /* height: 23px; */
  line-height: 23px;
  min-height: 23px;

}

.custom-box {
  display: flex;
  justify-content: space-between;
  height: 100%;
  padding: 10px;
  line-height: normal;

  .good-img {
    width: 90px;
    height: 90px;
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .good-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-family: Microsoft YaHei;

    .info-name {
      &>div:nth-of-type(1) {
        font-weight: bold;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
      }

      &>div:nth-of-type(2) {
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
      }
    }

    .info-num {
      padding-left: 20px;
      text-align: right;
    }
  }
}

.operation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #0098FF;
  font-size: 14px;

  div {
    margin: 3px 0;
    cursor: pointer;
  }
}

:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
  color:#555;
}

.dataStatus {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-badge {
    display: flex;
    align-items: center;

    :deep(sup) {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      padding: 0;
      margin-right: 5px;

      .el-badge__content.is-dot {
        width: 10px;
        height: 10px;
      }
    }
  }
}
</style>
