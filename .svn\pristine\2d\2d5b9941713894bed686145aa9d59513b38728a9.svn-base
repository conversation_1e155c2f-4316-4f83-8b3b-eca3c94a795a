<template>
  <nd-dialog ref="dialogRef" width="60vw" height="60vh" :title="page.title" align-center :loading="loading">
    <div class="dialogBox">
      <el-form ref="addFormRef" :model="page.goods" class="add-box">
        <div class="borderBox">
          <div class="box_title">
            <div class="colorLine"></div>基础信息
          </div>
          <el-row>
            <el-col :span="24">
              <el-form-item label="商品图片" label-width="100px" prop="files" style="margin-bottom: 0;">
                <ndb-upload :files="page.goods.files" fzgs="product1" :limit="15" :disabled="true"></ndb-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品名称" label-width="100px" prop="name">
                <span>{{ page.goods.name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供应商" label-width="100px" prop="name">
                <span>{{ page.goods.supplyName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品类型" label-width="100px" prop="categoryId">
                <span>{{ page.goods.categoryName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12"></el-col>
            <el-col :span="12">
              <el-form-item label="商品标签" label-width="100px">
                <span>{{ page.goods.tagName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商城标签" label-width="100px">
                <span>{{ page.goods.tag2Name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="商品描述" label-width="100px" prop="description">
                <span>{{ page.goods.description }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="borderBox">
          <div class="box_title">
            <div class="colorLine"></div>规格信息
          </div>
          <nd-table border style="height: 100%" :data="page.tableList">
            <el-table-column align="center" type="index" width="70px" label="序号" />
            <el-table-column align="center" label="规格" min-width="220px" label-class-name="star"
              show-overflow-tooltip="">
              <template #default="scope">
                <span>{{ scope.row.spData }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="原价" min-width="160px" label-class-name="star">
              <template #default="scope">
                <span>{{ scope.row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="协议价" min-width="160px">
              <template #default="scope">
                <span>{{ scope.row.agreementPrice || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="成本价" min-width="160px">
              <template #default="scope">
                <span>{{ scope.row.costPrice || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="库存" min-width="160px" label-class-name="star">
              <template #default="scope">
                <span>{{ scope.row.stock }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="尺寸" min-width="160px" show-overflow-tooltip="">
              <template #default="scope">
                <span>{{ scope.row.size || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="重量(kg)" min-width="160px">
              <template #default="scope">
                <span>{{ scope.row.weight }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="体积(m³)" min-width="160px">
              <template #default="scope">
                <span>{{ scope.row.volume }}</span>
              </template>
            </el-table-column>
          </nd-table>
        </div>

        <div class="borderBox">
          <div class="box_title">
            <div class="colorLine"></div>商品详情
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="品类" label-width="100px">
                <span>{{ page.goods.plValue }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格" label-width="100px">
                <span>{{ page.goods.ggValue }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="品牌" label-width="100px">
                <span>{{ page.goods.brandName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="货号" label-width="100px">
                <span>{{ page.goods.productSn }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="售后" label-width="100px">
                <span>{{ page.goods.afterSalesTypeName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="特点" label-width="100px">
                <span>{{ page.goods.feature }}</span>
              </el-form-item>
            </el-col>

            <!-- 鱼苗 -->
            <template v-if="page.goods.categoryKey == 'YZYM'">
              <el-col :span="12">
                <el-form-item label="品种名" label-width="100px">
                  <span>{{ page.jsonGoods.pzm }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="成活率" label-width="100px">
                  <span>{{ page.jsonGoods.chl }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="提供技术" label-width="100px">
                  <span>{{ page.jsonGoods.tgjs }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="起批量" label-width="100px">
                  <span>{{ page.jsonGoods.qpj }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发货地址" label-width="100px">
                  <span>{{ page.jsonGoods.fhdz }}</span>
                </el-form-item>
              </el-col>
            </template>
            <!-- 饲料 -->
            <template v-if="page.goods.categoryKey == 'LASL'">
              <el-col :span="12">
                <el-form-item label="饲料种类" label-width="100px">
                  <span>{{ page.jsonGoods.slzl }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="适用鱼" label-width="100px">
                  <span>{{ page.jsonGoods.ssy }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="生产厂家" label-width="100px">
                  <span>{{ page.jsonGoods.sccj }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产地" label-width="100px">
                  <span>{{ page.jsonGoods.cd }}</span>
                </el-form-item>
              </el-col>
            </template>
            <!-- 鱼药 -->
            <template v-if="page.goods.categoryKey == 'DBYY'">
              <el-col :span="12">
                <el-form-item label="原料组成" label-width="100px">
                  <span>{{ page.jsonGoods.ylzc }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性状" label-width="100px">
                  <span>{{ page.jsonGoods.xz }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="适用对象" label-width="100px">
                  <span>{{ page.jsonGoods.sydx }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="保质期" label-width="100px">
                  <span>{{ page.jsonGoods.bzq }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="储藏方式" label-width="100px">
                  <span>{{ page.jsonGoods.ccfs }}</span>
                </el-form-item>
              </el-col>
            </template>
            <!-- 设备 -->
            <template v-if="page.goods.categoryKey == 'YZSB'">
              <el-col :span="12">
                <el-form-item label="适用对象" label-width="100px">
                  <span>{{ page.jsonGoods.sydx }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用途" label-width="100px">
                  <span>{{ page.jsonGoods.yt }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="类型" label-width="100px">
                  <span>{{ page.jsonGoods.lx }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="适用场所" label-width="100px">
                  <span>{{ page.jsonGoods.sycs }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="型号" label-width="100px">
                  <span>{{ page.jsonGoods.xh }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品类型" label-width="100px">
                  <span>{{ page.jsonGoods.cplx }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="功率" label-width="100px">
                  <span>{{ page.jsonGoods.gl }}</span>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="24">
              <el-form-item label="详情图片" label-width="100px">
                <ndb-upload :files="page.goods.files2" fzgs="product2" :disabled="true"></ndb-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <template #footer>
      <nd-button type="" icon="Close" @click="close">关&nbsp;闭</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入公共组件
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndbUpload from "@/components/business/ndbUpload/index.vue";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 导入element-plus
import { ElMessage as elMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义ref
const dialogRef = ref(null);
const addFormRef = ref(null);
let currentTab = ref(0);
let stepOptions = [
  {
    label: "商品基本信息",
    value: "0",
  },
  {
    label: "商品详情",
    value: "1",
  },
];
// 定义page
const page = reactive({
  title: "",
  tableList: [
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
  ],
  dict: {
    categoryList: [], // 商品分类
    goodsTagList: [], // 商品标签
    indexTypeList: [], // 商城标签
    brandList: [], // 品牌
    afterSaleList: [], // 售后
  },
  goods: {
    productId: "",
    name: "", // 商品名称
    supplyName: "", // 供应商名称
    files: [], // 商品展示图片
    description: "", // 描述
    categoryId: null, // 类别
    categoryName: "", // 类别
    categoryKey: null, // 类别key:详情字段判断用
    tagId: [], // 商品标签
    tagName: "",
    tagId2: [], // 商城标签
    tag2Name: "",
    plValue: "", // 品类
    ggValue: "", // 规格
    brandId: null, // 品牌
    brandName: null, // 品牌
    productSn: "", // 货号
    afterSalesType: null, // 售后
    afterSalesTypeName: null, // 售后
    feature: "", // 特点
    files2: [], // 商品详情图片
  },
  jsonGoods: {
    pzm: "", // 品种名
    chl: "", // 成活率
    tgjs: "", // 提供技术
    qpj: "", // 起批量
    fhdz: "", // 发货地址
    slzl: "", // 饲料种类
    ssy: "", // 适用鱼
    sccj: "", // 生产厂家
    cd: "", // 产地
    ylzc: "", // 原料组成
    xz: "", // 性状
    bzq: "", // 适用对象
    ccfs: "", // 保质期
    sydx: "", // 储藏方式
    yt: "", // 用途
    lx: "", // 类型
    sycs: "", // 适用场所
    xh: "", // 型号
    cplx: "", // 产品类型
    gl: "", // 功率
  },
});

let loading = ref(false)

watch(
  () => page.goods.files.length,
  (newValue, oldValue) => {
    if (addFormRef.value) addFormRef.value.validateField('files')
  },
  { deep: true }
);

// 打开弹窗
function open(params) {
  console.log("params", params);

  currentTab.value = 0;
  page.tableList = [
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
  ];

  Object.keys(page.goods).forEach((key) => {
    page.goods[key] = "";
  });
  page.goods.files = [];
  page.goods.files2 = [];
  page.goods.tagId = [];
  page.goods.tagId2 = [];

  Object.keys(page.jsonGoods).forEach((key) => {
    page.jsonGoods[key] = "";
  });

  page.title = "商品详情";
  page.goods.productId = params.productId;
  getDetail();
  dialogRef.value.open();
}

// 获得详情
function getDetail() {
  loading.value = true
  $axios({
    url: "/goods/getDetail",
    method: "get",
    data: {
      id: page.goods.productId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      // page.goods = res.data.data;
      page.goods.productId = res.data.data.productId;
      page.goods.name = res.data.data.name;
      page.goods.supplyName = res.data.data.supplyName;
      page.goods.description = res.data.data.description;
      page.goods.categoryId = res.data.data.categoryId;
      page.goods.categoryKey = res.data.data.categoryKey || null;
      page.goods.categoryName = res.data.data.categoryName;
      page.goods.plValue = res.data.data.plValue;
      page.goods.ggValue = res.data.data.ggValue;
      page.goods.brandId = res.data.data.brandId || null;
      page.goods.brandName = res.data.data.brandName || null;
      page.goods.productSn = res.data.data.productSn;
      // page.goods.afterSalesType = res.data.data.afterSalesType || null;
      page.goods.afterSalesTypeName = '退货退款'; // 写死
      page.goods.feature = res.data.data.feature;
      // 文件
      if (res.data.data.files.length > 0) {
        page.goods.files = res.data.data.files.filter((item) => item.fzgs == "product1");
        page.goods.files2 = res.data.data.files.filter((item) => item.fzgs == "product2");
      }
      // 规格
      page.tableList = res.data.data.spec || []
      if (page.tableList.length > 0) {
        page.tableList = page.tableList.map(item => {
          return {
            ...item,
            price: (item.price / 100).toFixed(2),
            agreementPrice: item.agreementPrice > 0 ? (item.agreementPrice / 100).toFixed(2) : null,
            costPrice: item.costPrice > 0 ? (item.costPrice / 100).toFixed(2) : null,
          }
        })
      }

      // 标签
      if (res.data.data.tags.length > 0) {
        let tagId = res.data.data.tags.filter((item) => item.type == "product");
        if (tagId.length > 0) {
          page.goods.tagId = tagId.map(item => item.tagId)
          page.goods.tagName = tagId.map(item => item.name).join(',')
        } else {
          page.goods.tagId = []
          page.goods.tagName = ""
        }

        let tagId2 = res.data.data.tags.filter((item) => item.type == "index");
        if (tagId2.length > 0) {
          page.goods.tagId2 = tagId2.map(item => item.tagId)
          page.goods.tag2Name = tagId2.map(item => item.name).join(',')
        } else {
          page.goods.tagId2 = []
          page.goods.tag2Name = ""
        }
      }
      page.jsonGoods = JSON.parse(res.data.data.attributeJson);
    }
  }).finally(() => {
    loading.value = false
  });
}

// 新增一行
function addNewTableLine() {
  page.tableList.push({ spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" });
}

// 删除一行
function delTableLine(row, index) {
  console.log(row, index);
  if (page.tableList.length < 2) {
    page.tableList = [{ spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" }]
  } else {
    page.tableList.splice(index, 1)
  }
}

// 关闭
const close = () => {
  dialogRef.value.close();
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 清空表单
  page.goods = {
    id: "",
    name: "",
    files: [],
  };
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content span) {
  word-break: break-all;
}

:deep(.el-form-item__label) {
  color: #555;
}

:deep(.el-form-item__content) {
  color: #333;
}

:deep(.el-table th.el-table__cell) {
  color: #303133 !important;
}

:deep(.star) {
  .cell::before {
    content: "*";
    color: #f56c6c;
    margin-right: 2px;
  }
}

.dialogBox {
  padding: 15px 12px;
}

.my_stepTab {
  position: sticky;
  z-index: 99;
  top: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin: 0 0 12px;
  border: 1px solid #EAEAEA;
  padding: 12px;
  border-radius: 5px;

  .stepItem {
    width: 180px;
    position: relative;
    z-index: 99;
    height: 32px;
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }

    .stepItem_title {
      color: #666;
      height: 32px;
      line-height: 32px;
      text-align: center;
    }

    .silk_ribbon0 {
      display: inline-block;
      position: absolute;
      z-index: -1;
      width: 100%;
      height: 32px;
      line-height: 32px;
      padding-left: 15px;
      background: #f2f2f2;
      left: 0;
      top: 0;
    }

    .silk_ribbon0:after {
      content: "";
      position: absolute;
    }

    .silk_ribbon0:after {
      height: 0;
      width: 0;
      border-top: 16px solid transparent;
      border-bottom: 16px solid transparent;
      border-left: 16px solid #f2f2f2;
      right: -16px;
    }

    .silk_ribbon1 {
      display: inline-block;
      position: absolute;
      z-index: -1;
      width: calc(100% - 20px);
      height: 32px;
      line-height: 32px;
      background: #f2f2f2;
      top: 0;
      left: 10px;
    }

    .silk_ribbon1:before,
    .silk_ribbon1:after {
      content: "";
      position: absolute;
    }

    .silk_ribbon1:before {
      height: 0;
      width: 0;
      border-left: 16px solid transparent;
      border-top: 16px solid #f2f2f2;
      border-bottom: 16px solid #f2f2f2;
      bottom: 0;
      left: -16px;
    }

    .silk_ribbon1:after {
      height: 0;
      width: 0;
      border-top: 16px solid transparent;
      border-bottom: 16px solid transparent;
      border-left: 16px solid #f2f2f2;
      right: -16px;
    }
  }

  .activeTab {
    .stepItem_title {
      color: #fff;
    }

    .silk_ribbon {
      background: #068324;
    }

    .silk_ribbon0:after {
      border-left-color: #068324;
    }

    .silk_ribbon1:before {
      border-top-color: #068324;
      border-bottom-color: #068324;
      bottom: 0;
      left: -15px;
    }

    .silk_ribbon1:after {
      border-left-color: #068324;
      right: -15px;
    }
  }
}

.add-box {

  :deep(.el-textarea__inner) {
    height: 80px;
  }
}

.borderBox {
  border: 1px solid #EAEAEA;
  background-color: #fff;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 5px;
}

.box_title {
  font-family: Microsoft YaHei;
  font-size: 16px;
  font-weight: bold;
  color: #444444;
  display: flex;
  align-items: center;
  margin-bottom: 14px;

  .colorLine {
    width: 2px;
    height: 16px;
    background: #068324;
    margin-right: 6px;
  }
}

.suggest {
  font-family: Microsoft YaHei UI;
  font-size: 12px;
  line-height: 22px;
  color: #C0C4CC;
}

.fileError {
  padding-top: 20px;
}

.addNewTable {
  width: fit-content;
  color: #068324;
  text-decoration: underline;
  margin: 10px 0;
  cursor: pointer;
}
</style>
