<template>
  <nd-dialog ref="dialogRef" width="60vw" height="60vh" :title="page.title" align-center :loading="loading">
    <div class="dialogBox">
      <div class="my_stepTab">
        <div class="stepItem" v-for="(item, index) in stepOptions" :key="index"
          :class="currentTab === index ? 'activeTab' : ''" @click="tabClick(index)">
          <div :class="`silk_ribbon silk_ribbon${index}`"></div>
          <div class="stepItem_title">{{ page.status === 'edit' ? '编辑' + item.label : item.label }}</div>
        </div>
      </div>
      <div class="add-box">
        <el-form ref="addFormRef" :model="page.goods" :rules="rules">
          <div v-show="currentTab === 0">
            <div class="borderBox">
              <div class="box_title">
                <div class="colorLine"></div>基础信息
              </div>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="商品图片" label-width="100px" prop="files" style="margin-bottom: 0;">
                    <ndb-upload :files="page.goods.files" fzgs="product1" :limit="15"></ndb-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="" label-width="100px" style="margin-bottom: 8px;">
                    <div class="suggest" :class="fileError ? 'fileError' : ''">
                      建议尺寸：800*800像素，最多上传15张</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="商品名称" label-width="100px" prop="name">
                    <nd-input v-model="page.goods.name" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="供应商" label-width="100px" prop="supplyId">
                    <ndb-area-tree v-model="page.goods.supplyId" width="100%" ref="treeAllRef" placeholder="请选择"
                      :default-expanded-keys="defaultExpandedKeys" :teleported="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="商品类型" label-width="100px" prop="categoryId">
                    <nd-select v-model="page.goods.categoryId" placeholder="请选择" clearable width="100%"
                      @change="categoryChange">
                      <el-option v-for="item in page.dict.categoryList" :label="item.name" :key="item.categoryId"
                        :value="item.categoryId" />
                    </nd-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12"></el-col>
                <el-col :span="12">
                  <el-form-item label="商品标签" label-width="100px">
                    <nd-select v-model="page.goods.tagId" placeholder="请选择" multiple clearable width="100%">
                      <el-option v-for="item in page.dict.goodsTagList" :label="item.name" :key="item.tagId"
                        :value="item.tagId" />
                    </nd-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="商城标签" label-width="100px">
                    <nd-select v-model="page.goods.tagId2" placeholder="请选择" multiple clearable width="100%">
                      <el-option v-for="item in page.dict.indexTypeList" :label="item.name" :key="item.tagId"
                        :value="item.tagId" />
                    </nd-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="商品描述" label-width="100px" prop="description">
                    <nd-input v-model="page.goods.description" type="textarea" placeholder="请输入" width="100%"
                      maxlength="500" show-word-limit></nd-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="borderBox">
              <div class="box_title">
                <div class="colorLine"></div>规格信息
              </div>
              <nd-table border style="height: 100%" :data="page.tableList">
                <el-table-column align="center" type="index" width="70px" label="序号" />
                <el-table-column align="center" label="规格" min-width="220px" label-class-name="star"
                  show-overflow-tooltip="">
                  <template #default="scope">
                    <nd-input v-model.trim="scope.row.spData" width="100%" placeholder="请输入规格，如：1kg/包" maxlength="50" />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="原价" min-width="160px" label-class-name="star">
                  <template #default="scope">
                    <nd-input v-model.trim="scope.row.price" width="100%" type2="number02" placeholder="请输入" />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="协议价" min-width="160px">
                  <template #default="scope">
                    <nd-input v-model.trim="scope.row.agreementPrice" type2="number02" width="100%" placeholder="请输入" />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="成本价" min-width="160px">
                  <template #default="scope">
                    <nd-input v-model.trim="scope.row.costPrice" type2="number02" width="100%" placeholder="请输入" />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="库存" min-width="160px" label-class-name="star">
                  <template #default="scope">
                    <span v-if="scope.row.skuId">{{ scope.row.stock }}</span>
                    <nd-input v-else v-model.trim="scope.row.stock"
                      :type2="page.status == 'add' ? 'number4' : 'number3'" width="100%" placeholder="请输入" />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="尺寸" min-width="160px" show-overflow-tooltip="">
                  <template #default="scope">
                    <nd-input v-model.trim="scope.row.size" width="100%" placeholder="请输入" maxlength="50" />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="重量(kg)" min-width="160px">
                  <template #default="scope">
                    <nd-input v-model.trim="scope.row.weight" type2="number2" width="100%" placeholder="请输入" />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="体积(m³)" min-width="160px">
                  <template #default="scope">
                    <nd-input v-model.trim="scope.row.volume" type2="number2" width="100%" placeholder="请输入" />
                  </template>
                </el-table-column>
                <el-table-column align="center" fixed="right" label="操作" width="60px">
                  <template #default="scope">
                    <el-icon color="#0b8df1" :size="16" style="cursor: pointer;margin-top: 8px;"
                      @click="delTableLine(scope.row, scope.$index)">
                      <Delete />
                    </el-icon>
                  </template>
                </el-table-column>
              </nd-table>
              <div class="addNewTable" @click="addNewTableLine">新增一行</div>
            </div>
          </div>
        </el-form>
        <el-form ref="detailFormRef" :model="page.goods" :rules="detailRules">
          <div v-show="currentTab === 1">
            <div class="borderBox">
              <div class="box_title">
                <div class="colorLine"></div>商品详情
              </div>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="品类" label-width="100px">
                    <nd-input v-model="page.goods.plValue" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="规格" label-width="100px">
                    <nd-input v-model="page.goods.ggValue" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="品牌" label-width="100px">
                    <nd-select v-model="page.goods.brandId" placeholder="请选择" clearable width="100%">
                      <el-option v-for="item in page.dict.brandList" :label="item.name" :key="item.brandId"
                        :value="item.brandId" />
                    </nd-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="货号" label-width="100px">
                    <nd-input v-model="page.goods.productSn" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="售后" label-width="100px" prop="afterSalesType">
                    <nd-select v-model="page.goods.afterSalesType" placeholder="请选择" clearable width="100%">
                      <el-option v-for="item in page.dict.afterSaleList" :label="item.dictValue" :key="item.dictKey"
                        :value="item.dictKey" />
                    </nd-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="特点" label-width="100px">
                    <nd-input v-model="page.goods.feature" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                  </el-form-item>
                </el-col>

                <!-- 鱼苗 -->
                <template v-if="page.goods.categoryKey == 'YZYM'">
                  <el-col :span="12">
                    <el-form-item label="品种名" label-width="100px">
                      <nd-input v-model="page.jsonGoods.pzm" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="成活率" label-width="100px">
                      <nd-input v-model="page.jsonGoods.chl" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="提供技术" label-width="100px">
                      <nd-input v-model="page.jsonGoods.tgjs" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="起批量" label-width="100px">
                      <nd-input v-model="page.jsonGoods.qpj" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发货地址" label-width="100px">
                      <nd-input v-model="page.jsonGoods.fhdz" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                </template>
                <!-- 饲料 -->
                <template v-if="page.goods.categoryKey == 'LASL'">
                  <el-col :span="12">
                    <el-form-item label="饲料种类" label-width="100px">
                      <nd-input v-model="page.jsonGoods.slzl" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="适用鱼" label-width="100px">
                      <nd-input v-model="page.jsonGoods.ssy" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生产厂家" label-width="100px">
                      <nd-input v-model="page.jsonGoods.sccj" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="产地" label-width="100px">
                      <nd-input v-model="page.jsonGoods.cd" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                </template>
                <!-- 鱼药 -->
                <template v-if="page.goods.categoryKey == 'DBYY'">
                  <el-col :span="12">
                    <el-form-item label="原料组成" label-width="100px">
                      <nd-input v-model="page.jsonGoods.ylzc" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="性状" label-width="100px">
                      <nd-input v-model="page.jsonGoods.xz" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="适用对象" label-width="100px">
                      <nd-input v-model="page.jsonGoods.sydx" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="保质期" label-width="100px">
                      <nd-input v-model="page.jsonGoods.bzq" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="储藏方式" label-width="100px">
                      <nd-input v-model="page.jsonGoods.ccfs" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                </template>
                <!-- 设备 -->
                <template v-if="page.goods.categoryKey == 'YZSB'">
                  <el-col :span="12">
                    <el-form-item label="适用对象" label-width="100px">
                      <nd-input v-model="page.jsonGoods.sydx" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="用途" label-width="100px">
                      <nd-input v-model="page.jsonGoods.yt" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="类型" label-width="100px">
                      <nd-input v-model="page.jsonGoods.lx" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="适用场所" label-width="100px">
                      <nd-input v-model="page.jsonGoods.sycs" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="型号" label-width="100px">
                      <nd-input v-model="page.jsonGoods.xh" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="产品类型" label-width="100px">
                      <nd-input v-model="page.jsonGoods.cplx" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="功率" label-width="100px">
                      <nd-input v-model="page.jsonGoods.gl" placeholder="请输入" width="100%" maxlength="50"></nd-input>
                    </el-form-item>
                  </el-col>
                </template>
                <el-col :span="24">
                  <el-form-item label="详情图片" label-width="100px">
                    <ndb-upload :files="page.goods.files2" fzgs="product2"></ndb-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <template #footer>
      <nd-button type="" v-if="currentTab == 0" @click="tabClick(1)">下一步</nd-button>
      <nd-button type="" v-if="currentTab == 1" @click="tabClick(0)">上一步</nd-button>
      <nd-button type="primary" v-if="currentTab == 1" icon="FolderChecked" @click="save">保&nbsp;存</nd-button>
      <nd-button type="" icon="Close" @click="close">关&nbsp;闭</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入公共组件
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndbUpload from "@/components/business/ndbUpload/index.vue";
import ndbAreaTree from "@/components/business/ndbAreaTree/index.vue";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 导入element-plus
import { ElMessage as elMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义ref
const dialogRef = ref(null);
const addFormRef = ref(null);
const detailFormRef = ref(null);

let currentTab = ref(0);
let stepOptions = [
  {
    label: "商品基本信息",
    value: "0",
  },
  {
    label: "商品详情",
    value: "1",
  },
];
// 定义page
const page = reactive({
  status: "",
  title: "",
  tableList: [
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
  ],
  dict: {
    categoryList: [], // 商品分类
    goodsTagList: [], // 商品标签
    indexTypeList: [], // 商城标签
    brandList: [], // 品牌
    afterSaleList: [{
      "dictId": "1938074842234490880",
      "dictName": "售后",
      "dictKey": "return_refund",
      "dictValue": "退货退款",
      "dictMark": "SH",
      "dictPid": "1938066248340475904",
      "allPath": "1938066248340475904|1938074842234490880",
      "orderCode": 1,
      "describes": "",
      "del": 0,
      "insertTime": "2025-06-26 11:20:25",
      "updateTime": "2025-06-27 15:53:04"
    }], // 售后
  },
  specDelArr: [],
  goods: {
    productId: "",
    name: "", // 商品名称
    supplyId: [], // 供应商
    supplyIdOld: [], // 供应商(详情接口传回来的)
    files: [], // 商品展示图片
    description: "", // 描述
    categoryId: null, // 类别
    categoryName: "", // 类别
    categoryKey: null, // 类别key:详情字段判断用
    tagId: [], // 商品标签
    tagName: "",
    tagId2: [], // 商城标签
    tag2Name: "",
    plValue: "", // 品类
    ggValue: "", // 规格
    brandId: null, // 品牌
    brandName: null, // 品牌
    productSn: "", // 货号
    afterSalesType: null, // 售后
    feature: "", // 特点
    files2: [], // 商品详情图片
  },
  jsonGoods: {
    pzm: "", // 品种名
    chl: "", // 成活率
    tgjs: "", // 提供技术
    qpj: "", // 起批量
    fhdz: "", // 发货地址
    slzl: "", // 饲料种类
    ssy: "", // 适用鱼
    sccj: "", // 生产厂家
    cd: "", // 产地
    ylzc: "", // 原料组成
    xz: "", // 性状
    bzq: "", // 适用对象
    ccfs: "", // 保质期
    sydx: "", // 储藏方式
    yt: "", // 用途
    lx: "", // 类型
    sycs: "", // 适用场所
    xh: "", // 型号
    cplx: "", // 产品类型
    gl: "", // 功率
  },
});
let defaultExpandedKeys = reactive([]);

let loading = ref(false)

watch(
  () => page.goods.files.length,
  (newValue, oldValue) => {
    if (addFormRef.value) addFormRef.value.validateField('files')
  },
  { deep: true }
);

// 表单校验规则
const rules = reactive({
  files: [{ required: true, validator: fileRule, trigger: ["blur", "change"] }],
  name: [{ required: true, message: "请输入商品名称！", trigger: ["blur", "change"] }],
  supplyId: [{ required: true, message: "请选择供应商！", trigger: ["blur", "change"] }],
  categoryId: [{ required: true, message: "请选择商品类型！", trigger: ["blur", "change"] }],
  description: [{ required: true, message: "请输入商品描述！", trigger: ["blur", "change"] }],
});

const detailRules = reactive({
  afterSalesType: [{ required: true, message: "请选择售后！", trigger: ["blur", "change"] }],
})

let fileError = ref(false)
function fileRule(rule, value, callback) {
  if (page.goods.files.length < 1) {
    fileError.value = true
    return callback(new Error("请上传商品图片！"));
  } else {
    fileError.value = false
    return callback();
  }
};

// 打开弹窗
function open(status, params) {
  console.log("params", params);

  currentTab.value = 0;
  page.tableList = [
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
    { spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" },
  ];
  page.specDelArr = []
  Object.keys(page.goods).forEach((key) => {
    page.goods[key] = "";
  });
  page.goods.supplyId = [];
  page.goods.supplyIdOld = [];
  page.goods.files = [];
  page.goods.files2 = [];
  page.goods.tagId = [];
  page.goods.tagId2 = [];

  Object.keys(page.jsonGoods).forEach((key) => {
    page.jsonGoods[key] = "";
  });

  getTagList();
  getCategoryList();
  getBrandList();
  // getAfterSaleList();
  defaultExpandedKeys = []
  if (status === "add") {
    page.status = "add";
    page.title = "添加商品";
    dialogRef.value.open();
  }
  if (status === "edit") {
    page.status = "edit";
    page.title = "编辑商品";
    page.goods.productId = params.productId;
    getDetail();
    dialogRef.value.open();
  }
}

const tabClick = async (index) => {
  if (index > 0) {
    await addFormRef.value.validate((valid, fields) => {
      if (valid) {
        // 过滤无数据的数据
        let notNullSpec = page.tableList.filter(item => item.spData || item.price || item.agreementPrice || item.costPrice || item.stock || item.size || item.weight || item.volume)
        if (notNullSpec.length < 1) {
          return elMessage.error('无有效规格数据！')

        }
        let errorMsg = ""
        notNullSpec.forEach(ele => {
          if (errorMsg) return
          if (!ele.spData) return errorMsg = '规格字段不能为空！'
          if (!ele.price) return errorMsg = '原价字段不能为空！'
          if (ele.price * 1 == 0) return errorMsg = '原价必须大于0！'
          if (ele.stock === '') return errorMsg = "库存不能为空！"
          if (ele.stock * 1 > 999999999) return errorMsg = "库存最大限制999999999"
        })
        if (errorMsg) return elMessage.error(errorMsg)
        currentTab.value = index;
      } else {
        return elMessage.error('基本信息有必填信息为空，请填写。')
      }
    })
  } else {
    currentTab.value = index;
  }
}

function categoryChange(e) {
  console.log('e', e);
  page.dict.categoryList.forEach(ele => {
    if (ele.categoryId === e) {
      page.goods.categoryKey = ele.categoryKey
    }
  })
}

// 商品分类
function getCategoryList() {
  $axios({
    url: "/dict/getCategory",
    method: "get",
  }).then((res) => {
    if (res.data.code === 2000) page.dict.categoryList = res.data.data;
  });
}

// 商品标签
function getTagList() {
  $axios({
    url: "/dict/getGoodsTag",
    method: "get",
  }).then((res) => {
    if (res.data.code === 2000) {
      page.dict.goodsTagList = res.data.data.filter((item) => item.type == "product");
      page.dict.indexTypeList = res.data.data.filter((item) => item.type == "index");
    }
  });
}

// 获取商品品牌
function getBrandList() {
  $axios({
    url: "/dict/getbrand",
    method: "get",
  }).then((res) => {
    if (res.data.code === 2000) page.dict.brandList = res.data.data;
  });
}

// 获取售后
function getAfterSaleList() {
  $axios({
    url: "/dict/getDict",
    method: "get",
    data: {
      dict_mark: "SH",
    },
  }).then((res) => {
    if (res.data.code === 2000) page.dict.afterSaleList = res.data.data;
  });
}

// 获得详情
function getDetail() {
  loading.value = true
  $axios({
    url: "/goods/getDetail",
    method: "get",
    data: {
      id: page.goods.productId,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      // page.goods = res.data.data;
      page.goods.productId = res.data.data.productId;
      page.goods.name = res.data.data.name;
      page.goods.supplyId = res.data.data.supplyId.map(item => item.supplierId);
      page.goods.supplyIdOld = [...page.goods.supplyId]
      let allPath = res.data.data.supplyId.map(item => item.allPath);
      allPath = allPath.join("|").split("|");
      allPath = Array.from(new Set(allPath))
      defaultExpandedKeys = allPath

      page.goods.description = res.data.data.description;
      page.goods.categoryId = res.data.data.categoryId;
      page.goods.categoryKey = res.data.data.categoryKey || null;
      page.goods.categoryName = res.data.data.categoryName;
      page.goods.plValue = res.data.data.plValue;
      page.goods.ggValue = res.data.data.ggValue;
      page.goods.brandId = res.data.data.brandId || null;
      page.goods.brandName = res.data.data.brandName || null;
      page.goods.productSn = res.data.data.productSn;
      page.goods.afterSalesType = res.data.data.afterSalesType || null;
      page.goods.feature = res.data.data.feature;
      // 文件
      if (res.data.data.files.length > 0) {
        page.goods.files = res.data.data.files.filter((item) => item.fzgs == "product1");
        page.goods.files2 = res.data.data.files.filter((item) => item.fzgs == "product2");
      }
      // 规格
      page.tableList = res.data.data.spec || []
      if (page.tableList.length > 0) {
        page.tableList = page.tableList.map(item => {
          return {
            ...item,
            price: (item.price / 100).toFixed(2),
            agreementPrice: item.agreementPrice > 0 ? (item.agreementPrice / 100).toFixed(2) : null,
            costPrice: item.costPrice > 0 ? (item.costPrice / 100).toFixed(2) : null,
          }
        })
      }
      if (page.tableList.length < 2) {
        for (let i = 0; i < (2 - page.tableList.length); i++) {
          addNewTableLine()
        }
      }

      // 标签
      if (res.data.data.tags.length > 0) {
        let tagId = res.data.data.tags.filter((item) => item.type == "product");
        if (tagId.length > 0) {
          page.goods.tagId = tagId.map(item => item.tagId)
          page.goods.tagName = tagId.map(item => item.name).join(',')
        } else {
          page.goods.tagId = []
          page.goods.tagName = ""
        }

        let tagId2 = res.data.data.tags.filter((item) => item.type == "index");
        if (tagId2.length > 0) {
          page.goods.tagId2 = tagId2.map(item => item.tagId)
          page.goods.tag2Name = tagId2.map(item => item.name).join(',')
        } else {
          page.goods.tagId2 = []
          page.goods.tag2Name = ""
        }
      }
      page.jsonGoods = JSON.parse(res.data.data.attributeJson);
    }
  }).finally(() => {
    loading.value = false
  });
}

// 新增一行
function addNewTableLine() {
  page.tableList.push({ spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" });
}

// 删除一行
function delTableLine(row, index) {
  console.log("delTableLine", row, index);
  if (row.skuId) page.specDelArr.push(row.skuId)
  if (page.tableList.length < 2) {
    page.tableList = [{ spData: "", price: "", agreementPrice: "", costPrice: "", stock: "", size: "", weight: "", volume: "" }]
  } else {
    page.tableList.splice(index, 1)
  }
}

// 关闭
const close = () => {
  dialogRef.value.close();
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
};

let postTimer = ref(null)
// 保存
const save = async () => {
  await detailFormRef.value.validate((valid, fields) => {
    if (valid) {
      // 过滤无数据的数据
      let notNullSpec = page.tableList.filter(item => item.spData || item.price || item.agreementPrice || item.costPrice || item.stock || item.size || item.weight || item.volume)

      notNullSpec = notNullSpec.map(item => {
        return {
          ...item,
          price: Math.round(item.price * 100),
          agreementPrice: item.agreementPrice > 0 ? Math.round(item.agreementPrice * 100) : null,
          costPrice: item.costPrice > 0 ? Math.round(item.costPrice * 100) : null,
        }
      })
      // page.goods.supplyId = "1912342089929199617,1912342089929199618"
      const specs_edit = notNullSpec.filter(item => item.skuId) // 编辑的规格
      const specs_add = notNullSpec.filter(item => !item.skuId) // 新增的规格
      const specs_del = [...page.specDelArr] // 删除的规格
      let postData = {
        name: page.goods.name,
        supplyId: page.goods.supplyId.join(','),
        files: page.goods.files,
        description: page.goods.description,
        categoryId: page.goods.categoryId,
        tagId: page.goods.tagId,
        tagId2: page.goods.tagId2,
        plValue: page.goods.plValue,
        ggValue: page.goods.ggValue,
        brandId: page.goods.brandId || null,
        productSn: page.goods.productSn,
        afterSalesType: page.goods.afterSalesType || null,
        feature: page.goods.feature,
        pic: page.goods.files[0].sourcePath, // 第一张图
        attributeJson: JSON.stringify(page.jsonGoods), // 其他属性json
        // specs: notNullSpec,
        specs: specs_add,
        specs_edit: specs_edit,
        specs_del: specs_del,
        files2: page.goods.files2,
      };
      console.log("postData", postData);

      // return 
      // 编辑
      if (page.status === "edit") {
        edit(postData);
      } else {
        add(postData)
      }
    }
  })
};

// 新增
function add(postData) {
  if (postTimer.value) return
  postTimer.value = setTimeout(() => { }, 3000);
  $axios({
    url: "/goods/add",
    method: "post",
    data: postData,
  }).then((res) => {
    if (res.data.code === 2000) {
      elMessage({
        message: "保存成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  }).finally(() => {
    postTimer.value = null
  });
}

// 编辑
function edit(postData) {
  let allStock = 0
  let allData = [...postData.specs, ...postData.specs_edit]
  allData.forEach(ele => {
    allStock += ele.stock
  })
  if (allStock < 1) return elMessage.error('规格列表总库存数必须大于0！')
  if (postTimer.value) return
  postTimer.value = setTimeout(() => { }, 3000);
  $axios({
    url: "/goods/update",
    method: "post",
    data: {
      productId: page.goods.productId,
      ...postData,
      supplyIdOld: page.goods.supplyIdOld.join(','),
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      elMessage({
        message: "保存成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  }).finally(() => {
    postTimer.value = null
  });
}

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content span) {
  word-break: break-all;
}

:deep(.el-form-item__label) {
  color: #555;
}

:deep(.el-table th.el-table__cell) {
  color: #303133 !important;
}

:deep(.star) {
  .cell::before {
    content: "*";
    color: #f56c6c;
    margin-right: 2px;
  }
}

.dialogBox {
  padding: 15px 12px;
}

.my_stepTab {
  position: sticky;
  z-index: 99;
  top: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin: 0 0 12px;
  border: 1px solid #EAEAEA;
  padding: 12px;
  border-radius: 5px;

  .stepItem {
    width: 180px;
    position: relative;
    z-index: 99;
    height: 32px;
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }

    .stepItem_title {
      color: #666;
      height: 32px;
      line-height: 32px;
      text-align: center;
    }

    .silk_ribbon0 {
      display: inline-block;
      position: absolute;
      z-index: -1;
      width: 100%;
      height: 32px;
      line-height: 32px;
      padding-left: 15px;
      background: #f2f2f2;
      left: 0;
      top: 0;
    }

    .silk_ribbon0:after {
      content: "";
      position: absolute;
    }

    .silk_ribbon0:after {
      height: 0;
      width: 0;
      border-top: 16px solid transparent;
      border-bottom: 16px solid transparent;
      border-left: 16px solid #f2f2f2;
      right: -16px;
    }

    .silk_ribbon1 {
      display: inline-block;
      position: absolute;
      z-index: -1;
      width: calc(100% - 20px);
      height: 32px;
      line-height: 32px;
      background: #f2f2f2;
      top: 0;
      left: 10px;
    }

    .silk_ribbon1:before,
    .silk_ribbon1:after {
      content: "";
      position: absolute;
    }

    .silk_ribbon1:before {
      height: 0;
      width: 0;
      border-left: 16px solid transparent;
      border-top: 16px solid #f2f2f2;
      border-bottom: 16px solid #f2f2f2;
      bottom: 0;
      left: -16px;
    }

    .silk_ribbon1:after {
      height: 0;
      width: 0;
      border-top: 16px solid transparent;
      border-bottom: 16px solid transparent;
      border-left: 16px solid #f2f2f2;
      right: -16px;
    }
  }

  .activeTab {
    .stepItem_title {
      color: #fff;
    }

    .silk_ribbon {
      background: #068324;
    }

    .silk_ribbon0:after {
      border-left-color: #068324;
    }

    .silk_ribbon1:before {
      border-top-color: #068324;
      border-bottom-color: #068324;
      bottom: 0;
      left: -15px;
    }

    .silk_ribbon1:after {
      border-left-color: #068324;
      right: -15px;
    }
  }
}

.add-box {
  :deep(.el-textarea__inner) {
    height: 80px;
  }
}

.borderBox {
  border: 1px solid #EAEAEA;
  background-color: #fff;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 5px;
}

.box_title {
  font-family: Microsoft YaHei;
  font-size: 16px;
  font-weight: bold;
  color: #444444;
  display: flex;
  align-items: center;
  margin-bottom: 14px;

  .colorLine {
    width: 2px;
    height: 16px;
    background: #068324;
    margin-right: 6px;
  }
}

.suggest {
  font-family: Microsoft YaHei UI;
  font-size: 12px;
  line-height: 22px;
  color: #C0C4CC;
}

.fileError {
  padding-top: 20px;
}

.addNewTable {
  width: fit-content;
  color: #068324;
  text-decoration: underline;
  margin: 10px 0;
  cursor: pointer;
}
</style>
