<template>
    <ndDialog
        ref="dialogRef"
        :title="dialogData.title"
        :before-close="close"
        width="600px"
        height="250px"
        align-center
    >
        <div class="view">
            <div class="main">
                <div class="alert">
                    <div class="img">
                        <img
                            src="@/assets/images/purchaseRequestFormView/lamp.png"
                            alt=""
                        />
                    </div>

                    <div class="text">需求单取消后无法恢复，请谨慎操作。</div>
                </div>

                <el-form
                    :model="cancelParams"
                    :rules="cancelRules"
                    ref="cancelFormRef"
                >
                    <div class="form">
                        <div class="form-item">
                            <el-form-item
                                style="width: 100%"
                                prop="cancelReason"
                            >
                                <div class="form-item-title">
                                    <span>*</span>
                                    取消理由
                                </div>
                                <div class="form-item-content">
                                    <ndInput
                                        v-model="cancelParams.cancelReason"
                                        type="textarea"
                                        style="width: 100%"
                                        maxlength="200"
                                        :autosize="{ minRows: 2, maxRows: 6 }"
                                    >
                                    </ndInput>
                                </div>
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
            </div>
        </div>

        <template #footer>
            <ndButton type="primary" @click="cancel">
                <img
                    class="button-icon"
                    src="@/assets/images/purchaseRequestFormView/finger.png"
                    alt=""
                />
                确 定
            </ndButton>
            <ndButton @click="close">
                <el-icon style="margin-right: 8px"><Close /></el-icon>
                取 消
            </ndButton>
        </template>
    </ndDialog>
</template>

<script setup>
// 公共组件
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndInput from "@/components/ndInput.vue";

// hooks
import { useDialog } from "../../hooks/cancel/useDialog";

import { ref } from "vue";

// emit
const emit = defineEmits(["refresh"]);

// ref
const dialogRef = ref(null);
const cancelFormRef = ref(null);

// useDialog
const { dialogData, cancelParams, cancelRules, open, close, cancel } =
    useDialog({
        dialogRef,
        cancelFormRef,
        emit,
    });

defineExpose({
    open,
    close,
});
</script>

<style lang="scss" scoped>
.view {
    height: 100%;
    width: 100%;
    padding: 12px;

    .main {
        width: 100%;
        height: 100%;
        background: #ffffff;
        border: 1px solid #eaeaea;
        border-radius: 5px;
        padding: 12px;

        .alert {
            border-radius: 5px;
            padding: 6px 8px;
            background: #fff3e5;
            display: flex;
            margin-bottom: 16px;

            .img {
                margin-right: 4px;

                img {
                    width: 14px;
                    height: 14px;
                }
            }

            .text {
                font-family: Microsoft YaHei;
                font-size: 12px;
                font-weight: normal;
                line-height: 16px;
                letter-spacing: 0px;

                color: #ea7f00;
            }
        }

        .form {
            .form-item {
                display: flex;
                align-items: center;
                width: 100%;

                .form-item-title {
                    min-width: 95px;
                    margin-right: 10px;
                    font-family: Microsoft YaHei;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 22px;
                    text-align: right;
                    letter-spacing: 0px;

                    /* Font/text-regular */
                    color: #555555;

                    span {
                        font-family: Microsoft YaHei;
                        font-size: 14px;
                        font-weight: normal;
                        line-height: 22px;
                        text-align: right;
                        letter-spacing: 0em;

                        /* Error 错误/Error6-Normal */
                        color: #d54941;
                    }
                }

                .form-item-content {
                    width: calc(100% - 105px);
                }
            }
        }
    }
}

.button-icon {
    width: 14px;
    height: 14px;
    margin-right: 9px;
}

:deep(.el-form-item__error) {
    left: 105px;
}
</style>
