<template>
  <nd-dialog ref="dialogRef" :title="title" width="60vw" height="65vh">
    <el-form
      :model="page.formData"
      :rules="rules"
      label-width="120px"
      ref="formRef"
      :disabled="isReadOnly"
      style="background: #fff; padding: 12px; margin: 12px; border-radius: 5px;border: 1px solid #EAEAEA;"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="所属地区" prop="areaName">
            <nd-input
              v-model="page.formData.areaName"
              placeholder="自动获取当前地区"
              style="width: 100%"
              readonly
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商名称" prop="name" required>
            <nd-input
              v-model="page.formData.name"
              placeholder="请输入供应商名称"
              clearable
              style="width: 100%"
              maxlength="20"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商编码" prop="code">
            <nd-input
              v-model="page.formData.code"
              placeholder="自动生成"
              clearable
              disabled
              style="width: 100%"
              readonly
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="联系人" prop="contactName">
            <nd-input
              v-model="page.formData.contactName"
              placeholder="请输入联系人"
              clearable
              style="width: 100%"
              maxlength="10"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="手机号码" prop="mobile">
            <nd-input
              v-model="page.formData.mobile"
              placeholder="请输入手机号"
              clearable
              style="width: 100%"
              maxlength="11"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="身份证号" prop="idNumber">
            <nd-input
              v-model="page.formData.idNumber"
              placeholder="请输入身份证号"
              clearable
              style="width: 100%"
              maxlength="18"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态" prop="status" required>
            <nd-radio-group
              v-model="page.formData.status"
              @change="handleStatusChange"
            >
              <nd-radio :label="1">正常</nd-radio>
              <nd-radio :label="0">禁用</nd-radio>
            </nd-radio-group>
          </el-form-item>
        </el-col>
        <!-- 新增服务地区行 -->
        <el-col :span="24">
          <el-form-item label="服务地区" prop="areaIds" required>
            <nd-tree
              ref="treeRef"
              :default-checked-keys="defaultCheckedKeys"
              :default-expanded-keys="defaultExpandedKeys"
              :data="page.treeData"
              show-checkbox
              node-key="id"
              :props="treeProps"
              @check="handleTreeCheck"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <nd-button type="primary" @click="submitForm">保存</nd-button>
      <nd-button @click="close">取消</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndTree from "@/components/ndTree.vue";

import { ref, reactive, inject, nextTick, computed } from "vue";
// 导入element-plus
import { ElMessageBox, ElMessage } from "element-plus";

// 定义axios
const $axios = inject("$axios");

const dialogRef = ref(null);

const page = reactive({
  formData: {
    areaId: "",
    name: "",
    code: "", //编码
    contactName: "",
    mobile: "",
    idNumber: "",
    status: 1,
    areaIds: [],
  },
  treeData: [],
  firstLevelNodeId: "",
  supplierId: "", // 编辑ID
  type: "", // 操作类型
});

//校验
const rules = {
  name: [{ required: true, message: "供应商名称不能为空", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  areaIds: [{ required: true, message: "请选择服务地区", trigger: "change" }],
  mobile: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const reg = /^1[3-9]\d{9}$/;
          if (!reg.test(value)) {
            callback(new Error("请输入有效的11位手机号码"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  idNumber: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const reg =  /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
          if (!reg.test(value)) {
            callback(new Error("请输入有效的身份证号码（15位或18位）"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

const emit = defineEmits(["before-close"]);

// 对话框标题计算
const title = ref("新增机构");

const modeMap = {
  add: "新增",
  edit: "编辑",
  detail: "查看",
};
const treeRef = ref(null);

// 默认选中的keys
const defaultCheckedKeys = computed(() => {
  return page.formData.areaIds || [];
});

//默认展开的keys
const defaultExpandedKeys = computed(() => {
  if (page.type === "add") {
    return [page.firstLevelNodeId];
  } else {
    return page.formData.areaIds.length > 0 ? [page.formData.areaIds[0]] : [];
  }
});

const open = async (mode = "add", row = null, params) => {
  reset();
  title.value = `${modeMap[mode]}供应商`;
  await getTreeData();

  if (mode == "add") {
    page.formData.areaId = params.areaId;
    page.formData.areaName = params.areaName;
    page.type = mode;

    // 新增时默认选中当前地区下的所有区县
    if (page.treeData.length > 0) {
      const targetNode = findNodeById(page.treeData, params.areaId);
      if (targetNode) {
        // 只获取叶子节点（区县级）
        page.formData.areaIds = getAllLeafIds(targetNode);
      }
    }
  }
  // 编辑/查看模式
  else if (mode !== "add" && row) {
    await getDetail(row.supplierId);
    page.supplierId = row.supplierId;
    page.type = mode;
  } else {
    page.supplierId = "";
  }
  isReadOnly.value = mode === "detail";
  dialogRef.value?.open(mode);
};
// 获取所有叶子节点ID（区县级）
const getAllLeafIds = (node) => {
  let ids = [];
  if (node.leaf) {
    ids.push(node.id);
  }
  // 递归处理子节点
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      ids = ids.concat(getAllLeafIds(child));
    });
  }
  return ids;
};

// 树节点选中处理
const handleTreeCheck = (currentNode, { checkedNodes }) => {
  // 过滤出所有叶子节点（区县级）
  const leafNodes = checkedNodes.filter((node) => node.leaf);
  // 只保留叶子节点的ID
  page.formData.areaIds = leafNodes.map((node) => node.id);
};

// 递归收集所有子节点id
const getAllIdsFromNode = (node) => {
  let ids = [];
  if (node.id) {
    ids.push(node.id);
  }
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      ids = ids.concat(getAllIdsFromNode(child));
    });
  }
  return ids;
};

// 递归查找节点
const findNodeById = (nodes, targetId) => {
  for (let node of nodes) {
    if (node.id === targetId) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const result = findNodeById(node.children, targetId);
      if (result) return result;
    }
  }
  return null;
};
//服务地区
const getTreeData = () => {
  return $axios({
    url: "/supplier/chooseAreaTree",
    method: "get",
    serverName: "nd-base2",
  }).then((res) => {
    if (res.data.code === 2000) {
      page.treeData = res.data.data;
      if (page.treeData.length > 0) {
        page.firstLevelNodeId = page.treeData[0].id;
      }
    }
  });
};

// 添加只读状态判断
const isReadOnly = ref(false);

// 修改树配置
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "leaf",
};

// 添加表单提交方法
const formRef = ref(null);
function checkUser() {
  let params = {
    name: page.formData.name,
    areaId: page.formData.areaId,
    contactName: page.formData.contactName,
    idNumber: page.formData.idNumber,
    mobile: page.formData.mobile,
    status: page.formData.status,
    areaIds: page.formData.areaIds,
  };
  $axios({
    url: "/supplier/checkExist",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      if (res.data.data > 0) {
        ElMessageBox.confirm("该服务地区已有供应商，确定保存？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            saveSupplier();
          })
          .catch(() => {
            console.log("取消保存");
          });
      } else {
        saveSupplier();
      }
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

//保存
function saveSupplier() {
  let params = {
    name: page.formData.name,
    areaId: page.formData.areaId,
    contactName: page.formData.contactName,
    idNumber: page.formData.idNumber,
    mobile: page.formData.mobile,
    status: page.formData.status,
    areaIds: page.formData.areaIds,
  };
  $axios({
    url: "/supplier/add",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("操作成功");
      close();
      emit("success");
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

//编辑
function updateSupplier() {
  let params = {
    supplierId: page.supplierId,
    name: page.formData.name,
    areaId: page.formData.areaId,
    contactName: page.formData.contactName,
    idNumber: page.formData.idNumber,
    mobile: page.formData.mobile,
    status: page.formData.status,
    areaIds: page.formData.areaIds,
  };
  $axios({
    url: "/supplier/update",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("操作成功");
      close();
      emit("success");
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (!valid) {
      return;
    }
    if (page.formData.areaIds.length === 0) {
      ElMessage.error("请至少选择一个服务地区");
      return;
    }
    if (page.type === "add") {
      checkUser();
    } else if (page.type === "edit") {
      updateSupplier();
    }
  });
};

//回显
const getDetail = (val) => {
  return $axios({
    url: "/supplier/details",
    method: "get",
    serverName: "nd-base2",
    data: {
      supplierId: val,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.formData.areaIds = res.data.data.areaIds;
      // page.formData.areaId = res.data.data.areaId;
      page.formData.areaName = res.data.data.areaAllPathName;
      page.formData.name = res.data.data.name;
      page.formData.code = res.data.data.code;
      page.formData.contactName = res.data.data.contactName;
      page.formData.mobile = res.data.data.mobile;
      page.formData.status = res.data.data.status;
      page.formData.idNumber = res.data.data.idNumber;
    }
  });
};

//重置
const reset = () => {
  page.formData.areaId = "";
  page.formData.areaIds = [];
  page.formData.areaName = "";
  page.formData.name = "";
  page.formData.code = "";
  page.formData.contactName = "";
  page.formData.mobile = "";
  page.formData.idNumber = "";
};
//状态
const handleStatusChange = (newVal) => {
  if (newVal === 0) {
    ElMessageBox.confirm(
      "禁用后该供应商关联的用户将无法登录平台，确定禁用？",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    ).catch(() => {
      page.formData.status = 1;
    });
  }
};

const close = () => {
  dialogRef.value.close();
  emit("before-close");
};

// 暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped></style>
