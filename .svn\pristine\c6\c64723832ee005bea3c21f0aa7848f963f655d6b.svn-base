<template>
  <ndb-page-tree-list>
    <template #tree>
      <nd-tree
        ref="treeRef"
        :data="page.treeData"
        node-key="id"
        :props="treeProps"
        :default-expanded-keys="defaultExpandedKeys"
        :current-node-key="currentNodeKey"
        :load="loadNode"
        lazy
        :highlight-current="true"
        @node-click="handleNodeClick"
      />
    </template>

    <ndb-page-list>
      <template #button>
        <nd-button @click="openAddDialog" type="primary" icon="plus"
          >新增</nd-button
        >
        <nd-button @click="handleBatchDelete" icon="delete">删除 </nd-button>
        <el-checkbox
          v-model="page.searchData.localLevel"
          style="margin-left: 20px"
          @change="handleLocalLevelChange"
          >仅看本级</el-checkbox
        >
      </template>

      <template #search>
        <nd-search-more>
          <nd-search-more-item title="供应商名称">
            <nd-input
              v-model.trim="page.searchData.nameLike"
              placeholder="请输入供应商名称"
            />
          </nd-search-more-item>

          <nd-search-more-item title="供应商编码">
            <nd-input
              v-model.trim="page.searchData.codeLike"
              placeholder="请输入编码"
            />
          </nd-search-more-item>

          <nd-search-more-item title="联系人">
            <nd-input
              v-model.trim="page.searchData.contactNameLike"
              placeholder="请输入联系人"
            />
          </nd-search-more-item>

          <nd-search-more-item title="手机号码">
            <nd-input
              v-model.trim="page.searchData.contactPhoneLike"
              placeholder="请输入手机号"
            />
          </nd-search-more-item>

          <nd-search-more-item title="身份证号">
            <nd-input
              v-model.trim="page.searchData.idNumberLike"
              placeholder="请输入身份证号"
            />
          </nd-search-more-item>

          <nd-search-more-item title="状态">
            <nd-select v-model="page.searchData.status" clearable>
              <el-option label="全部" :value="2" />
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </nd-select>
          </nd-search-more-item>

          <template #footer>
            <nd-button type="primary" @click="getTableData">查询</nd-button>
            <nd-button @click="reset">重置</nd-button>
          </template>
        </nd-search-more>
      </template>

      <template #table>
        <nd-table
          :data="page.table.data"
          border
          stripe
          @selection-change="handleSelectionChange"
          style="height: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          />
          <el-table-column
            prop="name"
            label="供应商名称"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span class="goto-detail" @click="openDetailDialog(row)">{{
                row.name
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="code" label="供应商编码" align="center" />
          <el-table-column
            prop="areaAllPathName"
            label="所属地区"
            align="center"
          />
          <el-table-column prop="contactName" label="联系人" align="center" />
          <el-table-column prop="mobile" label="手机号码" align="center" />
          <el-table-column
            prop="idNumber"
            label="身份证号"
            align="center"
            width="180"
          />
          <el-table-column prop="status" label="状态" align="center">
            <template #default="{ row }">
              <span v-if="row.status === 1">正常</span>
              <span v-else>禁用</span>
            </template>
          </el-table-column>
          <el-table-column prop="userNum" label="供应商用户" align="center">
            <template #default="{ row }">
              <span class="goto-detail" @click="openOtherPage(row)">{{
                row.userNum == 0 ? "" : row.userNum
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <div style="display: flex; gap: 8px; justify-content: center">
                <nd-button type="edit" @click="openEditDialog(row)"
                  >编辑</nd-button
                >
                <nd-button type="delete" @click="handleDelete(row.supplierId)"
                  >删除</nd-button
                >
              </div>
            </template>
          </el-table-column>
        </nd-table>
      </template>

      <template #page>
        <nd-pagination
          v-model:current-page="page.pager.page"
          v-model:page-size="page.pager.size"
          :total="page.pager.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </template>
    </ndb-page-list>
  </ndb-page-tree-list>

  <add-dialog ref="dialogRef" @before-close="getTableData"></add-dialog>
  <detail-dialog ref="detailRef" @before-close="getTableData"></detail-dialog>
</template>

<script setup>
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndbPageTreeList from "@/components/business/ndbPageTreeList/index.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTree from "@/components/ndTree.vue";
import { useRouter } from "vue-router";
const router = useRouter();

// 导入自定义组件
import addDialog from "./components/addDialog.vue";
import detailDialog from "./components/detailDialog.vue";

// 导入vue
import { reactive, ref, onMounted, inject, computed, nextTick } from "vue";
// 定义axios
const $axios = inject("$axios");

// 导入element-plus
import { ElMessageBox, ElMessage } from "element-plus";

const page = reactive({
  treeData: [],
  childrenArr: [],
  searchData: {
    areaId: "",
    nameLike: "",
    codeLike: "",
    contactNameLike: "",
    contactPhoneLike: "",
    idNumberLike: "",
    status: 2,
    localLevel: true,
    areaName: "",
  },
  table: {
    ids: [],
    selectedRows: [],
    data: [],
  },
  dict: {
    num: "",
    userTotal: "",
    count: "",
  },
  pager: {
    page: 1,
    size: 10,
    total: 10,
  },
});

onMounted(() => {
  getTreeData();
});

//仅看本级
function handleLocalLevelChange() {
  getTableData();
}

// 树形结构
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "leaf",
};

const treeRef = ref(null); // 树形结构的ref
const defaultExpandedKeys = ref([]); // 默认展开的节点
const currentNodeKey = ref("");
const firstRes = ref(true);

// 设置当前选中节点
const setCurrentNode = (key) => {
  currentNodeKey.value = key;
};

const getTreeData = () => {
  $axios({
    url: "/common/areaTree",
    method: "get",
    serverName: "nd-base2",
    params: {
      id: "",
      benji: true,
      containDept: false,
      deptType: "2",
      limitLevel: 3,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.treeData = res.data.data.map((item) => ({
        ...item,
        leaf: item.level === 3,
        children: item.level < 3 ? [] : undefined,
      }));
      if (firstRes.value) {
        page.childrenArr = res.data.data[0].children;
      }
      // 设置默认选中的第一个节点id
      if (page.treeData.length > 0) {
        const firstNodeId = page.treeData[0].id;
        setCurrentNode(firstNodeId);
        defaultExpandedKeys.value = [firstNodeId];
        // handleNodeClick(page.treeData[0]);
        page.searchData.areaId = page.treeData[0].id;
        page.searchData.areaName = page.treeData[0].name;
        getTableData();
      }
      //defaultExpandedKeys.value = page.treeData.map((item) => item.id);
    }
  });
};

// 点击树节点
const handleNodeClick = (node) => {
  setCurrentNode(node.id);
  page.searchData.areaId = node.id;
  page.searchData.areaName = node.name;
  getTableData();
};
// 加载树节点
const loadNode = (node, resolve) => {
  if (node.level === 0 || node.data.leaf) return resolve([]);
  if (firstRes.value) {
    firstRes.value = false;
    return resolve(page.childrenArr);
  }
  $axios({
    url: "/common/areaTree",
    method: "get",
    serverName: "nd-base2",
    params: {
      id: node.data.id,
      benji: false,
      containDept: false,
      deptType: "2",
      limitLevel: 3,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      const children = res.data.data.map((item) => ({
        ...item,
        leaf: item.level === 3,
        children: [],
      }));
      resolve(children);
    }
  });
};

//检查菜单
function findMenuInTree(tree, target) {
  if (tree.name === target) {
    return true;
  }
  if (tree.child && tree.child.length > 0) {
    return tree.child.some((child) => findMenuInTree(child, target));
  }
  return false;
}

// 跳转其他页面
const openOtherPage = (row) => {
  let authArr = JSON.parse(localStorage.getItem("syMenus"));
  const menu = "供应商用户管理";
  const hasMenu = authArr.some((root) => findMenuInTree(root, menu));
  if (hasMenu) {
    router.push({
      path: "/supplierUserManagementView",
      query: { supplierId: row.supplierId },
    });
  } else {
    ElMessage.info("暂无菜单权限");
  }
};

// 查询
const getTableData = () => {
  let params = {
    page: page.pager.page,
    size: page.pager.size,
    areaId: page.searchData.areaId,
    nameLike: page.searchData.nameLike,
    codeLike: page.searchData.codeLike,
    contactNameLike: page.searchData.contactNameLike,
    contactPhoneLike: page.searchData.contactPhoneLike,
    idNumberLike: page.searchData.idNumberLike,
    status: page.searchData.status === 2 ? "" : page.searchData.status,
    localLevel: page.searchData.localLevel,
  };
  $axios({
    url: "/supplier/page",
    method: "get",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.table.data = res.data.data.records;
      page.pager.total = res.data.data.total;
      page.pager.page = res.data.data.current;
      page.pager.size = res.data.data.size;
    } else {
      ElMessage.error(res.data.message);
    }
  });
};

// 重置
const reset = () => {
  page.searchData.nameLike = "";
  page.searchData.codeLike = "";
  page.searchData.contactNameLike = "";
  page.searchData.contactPhoneLike = "";
  page.searchData.idNumberLike = "";
  page.searchData.status = 2;
  page.searchData.localLevel = true;
  page.searchData.localLevel = true;
  getTableData();
};

// 修改选中事件处理
const handleSelectionChange = (rows) => {
  page.table.selectedRows = rows;
  page.table.ids = rows.map((item) => item.supplierId);
  console.log("选中的行：", page.table.ids);
};
// 检验关联用户批量删除
function getBatchUser() {
  let params = Object.values(page.table.ids);
  $axios({
    url: "/supplier/checkDeleteBatch",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.dict.num = res.data.data;
      page.dict.userTotal = page.table.ids.length;
      if (page.dict.num > 0 && page.dict.num !== page.dict.userTotal) {
        ElMessageBox.confirm(
          `勾选的${page.dict.userTotal}条数据中有${
            page.dict.num
          }条数据已创建用户或关联商品，确定将剩下的${
            page.dict.userTotal - page.dict.num
          }条删除？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            confirmButtonClass: "ExitConfirmButton",
            cancelButtonClass: "ExitCancelButton",
          }
        )
          .then(() => {
            batchDelete();
          })
          .catch(() => {});
      } else if (page.dict.num === page.dict.userTotal) {
        ElMessage.error(
          `勾选的${page.dict.num}条数据均已创建用户或关联商品，无法直接删除！`
        );
      } else if (page.dict.num === 0) {
        ElMessageBox.confirm(
          `确定将勾选的${page.dict.userTotal}条数据全部删除？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            confirmButtonClass: "ExitConfirmButton",
            cancelButtonClass: "ExitCancelButton",
            customClass: "ExitCustomClass",
          }
        )
          .then(() => {
            batchDelete();
          })
          .catch(() => {});
      }
    } else {
      console.log(res.data.message);
    }
  });
}

// 批量删除方法判断条件
const handleBatchDelete = async () => {
  if (page.table.ids.length === 0) {
    ElMessage.error("请先勾选记录");
    return;
  }
  getBatchUser();
};

// 批量删除方法
const batchDelete = () => {
  let params = Object.values(page.table.ids);
  $axios({
    url: "/supplier/deleteBatch",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("删除成功");
      getTableData();
    }
  });
};

// 添加删除方法
const handleDelete = (val) => {
  ElMessageBox.confirm("确定删除该供应商？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    confirmButtonClass: "ExitConfirmButton",
    cancelButtonClass: "ExitCancelButton",
    customClass: "ExitCustomClass",
  })
    .then(() => {
      $axios({
        url: "/supplier/delete?supplierId=" + val,
        method: "post",
        serverName: "nd-base2",
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success("删除成功");
          getTableData();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    })
    .catch(() => {
      console.log("取消删除");
    });
};

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

const dialogRef = ref(null);
const detailRef = ref(null);

// 打开新增对话框
const openAddDialog = () => {
  dialogRef.value?.open("add", "", page.searchData);
};

// 打开编辑对话框
const openEditDialog = (row) => {
  dialogRef.value?.open("edit", row);
};

// 打开详情对话框
const openDetailDialog = (row) => {
  detailRef.value?.open("detail", row);
};
</script>

<style lang="scss" scoped>
.goto-detail {
  cursor: pointer;
  color: #0b8df1;
}
:deep(.nd-table-box .el-table .el-table__cell) {
  padding: 0;
  font-size: 14px;
}
:deep(.nd-table-box .el-table .cell) {
    .table {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<style lang="scss">
.ExitConfirmButton {
  background: #068324 !important;
  border-color: #068324 !important;

  &:hover {
    opacity: 0.8;
  }
}

.ExitCancelButton {
  // background: #068324 !important;
  // border-color: #068324 !important;

  &:hover {
    background-color: rgba(133, 224, 154, 0.2) !important;
    color: #38864a !important;
    border-color: #38864a !important;
  }
}
.ExitCustomClass {
  .el-message-box__headerbtn:hover {
    .el-message-box__close {
      color: #068324;
    }
  }
}
</style>
