export default {
  props: {
    rowSpanName: {
      type: String,
      default: "",
    },
    rowSpanNames: {
      type: Array,
      default: () => [],
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageIndex: {
      type: Number,
      default: 1,
    }
  },
  data() {
    return {
      spanIndexArray: [],
      rowSpanIndex: 1,
      orderIndex: -1,
    };
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler(value) {
        this.rowSpanIndex = 1 + (this.pageIndex - 1) * this.pageSize;
        this.spanIndexArray = [];
        // 获取相同编号的数组
        this.getOrderNumber(value);
      },
    },
  },
  methods: {
    // 第一步：获取相同编号的数组
    getOrderNumber(value) {
      if (this.rowSpanName !== "") {
        var orderObj = {}
        value.forEach(function (element, index) {
          element.rowIndexForRowSpan = index;
          element.rowSpanIndex = this.rowSpanIndex;
          if (orderObj[element[this.rowSpanName]]) {
            orderObj[element[this.rowSpanName]].push(index);
          } else {
            orderObj[element[this.rowSpanName]] = []
            orderObj[element[this.rowSpanName]].push(index);
            this.rowSpanIndex = this.rowSpanIndex + 1;
          }
        }, this);
        // 用于合并行
        for (var k in orderObj) {
          var length = orderObj[k].length;
          orderObj[k].map((item, index) => {
            if (index === 0) {
              this.spanIndexArray[item] = length;
            } else {
              this.spanIndexArray[item] = 0;
            }
          });
        }
        // 用于样式
        let order = 1;
        for (let i = 0; i < value.length; i++) {
          if (i === 0) {
            value[i].rowSpanOrder = order;
          } else {
            if (value[i][this.rowSpanName] === value[i - 1][this.rowSpanName]) {
              value[i].rowSpanOrder = order;
            } else {
              value[i].rowSpanOrder = order + 1;
              order = order + 1;
            }
          }
        }
      } else {
        // 普通表格勾选时的行样式
        let order = 1;
        for (let i = 0; i < value.length; i++) {
          value[i].rowSpanOrder = order;
          order = order + 1;
        }
      }
    },
    // 第二步：合并行
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.rowSpanName !== "") {
        if (this.rowSpanNames.indexOf(column.property) > -1 || (column.type === "selection" && this.selectionSpan)) {
          const row = this.spanIndexArray[rowIndex];
          const col = row > 0 ? 1 : 0;
          return {
            rowspan: row,
            colspan: col
          }
        }
      }
    },
    // 自定义行样式
    tableRowClassName({ row, rowIndex }) {
      if (this.rowSpanName !== "") {
        if (this.$refs.table && this.$refs.table.selection && this.$refs.table.selection.length > 0) {
          if (this.orderIndex === row.rowSpanOrder) {
            for (let i = 0; i < this.$refs.table.selection.length; i++) {
              if (this.$refs.table.selection[i].rowSpanOrder === row.rowSpanOrder) {
                return "select-row";
              }
            }
            return "hover-row";
          } else {
            for (let i = 0; i < this.$refs.table.selection.length; i++) {
              if (this.$refs.table.selection[i].rowSpanOrder === row.rowSpanOrder) {
                return "select-row";
              }
            }
          }
        } else {
          if (this.orderIndex === row.rowSpanOrder) {
            return "hover-row";
          }
        }
      } else {
        var i = 0;
        if (this.$refs.table && this.$refs.table.selection) {
          for (i = 0; i < this.$refs.table.selection.length; i++) {
            if ((this.$refs.table.selection[i].rowSpanOrder === row.rowSpanOrder && row.rowSpanOrder) || (this.$refs.table.selection[i].id === row.id && row.id)) {
              return "select-row";
            }
          }
        }
      }
    },
    // 单元格鼠标进入
    cellMouseEnter(row, column, cell, event) {
      if (this.rowSpanName !== "") {
        this.data.forEach((item) => {
          if (row.rowSpanOrder === item.rowSpanOrder) {
            this.orderIndex = row.rowSpanOrder;
          }
        });
      }
    },
    // 单元格鼠标移出
    cellMouseLeave(row, column, cell, event) {
      if (this.rowSpanName !== "") {
        this.orderIndex = -1;
      }
    }
  },
};
