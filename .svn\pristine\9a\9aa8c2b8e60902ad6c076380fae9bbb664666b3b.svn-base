<template>
  <div class="ndb-page-list-box">
    <div class="search-box">
      <div class="search-box-left">
        <slot name="tag"></slot>
        <nd-button v-if="props.isSearch" type="search" :textSpace="false" @click="showSearchMore()"> 搜索 </nd-button>
        <slot name="after-tag"></slot>
      </div>
      <div class="search-box-right">
        <slot name="button"></slot>
      </div>
    </div>
    <div class="search-more-box" v-show="page.showSearchMore">
      <slot name="search"></slot>
    </div>
    <div :class="page.workhourSlotContentNone ? '' : 'workhour-box'">
      <slot name="workhour"></slot>
    </div>
    <div class="content-box">
      <slot name="table"></slot>
    </div>
    <div :class="props.isFooter ? 'footer-box' : ''">
      <slot name="page"></slot>
    </div>
  </div>
</template>

<script setup>
import ndButton from "@/components/ndButton.vue";
import { reactive, useSlots, onMounted } from "vue";

const props = defineProps({
  // 是否展示搜索
  isSearch: {
    type: Boolean,
    default: true,
  },
  // 是否展示底部盒子样式
  isFooter: {
    type: Boolean,
    default: true,
  },
});

// 页面
var page = reactive({
  showSearchMore: false, // 是否显示更多查询条件
  workhourSlotContentNone: true, // 统计插槽是否有内容
});

function showSearchMore() {
  page.showSearchMore = !page.showSearchMore;
}

// 插槽信息
let slots = useSlots();
if (slots.workhour) {
  page.workhourSlotContentNone = false;
} else {
  page.workhourSlotContentNone = true;
}
</script>

<style lang="scss" scoped>
.ndb-page-list-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-box {
    height: auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .search-box-left {
      display: flex;
    }

    .search-box-right {
      display: flex;
    }
  }

  .search-more-box {
    margin-bottom: 10px;
  }

  .workhour-box {
    width: 100%;
    height: auto;
    padding: 10px 10px 7px;
    margin-bottom: 10px;
    background: #ffffff;
    border-radius: 8px;
  }

  .content-box {
    height: 0px;
    flex: 1;

    // .tag {
    //   display: inline-block;
    //   width: 100%;
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    //   white-space: nowrap;
    // }

    // .operation {
    //   display: flex;
    //   justify-content: space-around;
    //   align-items: center;
    // }
  }

  .footer-box {
    height: 40px;
    padding-right: 10px;
    border-left: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
  }
}
</style>
