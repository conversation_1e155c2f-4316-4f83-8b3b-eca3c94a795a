<template>
  <nd-dialog ref="dialogRef" width="50vw" title="支出结算" align-center>
    <el-form
      ref="formRef"
      :model="page.form"
      :rules="rules"
      class="add-box"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="费用项" prop="type" required>
            <div
              style="display: flex; width: 100%; gap: 30px; text-align: center"
            >
              <div
                v-for="(item, index) in page.feeList"
                :key="index"
                :class="getFeeItemClass(item)"
                @click="selectType(item.value)"
              >
                <div class="fee-title">{{ item.title }}</div>
                <div class="fee-desc">{{ item.desc }}</div>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额" prop="amount">
            <div style="display: flex; width: 100%">
              <nd-input
                v-model.trim="page.form.amount"
                :maxlength="20"
                width="100%"
                readonly
                style="border: none"
              />
              <!-- <el-tooltip
                content="订单总金额，即买家(商户)应与平台结算金额"
                placement="top"
              >
                <el-icon style="margin-right: 10px; cursor: pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip> -->
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结算时间" prop="checkoutTime" required>
            <ndDatePicker
              v-model="page.form.checkoutTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择"
              width="100%"
              @change="checkoutTimeChange(page.form.checkoutTime)"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <nd-input
              v-model="page.form.remark"
              :maxlength="200"
              placeholder="请输入备注"
              show-word-limit
              width="100%"
              :rows="6"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <nd-button icon="Back" @click="close">取&nbsp;消</nd-button>
      <nd-button type="primary" icon="Pointer" @click="submitForm"
        >确&nbsp;定</nd-button
      >
    </template>
  </nd-dialog>
</template>

<script setup>
// 组件导入
import ndInput from "@/components/ndInput.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import { ref, reactive, inject } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");

// 定义
const page = reactive({
  form: {
    opId: "",
    orderId: "",
    amount: "",
    checkoutTime: "",
    remark: "",
    type: 2,
  },
  feeList: [
    {
      title: "养殖户费用",
      desc: "平台支付给卖家(养殖户)的费用",
      value: 2,
    },
    {
      title: "交易服务费",
      desc: "平台交易过程中产生的服务费用",
      value: 3,
    },
  ],
});

const dialogRef = ref(null);
const farmerDisabled = ref(false); // 养殖户禁用状态
const serviceDisabled = ref(false); // 服务费禁用状态

const getFeeItemClass = (item) => {
  return [
    "fee-item",
    { selected: page.form.type === item.value },
    { disabled: isDisabled(item.value) },
  ];
};

const isDisabled = (value) => {
  return (
    (value === 2 && farmerDisabled.value) ||
    (value === 3 && serviceDisabled.value)
  );
};

const open = (row, flag, yzFlag) => {
  reset();
  if (flag) {
    console.log("卖家费用");
    farmerDisabled.value = true;
    page.form.type = 3;
  } else if (yzFlag) {
    console.log("养殖户费用");
    serviceDisabled.value = true;
    page.form.type = 2;
  } else {
    page.form.type = 2;
  }
  page.form.opId = row.opId;
  page.form.orderId = row.orderId;
  setCurrentTime();
  if (page.form.checkoutTime) {
    getData();
  }
  dialogRef.value?.open();
};

// 新增表单引用
const formRef = ref(null);

const emit = defineEmits(["before-close"]);

//类型
const selectType = (value) => {
  page.form.type = value;
  page.form.amount = "";
  getData();
};

//结算时间更改
function checkoutTimeChange(val) {
  if (val) {
    getData();
  }
}

//校验规则
const rules = {
  checkoutTime: [
    { required: true, message: "结算时间不能为空", trigger: "blur" },
  ],
};

//获取当前时间
const setCurrentTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  page.form.checkoutTime = formattedTime;
};

//获取货款
function getData() {
  let params = {
    opId: page.form.opId,
    orderId: page.form.orderId,
    amount: page.form.amount,
    checkoutTime: page.form.checkoutTime,
    remark: page.form.remark,
    type: page.form.type,
  };
  $axios({
    url: "/orderBill/preCheckout",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      page.form.amount = res.data.data.amount;
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      let params = {
        opId: page.form.opId,
        orderId: page.form.orderId,
        amount: page.form.amount,
        checkoutTime: page.form.checkoutTime,
        remark: page.form.remark,
        type: page.form.type,
      };
      $axios({
        url: "/orderBill/checkout",
        method: "post",
        serverName: "nd-base2",
        data: params,
      }).then((res) => {
        if (res.data.code === 2000) {
          ElMessage.success(res.data.message);
          emit("before-close");
          close();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    }
  });
};

//重置
const reset = () => {
  page.form.opId = "";
  page.form.orderId = "";
  page.form.amount = "";
  page.form.checkoutTime = "";
  page.form.remark = "";
  page.form.type = 2;
  farmerDisabled.value = false;
  serviceDisabled.value = false;
};

//状态
const close = () => {
  dialogRef.value.close();
  emit("before-close");
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.add-box {
  padding: 12px;
  background-color: #fff;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;

  .el-form-item {
    margin-bottom: 16px;

    :deep(.el-form-item__label) {
      color: #606266;
      font-weight: 500;
    }
  }
}
.fee-item {
  flex: 1;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #a0d911;
  }

  &.selected {
    border: 1px solid #b3e09c;
    background: #f0f9eb;
  }

  .fee-title {
    font-weight: bold;
    color: #000;
  }

  .fee-desc {
    color: #666;
    font-size: 14px;
  }
}
.fee-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
  pointer-events: none;
  &:hover {
    border-color: #e0e0e0;
  }
}
</style>
