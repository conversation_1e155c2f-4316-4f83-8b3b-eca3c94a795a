import { reactive } from "vue";
import { FileData } from "./useFile";

/**
 * 
 * @param {Object} param0
 * @param {Object} param0.fileData
 * @param {FileData} param0.fileData.files 
 * @returns 
 */
export function useDialog({
    dialogRef = null,
    fileData = null
}) {

    const dialogData = reactive({
        title: "附件详情",
    })


    /**
     * open
     * 
     * @return {void}
     */
    const open = ({ files }) => {
        fileData.files = files ?? [];
        dialogRef && dialogRef.value.open();
    }

    /**
     * close
     * 
     * @return {void}
     */
    const close = () => {
        dialogRef && dialogRef.value.close()
    }

    return {
        dialogData,
        open,
        close
    }
}