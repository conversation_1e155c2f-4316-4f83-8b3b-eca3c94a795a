<template>
  <div class="menu-second-box">
    <div class="system-name">{{ page.title }}</div>
    <div class="menu">
      <el-menu class="el-menu-demo" :default-active="menu.defaultActive" mode="horizontal" @select="handleSelect" popper-class="menu-second-popper" active-text-color="#068324">
        <template v-for="item in menu.data">
          <!-- 1级有孩子 -->
          <template v-if="item.child && item.child.length > 0">
            <el-sub-menu :index="item.id">
              <template #title>{{ item.name }}</template>
              <template v-for="item2 in item.child">
                <!-- 2级有孩子 -->
                <template v-if="item2.child && item2.child.length > 0">
                  <el-sub-menu :index="item2.id">
                    <template #title>{{ item2.name }}</template>
                    <template v-for="item3 in item2.child">
                      <el-menu-item :index="item3.id">{{ item3.name }}</el-menu-item>
                    </template>
                  </el-sub-menu>
                </template>
                <!-- 2级没有孩子 -->
                <template v-else>
                  <el-menu-item :index="item2.id">{{ item2.name }}</el-menu-item>
                </template>
              </template>
            </el-sub-menu>
          </template>
          <!-- 1级没有孩子 -->
          <template v-else>
            <el-menu-item :index="item.id">{{ item.name }}</el-menu-item>
          </template>
        </template>
      </el-menu>
    </div>
    <div class="system-modify-out">
      <div class="modify" @click="openChangePassword">
        <el-icon><Unlock /></el-icon>修改密码
      </div>
      <div class="out" @click="logout">
        <el-icon><SwitchButton /></el-icon>退出
      </div>
    </div>
    <!-- <el-menu :default-active="activeIndex2" class="el-menu-demo" mode="horizontal" background-color="#545c64" text-color="#fff" active-text-color="#ffd04b" @select="handleSelect">
      <el-menu-item index="1">Processing Center</el-menu-item>
      <el-sub-menu index="2">
        <template #title>Workspace</template>
        <el-menu-item index="2-1">item one</el-menu-item>
        <el-menu-item index="2-2">item two</el-menu-item>
        <el-menu-item index="2-3">item three</el-menu-item>
        <el-sub-menu index="2-4">
          <template #title>item four</template>
          <el-menu-item index="2-4-1">item one</el-menu-item>
          <el-menu-item index="2-4-2">item two</el-menu-item>
          <el-menu-item index="2-4-3">item three</el-menu-item>
        </el-sub-menu>
      </el-sub-menu>
      <el-menu-item index="3" disabled>Info</el-menu-item>
      <el-menu-item index="4">Orders</el-menu-item>
    </el-menu> -->
  </div>
  <change-password ref="changePasswordRef"></change-password>
</template>

<script setup>
// 导入vue
import { ref, inject, onMounted, reactive, watch, getTransitionRawChildren } from "vue";
import { useRoute, useRouter } from "vue-router";

// 导入子组件
import changePassword from "./changePassword.vue";

// 导入element-plus
import { ElMessageBox, ElMessageBox as elMessageBox } from "element-plus";
import { ElMessage as elMessage } from "element-plus";

// 路由
const $route = useRoute();
const $router = useRouter();
const $axios = inject("$axios");

// emit
const emit = defineEmits(["menu-click"]);

// page
const page = reactive({
  title: "",
});

// ref
const changePasswordRef = ref(null);

// watch
watch(
  () => $router.currentRoute.value.fullPath,
  (newVal, oldVal) => {
    var id = localStorage.getItem("syJumpMenuId");
    // 获得标题
    getTitle(id);
    // 设置菜单数据
    setMenuData(id);
    // 高亮菜单
    highlightMenu();
    // 设置标题
    setTitle(menu.defaultActive);
  },
  {
    immediate: false,
  }
);

// 展开时使用的菜单对象
var menu = reactive({
  data: [], // 所有的菜单数据
  defaultActive: "", // 高亮的菜单
});

// 初始化
function init(id) {
  // 获得标题
  getTitle(id);
  // 设置菜单数据
  setMenuData(id);
  // 高亮菜单
  highlightMenu();
  // 模拟点击高亮菜单
  clickHighlightMenu();
  // 设置标题
  setTitle(menu.defaultActive);
}

// 获得标题
function getTitle(id) {
  var menuData = JSON.parse(localStorage.getItem("syMenus"));
  var subMenuData = menuData.find((item) => {
    return item.id === id;
  });
  page.title = subMenuData.name;
}

// 设置菜单数据
function setMenuData(id) {
  var menuData = JSON.parse(localStorage.getItem("syMenus"));
  var subMenuData = menuData.find((item) => {
    return item.id === id;
  });
  menu.data = subMenuData.child;
}

// 高亮菜单
function highlightMenu() {
  // 去掉路由后面的参数
  var routerWithoutQuery = $router.currentRoute.value.fullPath.split("?")[0];

  var node = treeFind(menu.data, (item) => {
    return "/" + item.url === routerWithoutQuery;
  });
  if (node) {
    menu.defaultActive = node.id;
  } else {
    if (menu.data[0].child.length > 0) {
      menu.defaultActive = menu.data[0].child[0].id;
    } else {
      menu.defaultActive = menu.data[0].id;
    }
  }
}

// 模拟点击高亮菜单
function clickHighlightMenu() {
  emit("menu-click", { key: menu.defaultActive });
}

// 菜单点击
function handleSelect(key, keyPath) {
  setTitle(key);
  emit("menu-click", { key: key });
}

// 设置标题
function setTitle(params) {
  var menuData = JSON.parse(localStorage.getItem("syMenus"));
  var node = treeFind(menuData, (item) => {
    return item.id === params;
  });
  page.title = node.name;
}

// 查找树节点
function treeFind(tree, func) {
  for (const data of tree) {
    if (func(data)) return data;
    if (data.child && data.child.length > 0) {
      const res = treeFind(data.child, func);
      if (res) return res;
    }
  }
  return null;
}

// 退出系统
function logout() {
  elMessageBox
    .confirm("确定退出系统?", "退出系统", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: "ExitConfirmButton",
      cancelButtonClass: "ExitCancelButton",
      customClass: "ExitCustomClass",
    })
    .then(() => {
      $axios({
        url: "/index/logout",
        method: "post",
        serverName: "nd-base2",
        data: {},
      }).then((res) => {
        if (res.data.code === 2000) {
          localStorage.setItem("syToken", "");
          localStorage.setItem("syUserName", "");
          localStorage.setItem("syDeptName", "");
          localStorage.setItem("syMenus", "");
          $router.push("loginView");
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    })
    .catch(() => {});
}

// 打开修改密码对话框
const openChangePassword = () => {
  changePasswordRef.value.open();
};

defineExpose({
  init,
});
</script>

<style lang="scss" scoped>
.menu-second-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;

  .system-name {
    width: auto;
    height: 30px;
    font-size: 22px;
    font-weight: bolder;
    letter-spacing: 0.1em;
    color: #068324;
    margin-right: 15px;
    cursor: pointer;
  }

  .menu {
    width: 0px;
    height: 100%;
    flex: 1;
  }

  :deep(.el-menu--horizontal) {
    background-color: #ffffff;
    height: 100%;
  }

  :deep(.el-menu-item.is-active) {
    color: #068324;
    background-color: #f4f5f7;
    border-top: 3px solid #ffffff;
    // border-bottom: 3px solid #0b8df1;
    border-bottom: 3px solid transparent;
    border-top: 3px solid #0b8df1;
  }

  :deep(.el-menu--horizontal .el-menu-item:not(.is-disabled):hover) {
    background-color: #ffffff;
  }

  :deep(.el-menu--horizontal .el-menu-item:not(.is-disabled):focus) {
    color: #303133;
    background-color: #ffffff;
  }

  :deep(.el-sub-menu .el-sub-menu__title:hover) {
    color: #068324;
    background-color: #ffffff;
    // border-bottom: 3px solid #0b8df1 !important;
    // border-bottom: 3px solid #0b8df1;
    // border-bottom: 3px solid transparent;
    // border-top: 3px solid #0b8df1;
  }

  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    color: #068324 !important;
    // border-bottom: 3px solid #0b8df1;
    border-bottom: 3px solid transparent;
    border-top: 3px solid #068324;
    background-color: #f4f5f7;
  }
  :deep(.el-menu-item.is-active) {
    color: #068324 !important;
    border-top: 3px solid #068324;
  }

  :deep(.el-sub-menu .el-sub-menu__icon-arrow) {
    margin-right: -16px;
  }

  :deep(.el-sub-menu__title) {
    padding-left: 10px;
    padding-right: 20px;
  }

  :deep(.el-menu--horizontal.el-menu) {
    border-bottom: inherit;
  }

  :deep(.el-menu-item) {
    padding-left: 10px;
    padding-right: 10px;
    // color: #ffffff;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
  }
}
:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  border-bottom-color: transparent !important;
}
:deep(.el-menu--horizontal .el-menu-item:not(.is-disabled):hover) {
  color: #068324;
}
</style>
<style lang="scss">
.menu-second-popper {
  .el-menu--horizontal > .el-menu-item.is-active {
    color: #068324 !important;
  }
  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    border-bottom-color: transparent !important;
  }
  .el-menu--horizontal .el-menu--popup .el-menu-item {
    &:hover {
      background-color: #f7f7f7 !important;
    }
  }
}
.el-menu--popup {
  --el-menu-hover-bg-color: #f7f7f7; /* 影响所有层级 */
  --el-menu-hover-text-color: #068324; /* 悬停文字颜色变量 */
}

.system-modify-out {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: normal;

  color: #555555;
  .modify {
    margin-right: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .out {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .el-icon {
    margin-right: 4px;
    color: #068324;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.ExitConfirmButton {
  background: #068324 !important;
  border-color: #068324 !important;

  &:hover {
    opacity: 0.8;
  }
}

.ExitCancelButton {
  &:hover {
    background-color: rgba(133, 224, 154, 0.2) !important;
    color: #38864a !important;
    border-color: #38864a !important;
  }
}

.ExitCustomClass {
  .el-message-box__headerbtn:hover {
    .el-message-box__close {
      color: #068324;
    }
  }
}
</style>
