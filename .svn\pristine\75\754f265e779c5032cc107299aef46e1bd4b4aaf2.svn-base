import { onMounted, reactive, watch } from "vue";
import axios from "@/http/index";
import { ElMessage } from "element-plus";

export function useEncryptionDecryption({ props = null }) {
    const encryptionDecryptionData = reactive({
        isDecrypt: false,
        ciphertext: "",
        plaintext: "",
        key: "",
    })

    /**
     * 解密数据接口
     * 
     * @returns {String}
     */
    const decryptData = async () => {
        const data = {
            content: encryptionDecryptionData.key
        }
        return axios({
            url: "/common/getPlaintext",
            method: "POST",
            serverName: "nd-base2",
            data,
        }).then(r => {
            if (r.data.code !== 2000) {
                ElMessage.error(r.data.message);
                return "";
            }
            return r.data.data;
        })
    }

    /**
     * 点击显示明文
     * 
     * @returns {void}
     */
    const showPlaintext = async () => {
        if (!encryptionDecryptionData.plaintext) encryptionDecryptionData.plaintext = await decryptData();
        encryptionDecryptionData.isDecrypt = true;
    }

    /**
     * 点击显示密文
     * 
     * @returns {void}
     */
    const showCiphertext = async () => {
        encryptionDecryptionData.isDecrypt = false;
    }

    onMounted(() => {
        // console.log(props);
    })

    watch(
        () => props,
        () => {
            encryptionDecryptionData.ciphertext = props?.ciphertext ?? "";
            encryptionDecryptionData.key = props?.keyText ?? "";
        },
        { immediate: true, deep: true })

    return {
        encryptionDecryptionData,
        showPlaintext,
        showCiphertext
    }
}