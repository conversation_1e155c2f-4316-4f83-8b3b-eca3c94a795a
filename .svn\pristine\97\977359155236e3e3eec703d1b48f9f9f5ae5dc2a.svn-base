<template>
  <div class="nd-drawer-box">
    <el-drawer :title="title" v-bind="$attrs">
      <slot></slot>
    </el-drawer>
  </div>
</template>
<script setup>
import { ref, watch, onMounted } from "vue";

// 定义属性
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: "",
  },
  // // 只读
  // disabled: {
  //   type: Boolean,
  //   default: false,
  // },
  // // 权限key
  // authKey: {
  //   type: String,
  //   default: "",
  // },
});

// // 系统级别disabled
// var _disabled = ref(false);

// // 监视disabled
// watch(
//   () => props.disabled,
//   (newValue, oldValue) => {
//     _disabled.value = newValue;
//   },
//   {
//     immediate: true,
//   }
// );

onMounted(() => {
  // if (localStorage.getItem("syAuths")) {
  //   var auth = localStorage.getItem("syAuths").split(",");
  //   auth.map((item) => {
  //     if (props.authKey === item) {
  //       _disabled.value = true;
  //     }
  //   });
  // }
});
</script>
<style lang="scss" scoped>
.nd-drawer-box {
  width: auto;
  height: auto;
  
  :deep(.el-drawer__header) {
    margin-bottom: 0px;
    padding: 20px;
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
    color: #0b8df1;
    border-bottom: 1px solid #dcdfe6;
  }

  :deep(.el-drawer__close-btn) {
    color: #606266;
  }

  :deep(.el-drawer__body) {
    padding: 0px;
    overflow: hidden;
  }
}
</style>
