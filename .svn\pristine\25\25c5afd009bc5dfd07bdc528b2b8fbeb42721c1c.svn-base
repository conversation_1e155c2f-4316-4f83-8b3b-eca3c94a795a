<template>
  <nd-dialog ref="dialogRef" width="50vw" :title="title" align-center>
    <el-form
      ref="formRef"
      :model="page.form"
      :rules="rules"
      class="add-box"
      label-width="120px"
      v-if="page.type != 'detail'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="运营机构名称" prop="name" required>
            <nd-input
              v-model.trim="page.form.name"
              placeholder="请输入机构名称"
              :maxlength="20"
              width="100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运营机构编码" prop="code">
            <nd-input
              v-model="page.form.code"
              placeholder="自动生成"
              disabled
              width="100%"
              readonly
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="联系人" prop="contactName">
            <nd-input
              v-model.trim="page.form.contactName"
              :maxlength="10"
              placeholder="请输入联系人"
              width="100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="mobile">
            <nd-input
              v-model.number="page.form.mobile"
              :maxlength="11"
              placeholder="请输入手机号码"
              width="100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="身份证号" prop="idNumber">
            <nd-input
              v-model="page.form.idNumber"
              :maxlength="18"
              placeholder="请输入身份证号"
              width="100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status" required>
            <nd-radio-group
              v-model="page.form.status"
              @change="handleStatusChange"
            >
              <nd-radio :label="1">正常</nd-radio>
              <nd-radio :label="0">禁用</nd-radio>
            </nd-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="form-container" v-else>
      <div class="form-row">
        <div class="form-item">
          <div class="form-label">运营机构名称</div>
          <div class="form-content">{{ page.form.name }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">运营机构编码</div>
          <div class="form-content">{{ page.form.code }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">联系人</div>
          <div class="form-content">{{ page.form.contactName }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">身份证号</div>
          <div class="form-content">{{ page.form.idNumber }}</div>
        </div>
        <div class="form-item">
          <div class="form-label">手机号码</div>
          <div class="form-content">{{ page.form.mobile }}</div>
        </div>
        <div class="form-item">
          <div class="form-label">状态</div>
          <div class="form-content" v-if="page.form.status == 1">正常</div>
          <div class="form-content" v-if="page.form.status == 0">禁用</div>
        </div>
      </div>
    </div>
    <template #footer>
      <nd-button
        v-if="page.type != 'detail'"
        type="primary"
        icon="FolderChecked"
        @click="submitForm"
        >保&nbsp;存</nd-button
      >
      <nd-button
        v-if="page.type != 'detail'"
        type=""
        icon="Close"
        @click="close"
        >取&nbsp;消</nd-button
      >
      <nd-button
        v-if="page.type == 'detail'"
        type=""
        icon="Close"
        @click="close"
        >关&nbsp;闭</nd-button
      >
    </template>
  </nd-dialog>
</template>

<script setup>
// 新增组件导入
import ndInput from "@/components/ndInput.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import { ref, reactive, inject } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");

// 定义
const page = reactive({
  form: {
    code: "",
    name: "",
    contactName: "",
    mobile: "",
    status: 1,
    idNumber: "",
  },
  type: "", // 操作类型
  operateId: "", // 操作id
});

// 对话框标题计算
const title = ref("新增机构");

const dialogRef = ref(null);

const modeMap = {
  add: "新增",
  edit: "编辑",
  detail: "查看",
};

const open = (mode = "add", row = null) => {
  reset();
  title.value = `${modeMap[mode]}运营机构`;
  page.type = mode;
  page.code = "";
  if (mode !== "add" && row) {
    getDetail(row.supplierId);
    page.operateId = row.supplierId;
    page.type = mode;
  } else {
    page.operateId = "";
  }
  isReadOnly.value = mode === "detail";
  dialogRef.value?.open(mode);
};

// 添加只读状态判断
const isReadOnly = ref(false);

// 新增表单引用
const formRef = ref(null);

const emit = defineEmits(["before-close"]);

//校验规则
const rules = {
  name: [{ required: true, message: "供应商名称不能为空", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  mobile: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const reg = /^1[3-9]\d{9}$/;
          if (!reg.test(value)) {
            callback(new Error("请输入有效的11位手机号码"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  idNumber: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const reg =  /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
          if (!reg.test(value)) {
            callback(new Error("请输入有效的身份证号码（15位或18位）"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

//保存
function saveSupplier() {
  let params = {
    name: page.form.name,
    contactName: page.form.contactName,
    mobile: page.form.mobile,
    idNumber: page.form.idNumber,
    status: page.form.status,
  };
  $axios({
    url: "/operate/add",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("操作成功");
      close();
      emit("success");
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

//编辑
function updateSupplier() {
  let params = {
    operateId: page.operateId,
    name: page.form.name,
    contactName: page.form.contactName,
    mobile: page.form.mobile,
    idNumber: page.form.idNumber,
    status: page.form.status,
  };
  console.log(params, "00000");
  $axios({
    url: "/operate/update",
    method: "post",
    serverName: "nd-base2",
    data: params,
  }).then((res) => {
    if (res.data.code === 2000) {
      ElMessage.success("操作成功");
      close();
      emit("success");
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (!valid) {
      return;
    } else if (page.type === "add") {
      saveSupplier();
    } else if (page.type === "edit") {
      updateSupplier();
    }
  });
};

//回显
const getDetail = (val) => {
  return $axios({
    url: "/operate/details",
    method: "get",
    serverName: "nd-base2",
    data: {
      operateId: val,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.form.name = res.data.data.name;
      page.form.code = res.data.data.code;
      page.form.contactName = res.data.data.contactName;
      page.form.mobile = res.data.data.mobile;
      page.form.status = res.data.data.status;
      page.form.idNumber = res.data.data.idNumber;
    }
  });
};

//重置
const reset = () => {
  page.form.name = "";
  page.form.contactName = "";
  page.form.mobile = "";
  page.form.status = 1;
  page.form.idNumber = "";
  page.form.code = "";
};
//状态
const handleStatusChange = (newVal) => {
  if (newVal === 0) {
    ElMessageBox.confirm(
      "禁用后该运营机构关联的用户将无法登录平台，确定禁用？",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    ).catch(() => {
      page.form.status = 1;
    });
  }
};
const close = () => {
  dialogRef.value.close();
  emit("before-close");
};
defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.add-box {
  padding: 12px;
  background-color: #fff;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #eaeaea;

  .el-form-item {
    margin-bottom: 16px;

    :deep(.el-form-item__label) {
      color: #606266;
      font-weight: 500;
    }
  }
}
.form-container {
  padding: 12px;
  background-color: #fff;
  margin: 12px;
  border-radius: 5px;
  border: 1px solid #EAEAEA;
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
  }

  .form-item {
    flex: 0 0 50%;
    padding: 8px;
    display: flex;
    align-items: center;

    .form-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      color: #303133;
    }
  }
  .form-item1 {
    flex: 0 0 50%;
    padding: 8px;
    display: flex;

    &.full-width {
      flex: 0 0 100%;
    }

    .form-label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      color: #303133;
    }
  }
}
</style>
