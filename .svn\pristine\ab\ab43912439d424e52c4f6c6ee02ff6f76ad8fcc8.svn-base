<template>
  <div class="main-box">
    <div class="left-box">
      <menu-first @menu-click="firstMenuClick"></menu-first>
    </div>
    <div class="right-box">
      <div class="top">
        <menu-second ref="menuSecondRef" @menu-click="secondMenuClick"></menu-second>
      </div>
      <div class="content">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import menuFirst from "./components/menuFirst.vue";
import menuSecond from "./components/menuSecond.vue";

import { ref, inject, onMounted, reactive, nextTick } from "vue";
import { useRouter } from "vue-router";

const $axios = inject("$axios");
const $router = useRouter();
const menuSecondRef = ref(null);
var firstLoad = true;

// 一级菜单点击
function firstMenuClick(params) {
  menuSecondRef.value.init(params.id);
  firstLoad = false;
}

// 二级菜单点击
function secondMenuClick(params) {
  if(!localStorage.getItem("syToken")) return;
  
  var id = params.key;
  var menuData = JSON.parse(localStorage.getItem("syMenus"));
  var node = treeFind(menuData, (item) => {
    return item.id === id;
  });
  if ("/" + node.url === $router.currentRoute.value.fullPath && !firstLoad) {
    $router.push({ path: "reloadView" });
    setTimeout(() => {
      $router.push({ path: node.url, query: getRequestParams(node.url) });
    }, 350);
  } else {
    $router.push({ path: node.url, query: getRequestParams(node.url) });
  }
}

// 查找树节点
function treeFind(tree, func) {
  for (const data of tree) {
    if (func(data)) return data;
    if (data.child && data.child.length > 0) {
      const res = treeFind(data.child, func);
      if (res) return res;
    }
  }
  return null;
}

// 获得请求参数
function getRequestParams(url) {
  let requestParams = {};
  if (url.indexOf("?") !== -1) {
    let str = url.substring(url.indexOf("?") + 1);
    let strs = str.split("&");
    for (let i = 0; i < strs.length; i++) {
      requestParams[strs[i].split("=")[0]] = strs[i].split("=")[1];
    }
  }
  return requestParams;
}
</script>

<style lang="scss" scoped>
.main-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;

  .left-box {
    width: auto;
    height: 100%;
    // background-color: #ba1f22;
    background: linear-gradient(10deg, #E8FDF0 0%, #F8FFFB 100%);
  }

  .right-box {
    width: 0px;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;

    .top {
      width: 100%;
      height: 58px;
      box-shadow: 0 4px 10px #0000000d;
    }

    .content {
      width: 100%;
      height: calc(100% - 58px);
      background-color: #f7f7f7;
      padding: 14px;
    }
  }
}
</style>
