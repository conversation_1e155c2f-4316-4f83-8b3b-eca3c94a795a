<template>
  <nd-dialog ref="dialogRef" :before-close="close" center :append-to-body="true" width="500px" height="200px" :title="'调整顺序'">
    <div class="change-order-box">
      <p>调整至第<nd-input v-model="data.index" type2="number3" :width="'90px'"></nd-input>位</p>
      <p>调整至第几位，就排到第几位</p>
      <p>输入值超最大序号时，默认排列到最后</p>
    </div>
    <template #footer>
      <nd-button type="primary" @click="save()"> 确认 </nd-button>
      <nd-button @click="close()"> 取消 </nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
import ndDialog from "@/components/ndDialog.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";

import { ref, reactive, watch, onMounted } from "vue";

const dialogRef = ref(null);

const prop = defineProps({
  // 文件
  files: {
    type: Array,
    default: () => [],
  },
});

const data = reactive({
  index: "",
  oldIndex: "",
});

const open = (index) => {
  data.index = index + 1;
  data.oldIndex = index + 1;
  dialogRef.value.open();
};

const close = () => {
  dialogRef.value.close();
};

const save = () => {
  let newObj = {};
  if (data.index - 1 === data.oldIndex - 1) {
    return;
  } else if (data.index - 1 === 0) {
    newObj = prop.files.splice(data.oldIndex - 1, 1);
    prop.files.unshift(...newObj);
  } else if (data.index >= prop.files.length) {
    newObj = prop.files.splice(data.oldIndex - 1, 1);
    prop.files.push(...newObj);
  } else if (data.index < prop.files.length && data.index - 1 > 0) {
    newObj = prop.files.splice(data.oldIndex - 1, 1);
    prop.files.splice(data.index - 1, 0, ...newObj);
  }
  data.index = "";
  data.oldIndex = "";
  prop.files.map((item, index) => {
    item.orders = index + 1;
  })
  close();
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.change-order-box {
  padding: 30px 0;

  > p {
    padding: 0 60px;
    color: #999;
  }

  > p:nth-of-type(1) {
    display: flex;
    align-items: center;
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;

    :deep(.nd-input-box .el-input__inner) {
      border-radius: 4px;
    }
  }
}
</style>
