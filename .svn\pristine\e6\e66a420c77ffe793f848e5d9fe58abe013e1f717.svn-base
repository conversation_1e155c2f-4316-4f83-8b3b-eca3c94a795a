<template>
  <nd-dialog ref="dialogRef" width="60vw" height="48vh" :title="page.title" align-center>
    <div class="my_stepTab">
      <div class="stepItem" v-for="(item, index) in stepOptions" :key="index"
        :class="currentTab === index ? 'activeTab' : ''">
        <div :class="`silk_ribbon silk_ribbon${index}`"></div>
        <div class="stepItem_title">{{ item.label }}</div>
      </div>
    </div>
    <el-form ref="addFormRef" :model="page.goods" :rules="rules" class="add-box">
      <template v-if="currentTab === 0">
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品图片" label-width="100px" class="star">
              <ndb-upload :files="page.goods.files" fzjs="product1"></ndb-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品名称" label-width="100px" class="star">
              <nd-input v-model="page.goods.name" placeholder="请输入" width="100%"></nd-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品类型" label-width="100px" class="star">
              <nd-select v-model="page.goods.category_id" placeholder="请选择" clearable width="100%">
                <el-option v-for="item in page.dict.categoryList" :label="item.name" :key="item.categoryId"
                  :value="item.categoryId" />
              </nd-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品标签" label-width="100px">
              <nd-select v-model="page.goods.tag_id" placeholder="请选择" clearable width="100%">
                <el-option v-for="item in page.dict.goodsTagList" :label="item.name" :key="item.tagId"
                  :value="item.tagId" />
              </nd-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="首页标签" label-width="100px">
              <nd-select v-model="page.goods.tag_id2" placeholder="请选择" clearable width="100%">
                <el-option v-for="item in page.dict.indexTypeList" :label="item.name" :key="item.tagId"
                  :value="item.tagId" />
              </nd-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="商品描述" label-width="100px" class="star">
              <nd-input v-model="page.goods.description" placeholder="请输入" width="100%"></nd-input>
            </el-form-item>
          </el-col>
        </el-row>
        规格列表
        <nd-table border style="height: 100%" :data="page.tableList">
          <el-table-column align="center" type="index" width="70px" label="序号" />
          <el-table-column align="center" label="规格" min-width="220px" label-class-name="star">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.sp_data" width="100%" placeholder="请输入规格，如：1kg/包" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="原价" min-width="160px" label-class-name="star">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.price" width="100%" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="协议价" min-width="160px">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.agreement_price" width="100%" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="成本价" min-width="160px">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.cost_price" width="100%" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="库存" min-width="160px" label-class-name="star">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.stock" width="100%" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="尺寸" min-width="160px">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.size" width="100%" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="重量(kg)" min-width="160px">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.weight" width="100%" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="体积(m³)" min-width="160px">
            <template #default="scope">
              <nd-input v-model.trim="scope.row.volume" width="100%" placeholder="请输入" />
            </template>
          </el-table-column>
        </nd-table>
        <div class="addNewTable" @click="addNewTableLine">新增一行</div>

      </template>

      <template v-if="currentTab === 1">
        <el-row>
          <el-col :span="12">
            <el-form-item label="品类" label-width="100px">
              <nd-input v-model="page.goods.pl_value" placeholder="请输入" width="100%"></nd-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" label-width="100px">
              <nd-input v-model="page.goods.brand_id" placeholder="请输入" width="100%"></nd-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货号" label-width="100px">
              <nd-input v-model="page.goods.product_sn" placeholder="请输入" width="100%"></nd-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="售后" label-width="100px">
              <nd-input v-model="page.goods.after_sales_type" placeholder="请输入" width="100%"></nd-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="特点" label-width="100px">
              <nd-input v-model="page.goods.feature" placeholder="请输入" width="100%"></nd-input>
            </el-form-item>
          </el-col>

          <!-- 鱼苗 -->
          <template v-if="page.goods.type === 1">
            <el-col :span="12">
              <el-form-item label="品种名" label-width="100px">
                <nd-input v-model="page.jsonGoods.pzm" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成活率" label-width="100px">
                <nd-input v-model="page.jsonGoods.chl" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="提供技术" label-width="100px">
                <nd-input v-model="page.jsonGoods.tgjs" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="起批量" label-width="100px">
                <nd-input v-model="page.jsonGoods.qpj" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发货地址" label-width="100px">
                <nd-input v-model="page.jsonGoods.fhdz" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
          </template>
          <!-- 饲料 -->
          <template v-if="page.goods.type === 2">
            <el-col :span="12">
              <el-form-item label="饲料种类" label-width="100px">
                <nd-input v-model="page.jsonGoods.slzl" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="适用鱼" label-width="100px">
                <nd-input v-model="page.jsonGoods.ssy" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生产厂家" label-width="100px">
                <nd-input v-model="page.jsonGoods.sccj" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产地" label-width="100px">
                <nd-input v-model="page.jsonGoods.cd" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
          </template>
          <!-- 鱼药 -->
          <template v-if="page.goods.type === 3">
            <el-col :span="12">
              <el-form-item label="原料组成" label-width="100px">
                <nd-input v-model="page.jsonGoods.ylzc" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性状" label-width="100px">
                <nd-input v-model="page.jsonGoods.xz" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="适用对象" label-width="100px">
                <nd-input v-model="page.jsonGoods.sydx" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="保质期" label-width="100px">
                <nd-input v-model="page.jsonGoods.bzq" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="储藏方式" label-width="100px">
                <nd-input v-model="page.jsonGoods.ccfs" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
          </template>
          <!-- 设备 -->
          <template v-if="page.goods.type === 4">
            <el-col :span="12">
              <el-form-item label="适用对象" label-width="100px">
                <nd-input v-model="page.jsonGoods.sydx" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用途" label-width="100px">
                <nd-input v-model="page.jsonGoods.yt" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类型" label-width="100px">
                <nd-input v-model="page.jsonGoods.lx" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="适用场所" label-width="100px">
                <nd-input v-model="page.jsonGoods.sycs" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号" label-width="100px">
                <nd-input v-model="page.jsonGoods.xh" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品类型" label-width="100px">
                <nd-input v-model="page.jsonGoods.cplx" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="功率" label-width="100px">
                <nd-input v-model="page.jsonGoods.gl" placeholder="请输入" width="100%"></nd-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item label="商品详情附件" label-width="100px">
              <ndb-upload :files="page.goods.files" fzjs="product2"></ndb-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <template #footer>
      <nd-button type="" v-if="currentTab == 0" @click="tabClick(1)">下一步</nd-button>
      <nd-button type="" v-if="currentTab == 1" @click="tabClick(0)">上一步</nd-button>
      <nd-button type="primary" v-if="currentTab == 1" icon="FolderChecked" @click="save">保&nbsp;存</nd-button>
      <nd-button type="" icon="Close" @click="close">关&nbsp;闭</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入公共组件
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndbUpload from "@/components/business/ndbUpload/index.vue";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 导入element-plus
import { ElMessage as elMessage } from "element-plus";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义ref
const dialogRef = ref(null);
const addFormRef = ref(null);
let currentTab = ref(0)
const stepOptions = [
  {
    label: '编辑商品基本信息',
    value: '0',
  },
  {
    label: '编辑商品详情',
    value: '1',
  }
]
// 定义page
const page = reactive({
  status: "",
  title: "",
  tableList: [
    { sp_data: "", price: "", agreement_price: "", cost_price: "", stock: "", size: "", weight: "", volume: "", },
    { sp_data: "", price: "", agreement_price: "", cost_price: "", stock: "", size: "", weight: "", volume: "", },
  ],
  dict: {
    categoryList: [],
    goodsTagList: [],
    indexTypeList: [],
  },
  goods: {
    productId: "",
    name: "", // 商品名称
    files: [], // 商品展示图片
    description: "", // 描述
    category_id: null, // 类别
    tag_id: null, // 商品标签
    tag_id2: null, // 首页标签
    pl_value: "", // 品类
    brand_id: null, // 品牌
    product_sn: "", // 货号
    after_sales_type: null, // 售后
    feature: "", // 特点
  },
  jsonGoods: {
    pzm: "", // 品种名
    chl: "", // 成活率
    tgjs: "", // 提供技术
    qpj: "", // 起批量
    fhdz: "", // 发货地址
    slzl: "", // 饲料种类
    ssy: "", // 适用鱼
    sccj: "", // 生产厂家
    cd: "", // 产地
    ylzc: "", // 原料组成
    xz: "", // 性状
    bzq: "", // 适用对象
    ccfs: "", // 保质期
    sydx: "", // 储藏方式
    yt: "", // 用途
    lx: "", // 类型
    sycs: "", // 适用场所
    xh: "", // 型号
    cplx: "", // 产品类型
    gl: "", // 功率
  }
});

// 表单校验规则
const rules = reactive({
  name: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
});

// 打开弹窗
function open(status, params) {
  console.log("params", params);
  currentTab.value = 0

  getCategoryList();
  getTagList();
  if (status === "add") {
    page.status = "add";
    page.title = "新建";
    dialogRef.value.open();
  }

  if (status === "edit") {
    page.status = "edit";
    page.title = "编辑";
    page.goods.productId = params.productId;
    getDetail();
    dialogRef.value.open();
  }
}

function tabClick(index) {
  currentTab.value = index
}

// 商品分类
function getCategoryList() {
  $axios({
    url: "/dict/getCategory",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) page.dict.categoryList = res.data.data;
  });
}

// 商品标签
function getTagList() {
  $axios({
    url: "/dict/getGoodsTag",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      page.dict.goodsTagList = res.data.data.filter(item => item.type == 'product')
      page.dict.indexTypeList = res.data.data.filter(item => item.type == 'index')
    };
  });
}

// 获得详情
function getDetail() {
  $axios({
    url: "/goods/getDetail",
    method: "get",
    data: {
      id: page.goods.productId,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      page.goods = res.data.data;
    }
  });
}

function addNewTableLine() {
  page.tableList.push({ sp_data: "", price: "", agreement_price: "", cost_price: "", stock: "", size: "", weight: "", volume: "" })
}

// 关闭
const close = () => {
  dialogRef.value.close();
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 清空表单
  page.goods = {
    id: "",
    name: "",
    files: [],
  };
};

// 保存
const save = async () => {
  await addFormRef.value.validate((valid, fields) => {
    if (valid) {
      // 新增
      if (page.status === "add") {
        add();
      }
      // 编辑
      if (page.status === "edit") {
        edit();
      }
    } else {
      console.log("校验失败!", fields);
    }
  });
};

// 新增
function add() {
  let postData = {
    name: page.goods.name,
    files: page.goods.files,
    description: page.goods.description,
    category_id: page.goods.category_id,
    tag_id: page.goods.tag_id,
    tag_id2: page.goods.tag_id2,
    pl_value: page.goods.pl_value,
    brand_id: page.goods.brand_id,
    product_sn: page.goods.product_sn,
    after_sales_type: page.goods.after_sales_type,
    feature: page.goods.feature,
    pic: page.goods.files[0].url, // 第一张图
    attribute_json: JSON.stringify(page.jsonGoods), // 其他属性json
    specs: page.tableList,
  }
  console.log("postData", postData);
  $axios({
    url: "/goods/add",
    method: "post",
    data: postData,
  }).then((res) => {
    if (res.data.code === 2000) {
      elMessage({
        message: "保存成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 编辑
function edit() {
  $axios({
    url: "/goods/update",
    method: "post",
    data: page.goods,
  }).then((res) => {
    if (res.data.code === 200) {
      elMessage({
        message: "保存成功",
        type: "success",
      });
      emit("before-close");
      close();
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
:deep(.star) {

  .el-form-item__label::before,
  .cell::before {
    content: "*";
    color: red;
    margin-right: 2px;
  }

}

.my_stepTab {
  position: sticky;
  z-index: 99;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 0 60px 20px;

  .stepItem {
    flex: 1;
    position: relative;
    z-index: 99;
    height: 40px;

    .stepItem_title {
      color: #666;
      height: 40px;
      line-height: 40px;
      text-align: center;
    }

    .silk_ribbon0 {
      display: inline-block;
      position: absolute;
      z-index: -1;
      width: 100%;
      height: 40px;
      line-height: 40px;
      padding-left: 15px;
      background: #f2f2f2;
      left: 0;
      top: 0;
    }

    .silk_ribbon0:after {
      content: "";
      position: absolute;
    }

    .silk_ribbon0:after {
      height: 0;
      width: 0;
      border-top: 20px solid transparent;
      border-bottom: 20px solid transparent;
      border-left: 15px solid #f2f2f2;
      right: -15px;
    }

    .silk_ribbon1 {
      display: inline-block;
      position: absolute;
      z-index: -1;
      width: calc(100% - 40px);
      height: 40px;
      line-height: 40px;
      background: #f2f2f2;
      top: 0;
      left: 25px;
    }

    .silk_ribbon1:before,
    .silk_ribbon1:after {
      content: "";
      position: absolute;
    }

    .silk_ribbon1:before {
      height: 0;
      width: 0;
      border-left: 15px solid transparent;
      border-top: 20px solid #f2f2f2;
      border-bottom: 20px solid #f2f2f2;
      bottom: 0;
      left: -15px;
    }

    .silk_ribbon1:after {
      height: 0;
      width: 0;
      border-top: 20px solid transparent;
      border-bottom: 20px solid transparent;
      border-left: 15px solid #f2f2f2;
      right: -15px;
    }
  }

  .activeTab {
    .stepItem_title {
      color: #fff;
    }

    .silk_ribbon {
      background: #ba1f22;
    }

    .silk_ribbon0:after {
      border-left-color: #ba1f22;
    }

    .silk_ribbon1:before {
      border-top-color: #ba1f22;
      border-bottom-color: #ba1f22;
      bottom: 0;
      left: -15px;
    }

    .silk_ribbon1:after {
      border-left-color: #ba1f22;
      right: -15px;
    }
  }
}


.add-box {
  padding: 0 60px 60px;

  :deep(.el-textarea__inner) {
    height: 80px;
  }
}

.addNewTable {
  width: fit-content;
  color: #BA1F22;
  text-decoration: underline;
  margin: 10px 0;
  cursor: pointer;
}
</style>