<template>
    <!-- // 订单处理 -->
    <nd-dialog ref="remarkDialogRef" width="46vw" height="37vh" title="备注" align-center :before-close='close'>
        <el-form ref="addFormRef" :model="form" :rules="rules" class="add-box" label-position="left">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="备注" label-width="60px" prop="remark">
                        <nd-input type="textarea" v-model="form.remark" placeholder=" " width="100%" show-word-limit maxlength="500"/>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <nd-button type="primary" icon="Pointer" @click="submit()">确&nbsp;定</nd-button>
            <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button>
        </template>
    </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义方法
let myEmit = defineEmits(["refreshTable"]);
// 定义属性
const props = defineProps({

});

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const remarkDialogRef = ref(null);
// 定义表单数据
const addFormRef = ref(null);
let tableContent = ref(null);
let tableData = ref([]);
let orderId = ref('');
let form = ref({
    "orderId": '',
    "shWay": '',
    "shReject": "",
    "remark": ""
});






// 定义校验规则 ==========================================================

// 自定义校验规则 --- 正则 --- 功能菜单名称最长不得超过10个汉字
const validateFunctionName = (rule, value, callback) => {
    const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
    let chineseCharacters = form.value.menuName.match(chineseReg); // 匹配输入字符串中的汉字
    if (chineseCharacters && chineseCharacters.length > 10) {
        return callback(new Error("error"));
    } else {
        return callback();
    }
};

// 表单校验规则
const rules = reactive({
    remark: [{ required: true, message: "请输入备注", }],
});

// 打开弹窗 ==============================================================
// 打开弹窗
function open() {
    remarkDialogRef.value.open();
}
// 获得详情
function getDetail() {
    $axios({
        url: "/order/manage/find",
        serverName: 'nd-base2',
        method: "get",
        data: {
            orderId: orderId.value,
        },
    }).then((res) => {
        if (res.data.code === 2000) {
            tableContent.value = res.data.data;
            tableData.value = tableContent.value.detailVos;
        }
    });
}

// 清空表单
const clear = () => {
    // 清空校验
    if (addFormRef.value) {
        addFormRef.value.resetFields();
    }
};

// 关闭弹窗 ==============================================================
const close = () => {
    remarkDialogRef.value.close();
    clear();
};

// 保存提交 ==============================================================
const submit = async () => {
    await addFormRef.value.validate((valid, fields) => {
        if (valid) {
            let params = form.value;
            $axios({
                url: "/order/manage/deal/cancel",
                method: "post",
                serverName: 'nd-base2',
                data: params,
            }).then((res) => {
                if (res.data.code === 2000) {
                    // 轻提示
                    ElMessage({
                        message: "处理成功",
                        type: "success",
                    });
                    // 关闭弹框
                    myEmit("refreshTable");
                    close();
                } else {
                    ElMessage({
                        message: res.data.message,
                        type: "warning",
                    });
                }
            });
        } else {
            console.log("校验失败!", fields);
        }
    });
};

// 暴露方法给父组件 =======================================================
defineExpose({
    open,
    clear,
});
</script>

<style lang="scss" scoped>
.goodstotal {
    margin-bottom: 20px;
}

.add-box {
    padding: 0px;
    margin-top: 32px;

    :deep(.el-textarea__inner) {
        height: 280px;
    }
}
:deep(.el-form-item__label){
    justify-content:flex-end
}
</style>