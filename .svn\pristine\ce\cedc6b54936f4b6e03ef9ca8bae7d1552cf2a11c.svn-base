<template>
  <ndb-page-list>
    <template #tag>
      <nd-tag
        @click="tagClick('')"
        :checked="page.search.data.label === 'pending'"
        >全部</nd-tag
      >
      <nd-tag
        @click="tagClick(0)"
        :checked="page.search.data.label === 'approved'"
        >待付款</nd-tag
      >
      <nd-tag
        @click="tagClick(1)"
        :checked="page.search.data.label === 'initiated'"
        >待发货</nd-tag
      >
      <nd-tag
        @click="tagClick(50)"
        :checked="page.search.data.label === 'cancellationRequest'"
        >申请取消</nd-tag
      >
      <nd-tag
        @click="tagClick(2)"
        :checked="page.search.data.label === 'received'"
        >已发货</nd-tag
      >
      <nd-tag
        @click="tagClick(3)"
        :checked="page.search.data.label === 'completed'"
        >已完成</nd-tag
      >
      <nd-tag
        @click="tagClick(51)"
        :checked="page.search.data.label === 'closed'"
        >已关闭</nd-tag
      >
    </template>
    <!-- <template #button>
      <nd-button @click="openAddDialog()" type="primary" icon="plus" authKey="DEMO_VIEW_ADD">新建</nd-button>
      <nd-button @click="openImportDialog()" icon="Download" authKey="DEMO_VIEW_IMPORT">导入</nd-button>
      <nd-button @click="openExportDialog()" icon="Upload" authKey="DEMO_VIEW_EXPORT">导出</nd-button>
    </template> -->
    <template #search>
      <nd-search-more arrowMarginLeft="30px">
        <nd-search-more-item title="商品类型" width="120px">
          <nd-select
            :empty-values="['']"
            :value-on-clear="''"
            v-model="page.search.data.splx"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in page.search.dict.splxList"
              :label="item.name"
              :key="item.categoryId"
              :value="item.categoryId"
            />
          </nd-select>
        </nd-search-more-item>
        <nd-search-more-item title="日期搜索" width="120px">
          <nd-select
            :empty-values="['']"
            :value-on-clear="''"
            v-model="page.search.data.rqlx"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in page.search.dict.datetypeList"
              :label="item.name"
              :key="item.modelId"
              :value="item.modelId"
            />
          </nd-select>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-date-picker
            v-model="page.search.data.cycleArr"
            :empty-values="['', '']"
            :value-on-clear="''"
            range-separator="至"
            type="daterange"
            format="YYYY-MM-DD "
            value-format="YYYY-MM-DD"
            placeholder="请选择"
            width="100%"
          ></nd-date-picker>
        </nd-search-more-item>
        <nd-search-more-item title="关键字搜索" width="120px">
          <!-- <nd-select v-model="page.search.data.modelId" placeholder="请选择" clearable>
            <el-option v-for="item in page.search.dict.approveltypeList" :label="item.name" :key="item.modelId"
              :value="item.modelId" />
          </nd-select> -->
          &nbsp;&nbsp;&nbsp;&nbsp;
          <nd-input
            type="text"
            v-model.trim="page.search.data.gjzValue"
            width="100%"
            placeholder="请输入"
            resize="none"
          />
        </nd-search-more-item>
        <nd-search-more-item title="订单归属地" width="120px">
          <nd-tree-select
            v-model="page.search.data.areaId"
            :teleported="false"
            :props="page.tree.defaultProps"
            lazy
            :load="loadNode"
            ref="treeRef"
            value-key="id"
            node-key="id"
            :placeholder="placeholder"
            :render-after-expand="false"
            :default-expanded-keys="defaultExpandedNodes"
            :check-strictly="checkStrictly"
            @node-click="handleNodeClick"
            filterable
          />
        </nd-search-more-item>

        <template #footer>
          <nd-button type="primary" @click="queryTable" authKey="">
            查询
          </nd-button>
          <nd-button @click="reset"> 重置 </nd-button>
        </template>
      </nd-search-more>
    </template>
    <template #table>
      <div class="table-container">
        <!-- 表头行 -->
        <div class="table-row header-row">
          <div class="header-cell order-product2">订单信息</div>
          <div class="header-cell order-product3">所属供应商</div>
          <div class="header-cell">供应商所属地区</div>
          <div class="header-cell">商品类型</div>
          <div class="header-cell">收货信息</div>
          <div class="header-cell">状态</div>
          <div class="header-cell">操作</div>
        </div>

        <!-- 数据行 -->
        <div class="table-content">
          <div
            class="item-box"
            v-for="item in page.list.data"
            :key="item.orderCode"
          >
            <div class="row-indicator">
              <div class="row-item">
                <div>下单时间：{{ item.xdTime }}</div>
                <div class="row-code">
                  订单编号：
                  <div class="order-num" @click.stop="openDetail(item)">
                    <span v-if="item.sfcd == 0">{{ item.orderNo }}</span>
                    <span v-if="item.sfcd == 1">
                      <span
                        v-if="
                          item.status == 0 ||
                          (item.status == 51 && item.cancelType == 1) ||
                          (item.status == 51 && item.cancelType == 4)
                        "
                        >{{ item.orderNo }}</span
                      >
                      <span v-else>
                        {{ item.orderNoChild }}
                      </span>
                    </span>
                  </div>
                </div>
                <div class="total">共一件商品，合计：¥{{ item.amount }}</div>
              </div>
            </div>

            <div class="table-row">
              <div class="data-cell order-product2">
                <div
                  class="goodsItemList"
                  v-for="(good, index) in item.detailVos"
                  :key="index"
                >
                  <div class="goodImg">
                    <img :src="good.productVo.pic" alt="" />
                  </div>
                  <div class="goodInfo">
                    <div class="goodTitle">
                      <div class="left">
                        {{ good.productVo.name }}
                      </div>
                      <div class="right">¥{{ good.amount }}</div>
                    </div>
                    <div class="goodNum">
                      <div class="left">{{ good.productVo.spData }}</div>
                      <div class="right">x{{ good.nums }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 所属供应商 -->
              <div class="data-cell order-product3 data-cell-center">
                <div class="buyer">
                  <div class="name">{{ item.deptName }}</div>
                </div>
              </div>
              <!-- 供应商所属地区 -->
              <div class="data-cell data-cell-center">
                <div class="buyer">
                  <div class="name">{{ item.allAreaName }}</div>
                </div>
              </div>
              <!-- 商品类型 -->
              <div class="data-cell data-cell-center">
                <div
                  class="splx"
                  v-for="(productType, index) in handlerDatas(item.detailVos)"
                  :key="index"
                >
                  {{ productType }}
                </div>
              </div>

              <!-- 收货信息 -->
              <div class="data-cell">
                <div class="shxx">
                  <div class="maijia">买家姓名：{{ item.dzglVo.realname }}</div>
                  <div class="lxfs">联系方式：{{ item.dzglVo.phone }}</div>
                </div>
              </div>

              <!-- 状态 -->
              <div class="data-cell data-cell-center">
                <!-- <div>
                  <div class="status" v-if="item.status == 0">
                    <span class="round"></span>
                    <span>待出货</span>
                  </div>
                  <div class="status" v-if="item.status == 1">
                    <span class="round1"></span>
                    <span>待收货</span>
                  </div>
                  <div class="status" v-if="item.status == 2">
                    <span class="round2"></span>
                    <span>已完成</span>
                  </div>
                  <div class="status" v-if="item.status == 3">
                    <span class="round3"></span>
                    <span>已取消</span>
                  </div>
                </div> -->
                <div class="tableStatus">
                  <div
                    class="dot"
                    style="background-color: #267fff"
                    v-if="item.status == 0 && item.sfsqsh == 0"
                  ></div>
                  <div
                    class="dot"
                    style="background-color: #ff8d02"
                    v-else-if="item.status == 1 && item.sfqx == 0"
                  ></div>
                  <div
                    class="dot"
                    style="background-color: #999999"
                    v-else-if="item.status == 1 && item.sfqx == 1"
                  ></div>
                  <div
                    class="dot"
                    style="background-color: #068324"
                    v-else-if="item.status == 2 && item.sfsqsh == 0"
                  ></div>
                  <div
                    class="dot"
                    style="background-color: #068324"
                    v-else-if="item.status == 3 && item.sfsqsh == 0"
                  ></div>
                  <div
                    class="dot"
                    style="background-color: #999999"
                    v-else-if="(item.sfsqsh = 1 && item.shStatue == 1)"
                  ></div>
                  <div
                    class="dot"
                    style="background-color: #999999"
                    v-else-if="(item.sfsqsh = 1 && item.shStatue == 2)"
                  ></div>
                  <div
                    class="dot"
                    style="background-color: #999999"
                    v-else-if="(item.sfsqsh = 1 && item.shStatue == 3)"
                  ></div>
                  <span
                    class="dot"
                    style="background-color: #999999"
                    v-else-if="item.status == 51"
                  ></span>
                  <span
                    class="dot"
                    style="background-color: #ff8d02"
                    v-else-if="item.status == 70"
                  ></span>
                  <div
                    class="dot"
                    style="background-color: #ff8d02"
                    v-else-if="item.status == 71"
                  ></div>
                  <div class="text">
                    <span v-if="item.status == 0 && item.sfsqsh == 0"
                      >待付款</span
                    >
                    <span v-else-if="item.status == 1 && item.sfqx == 0"
                      >待发货</span
                    >
                    <span v-else-if="item.status == 1 && item.sfqx == 1"
                      >申请取消</span
                    >
                    <span v-else-if="item.status == 2 && item.sfsqsh == 0"
                      >已发货</span
                    >
                    <span v-else-if="item.status == 3 && item.sfsqsh == 0"
                      >已完成</span
                    >
                    <span v-else-if="(item.sfsqsh = 1 && item.shStatue == 1)"
                      >处理中</span
                    >
                    <span v-else-if="item.shStatue == 2">售后完成</span>
                    <span v-else-if="item.shStatue == 3">售后关闭</span>
                    <span v-else-if="item.status == 51">已关闭</span>
                    <span v-else-if="item.status == 60">已关闭</span>
                    <span v-else-if="item.status == 70">已申请售后</span>
                    <span v-else-if="item.status == 71">退货申请中</span>
                  </div>
                </div>
              </div>

              <!-- 操作 -->
              <div class="data-cell">
                <div class="operation">
                  <div class="padding tableOperation">
                    <div class="operationItem" @click="openDetail(item)">
                      订单详情
                    </div>
                    <div
                      v-if="
                        item.status == 1 &&
                        item.sfqx == 0 &&
                        item.sfkcz == 1 &&
                        item.userType == 2
                      "
                      class="operationItem"
                      @click="openShipment(item)"
                    >
                      发货
                    </div>

                    <div
                      @click="lookWl(item)"
                      v-if="item.status == 2 || item.status == 3"
                      class="operationItem"
                    >
                      查看物流
                    </div>
                    <div
                      @click="openOrderProcessingRef(item)"
                      v-if="
                        item.status == 1 &&
                        item.sfqx == 1 &&
                        item.sfkcz == 1 &&
                        item.userType == 2
                      "
                      class="operationItem"
                    >
                      去处理
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <nd-table
        border
        style="height: 100%"
        :data="page.list.data"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="#" type="selection" width="52px">
        </el-table-column>
        <el-table-column
          align="center"
          label="订单编号"
          prop="orderNo"
          width="180px"
        >
          <template #default="scoped">
            <span v-if="scoped.row.sfcd == 0">{{ scoped.row.orderNo }}</span>
            <span v-if="scoped.row.sfcd == 1">
              <span
                v-if="
                  scoped.row.status == 0 ||
                  (scoped.row.status == 51 && scoped.row.cancelType == 1) ||
                  (scoped.row.status == 51 && scoped.row.cancelType == 4)
                "
                >{{ scoped.row.orderNo }}</span
              >
              <span v-else>
                {{ scoped.row.orderNoChild }}
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          class-name="orderInfoF"
          align="center"
          label="订单信息"
          prop="name"
          width="640px"
        >
          <template #default="scoped">
            <div class="padding orderInfo orderInfoFlex orderInfoaddselect">
              <div
                v-for="(good, index) in scoped.row.detailVos"
                :key="index"
                class="goodsItemList"
              >
                <div class="goodImg">
                  <img :src="good.productVo.pic" alt="" />
                </div>
                <div class="goodInfo">
                  <div class="goodTitle">
                    <div class="left">
                  

                      {{ good.productVo.name }}
                    </div>
                    <div class="right">¥{{ good.amount }}</div>
                  </div>
                  <div class="goodNum">
                    <div class="left">{{ good.productVo.spData }}</div>
                    <div class="right">x{{ good.nums }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="所属供应商"
          width="160px"
          prop="deptName"
        >
        </el-table-column>
        <el-table-column
          align="center"
          label="供应商所属地区"
          width="160px"
          prop="allAreaName"
        >
        </el-table-column>
        <el-table-column
          align="center"
          label="下单时间"
          width="160px"
          prop="xdTime"
        >
        </el-table-column>
        <el-table-column
          class-name="orderInfoF"
          align="center"
          label="商品类型"
          width="160px"
          prop="xdTime"
        >
          <template #default="scoped">
            <div
              class="productTypeItem"
              v-for="(productType, index) in handlerDatas(scoped.row.detailVos)"
              :key="index"
            >
              {{ productType }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          class-name="orderInfoF"
          label="收货信息"
          width="300px"
          prop="discountPrice"
        >
          <template #default="scoped">
            <div class="padding tabelshippingInfo borderRight">
              <div class="maijia">
                买家姓名：{{ scoped.row.dzglVo.realname }}
              </div>
              <div class="lxfs">
                收货人：{{ scoped.row.dzglVo.name }}&nbsp;{{
                  scoped.row.dzglVo.phone
                }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="状态"
          class-name="orderInfoF"
          width="106px"
          prop="discountPrice"
        >
          <template #default="scoped">
            <div class="padding tableStatus borderRight">
              <div
                class="dot"
                style="background-color: #267fff"
                v-if="scoped.row.status == 0 && scoped.row.sfsqsh == 0"
              ></div>
              <div
                class="dot"
                style="background-color: #ff8d02"
                v-else-if="scoped.row.status == 1 && scoped.row.sfqx == 0"
              ></div>
              <div
                class="dot"
                style="background-color: #999999"
                v-else-if="scoped.row.status == 1 && scoped.row.sfqx == 1"
              ></div>
              <div
                class="dot"
                style="background-color: #068324"
                v-else-if="scoped.row.status == 2 && scoped.row.sfsqsh == 0"
              ></div>
              <div
                class="dot"
                style="background-color: #068324"
                v-else-if="scoped.row.status == 3 && scoped.row.sfsqsh == 0"
              ></div>
              <div
                class="dot"
                style="background-color: #999999"
                v-else-if="(scoped.row.sfsqsh = 1 && scoped.row.shStatue == 1)"
              ></div>
              <div
                class="dot"
                style="background-color: #999999"
                v-else-if="(scoped.row.sfsqsh = 1 && scoped.row.shStatue == 2)"
              ></div>
              <div
                class="dot"
                style="background-color: #999999"
                v-else-if="(scoped.row.sfsqsh = 1 && scoped.row.shStatue == 3)"
              ></div>
              <span
                class="dot"
                style="background-color: #999999"
                v-else-if="scoped.row.status == 51"
              ></span>
              <span
                class="dot"
                style="background-color: #ff8d02"
                v-else-if="scoped.row.status == 70"
              ></span>
              <div
                class="dot"
                style="background-color: #ff8d02"
                v-else-if="scoped.row.status == 71"
              ></div>
              <div class="text">
                <span v-if="scoped.row.status == 0 && scoped.row.sfsqsh == 0"
                  >待付款</span
                >
                <span v-else-if="scoped.row.status == 1 && scoped.row.sfqx == 0"
                  >待发货</span
                >
                <span v-else-if="scoped.row.status == 1 && scoped.row.sfqx == 1"
                  >申请取消</span
                >
                <span
                  v-else-if="scoped.row.status == 2 && scoped.row.sfsqsh == 0"
                  >已发货</span
                >
                <span
                  v-else-if="scoped.row.status == 3 && scoped.row.sfsqsh == 0"
                  >已完成</span
                >
                <span
                  v-else-if="
                    (scoped.row.sfsqsh = 1 && scoped.row.shStatue == 1)
                  "
                  >处理中</span
                >
                <span v-else-if="scoped.row.shStatue == 2">售后完成</span>
                <span v-else-if="scoped.row.shStatue == 3">售后关闭</span>
                <span v-else-if="scoped.row.status == 51">已关闭</span>
                <span v-else-if="scoped.row.status == 60">已关闭</span>
                <span v-else-if="scoped.row.status == 70">已申请售后</span>
                <span v-else-if="scoped.row.status == 71">退货申请中</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="订单总金额"
          width="120px"
          prop="amount"
        >
        </el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          class-name="orderInfoF"
          width="120px"
        >
          <template #default="scoped">
            <div class="padding tableOperation">
              <div class="operationItem" @click="openDetail(scoped.row)">
                订单详情
              </div>
              <div
                v-if="
                  scoped.row.status == 1 &&
                  scoped.row.sfqx == 0 &&
                  scoped.row.sfkcz == 1 &&
                  scoped.row.userType == 2
                "
                class="operationItem"
                @click="openShipment(scoped.row)"
              >
                发货
              </div>
           
              <div
                @click="lookWl(scoped.row)"
                v-if="scoped.row.status == 2 || scoped.row.status == 3"
                class="operationItem"
              >
                查看物流
              </div>
              <div
                @click="openOrderProcessingRef(scoped.row)"
                v-if="
                  scoped.row.status == 1 &&
                  scoped.row.sfqx == 1 &&
                  scoped.row.sfkcz == 1 &&
                  scoped.row.userType == 2
                "
                class="operationItem"
              >
                去处理
              </div>
            </div>
          </template>
        </el-table-column>
      </nd-table> -->
    </template>
    <template #page>
      <nd-pagination
        :current-page="page.pager.pageIndex"
        :page-size="page.pager.pageSize"
        :total="page.pager.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </ndb-page-list>
  <ndb-import ref="importRef" type="PRO_TASK" titles="任务导入" projectId="1" />
  <ndb-export ref="exportRef" />
  <add-dialog ref="addDialogRef" @before-close="getTableData"></add-dialog>
  <shipment-dialog
    ref="shipmentDialogRef"
    @before-close="getTableData"
  ></shipment-dialog>
  <OrderProcessing ref="orderProcessingRef" @before-close="getTableData">
  </OrderProcessing>
</template>

<script setup>
// 导入公共组件
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndTag from "@/components/ndTag.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import ndbExport from "@/components/business/ndbExport/index.vue";
import OrderProcessing from "./components/orderProcessing.vue";
// 导入子组件
import addDialog from "./components/addDialog.vue";
import shipmentDialog from "./components/shipment.vue";
import ndTreeSelect from "@/components/ndTreeSelect.vue";
// 导入vue
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  computed,
  nextTick,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  ElMessage as elMessage,
  ElMessageBox as elMessageBox,
} from "element-plus";
const router = useRouter();
const route = useRoute();
// 定义axios
const $axios = inject("$axios");

// 定义ref
const addDialogRef = ref(null);
const shipmentDialogRef = ref(null);
const exportRef = ref(null);
const importRef = ref(null);
const orderProcessingRef = ref(null);
// 定义page
const page = reactive({
  tree: {
    data: [],
    defaultProps: {
      value: "id",
      label: "name",
      isLeaf: "leaf",
    },
  },
  search: {
    data: {
      label: "pending",
      rqlx: "", //日期类型 XD:下单时间 FK:付款时间 FH:发货时间 SH:收货时间
      status: "",
      splx: "", //商品类型
      gjzValue: "", //关键字-过滤值
      cycleArr: ["", ""],
      areaId: "", //订单归属地
    },
    dict: {
      approveltypeList: [], //审批类型
      statusList: [], //状态
      splxList: [],
      datetypeList: [
        { modelId: "XD", name: "下单时间" },
        { modelId: "FK", name: "付款时间" },
        { modelId: "FH", name: "发货时间" },
        { modelId: "SH", name: "确认收货时间" },
      ],
    },
  },
  list: {
    data: [],
  },
  pager: {
    pageIndex: 1,
    pageSize: 30,
    total: 0,
  },
});
// 加载节点
let placeholder = ref("请输入");
let defaultExpandedNodes = ref([]);
let checkStrictly = ref(true);
let isFirstExpandFlage = ref(false);
let isFirstExpandData = ref([]);
let benji = ref(true);
async function loadNode(node, resolve) {
  loadchildnode(node, resolve);
}
async function loadchildnode(node, resolve) {
  console.log(node, "--+++");
  let params = {
    benji: benji.value, //	是否返回本级
    // containDept: false, //是否包含部门
    // deptType: 1, //部门类型 1运营 2供应商
    // limitLevel: "", //最低级别，用来控制isLeaf的返回值
  };
  if (node.level == 0) {
    params.id = "";
  } else if (node.data.id) {
    params.id = node.data.id;
  }
  let res = null;
  if (isFirstExpandFlage.value) {
    res = isFirstExpandData.value;
    isFirstExpandFlage.value = false;
    benji.value = false;
  } else {
    res = await getNextAreaTree(params);
  }
  console.log("🚀 ~ loadchildnode ~ res:", params.benji);
  return resolve(res);
}
function getNextAreaTree(params) {
  return $axios({
    method: "get",
    data: params,
    url: "/common/areaTree",
    serverName: "nd-base2",
  }).then((res) => {
    console.log(res, "地区树");
    if (res.data.code === 2000) {
      isFirstExpandFlage.value = false;
      if (!params.id) {
        isFirstExpandData.value = res.data.data[0].children;
        isFirstExpandFlage.value = true;
        defaultExpandedNodes.value = [res.data.data[0].id];
      }
      return res.data.data || [];
    }
  });
}
function handleNodeClick(node) {
  console.log("🚀 ~ node:", node);
}
// 分类改变
function tagClick(index) {
  if (index === "") {
    page.search.data.label = "pending";
  }
  if (index === 0) {
    page.search.data.label = "approved";
  }
  if (index === 1) {
    page.search.data.label = "initiated";
  }
  if (index === 2) {
    page.search.data.label = "received";
  }
  if (index === 3) {
    page.search.data.label = "completed";
  }
  if (index === 50) {
    page.search.data.label = "cancellationRequest";
  }
  if (index === 51) {
    page.search.data.label = "closed";
  }
  page.search.data.status = index;
  // 获得表格数据
  getTableData();
}

// 获得审批类型
function getApprovalType() {
  return;
  $axios({
    url: "/oa/model/selectItem",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      page.search.dict.approveltypeList = res.data.data;
    }
  });
}
// 获得商品分类
const getGoodsType = () => {
  $axios({
    url: "/dict/getCategory",
    method: "get",
    data: {},
  }).then((res) => {
    if (res.data.code === 2000) {
      const allOption = { categoryId: "", name: "全部" };
      page.search.dict.splxList = [allOption, ...res.data.data];
    }
  });
};
// router
let memberId = ref("");
// 获得表格数据
function getTableData() {
  $axios({
    url: "/order/manage/page/findAll",
    serverName: "nd-base2",
    method: "get",
    data: {
      page: page.pager.pageIndex,
      size: page.pager.pageSize,
      splx: page.search.data.splx,
      status: page.search.data.status, //商品状态 0待付款 1:待发货 2:待收货 3:已完成 50:订单取消中 51:订单已取消 20:已发货
      rqlx: page.search.data.rqlx, //日期类型 XD:下单时间 FK:付款时间 FH:发货时间 SH:收货时间
      beginDate: page.search.data.cycleArr ? page.search.data.cycleArr[0] : "", // 开始时间
      endDate: page.search.data.cycleArr ? page.search.data.cycleArr[1] : "", // 结束时间
      gjzLb: "",
      gjzValue: page.search.data.gjzValue, //关键字-过滤值
      del: "",
      areaId: page.search.data.areaId, //订单归属地
      memberId: memberId.value,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      page.list.data = res.data.data.records.map((item, index) => {
        return {
          ...item,
          checked: false, // 选中框
        };
      });
      page.pager.total = res.data.data.total; //总页数
      page.pager.pageIndex = res.data.data.current; //当前页
      page.pager.pageSize = res.data.data.size; //每页记录数
    } else {
      elMessage({
        message: res.data.message,
        type: "warning",
      });
    }
  });
}

// 重置
function reset() {
  page.search.data.splx = "";
  page.search.data.rqlx = "";
  page.search.data.cycleArr = ["", ""];
  page.search.data.gjzValue = "";
  page.search.data.areaId = "";
  getTableData();
}

// 打开新增对话框
function openAddDialog() {
  addDialogRef.value.open("add", null);
}

// 选中框
const selectedPoints = ref([]);
const handleSelectionChange = (val) => {
  selectedPoints.value = val.map((item) => item.buildId);
};

// // 打开编辑dialog
// function openEditDialog(params) {
//   addDialogRef.value.open("detail", params);
// }
// 打开详情dialog
function openDetail(params) {
  addDialogRef.value.open(params, "detail");
}
// 打开发货dialog
function openShipment(params) {
  shipmentDialogRef.value.open(params, "shipment");
}
// 打开删除dialog
function openDeleteDialog(data) {
  elMessageBox
    .confirm("确定删除?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      $axios({
        url: "/goods/delete",
        method: "post",
        data: {
          goodsId: data.id,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          elMessage({
            message: "删除成功",
            type: "success",
          });
          getTableData();
        } else {
          elMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    });
}

// 分页每页数量改变
function handleSizeChange(params) {
  page.pager.pageSize = params;
  page.pager.pageIndex = 1;
  getTableData();
}

// 分页页码改变
function handleCurrentChange(params) {
  page.pager.pageIndex = params;
  getTableData();
}

// 导入
function openImportDialog() {
  importRef.value.open();
}

// 导出
function openExportDialog() {
  let params = {
    label: "approved",
    page: page.pager.pageIndex,
    size: page.pager.pageSize,
    label: page.search.data.label,
    status: page.search.data.status, // 流程状态
    modelId: page.search.data.modelId, // 审批类型
    startTime: page.search.data.cycleArr
      ? searchData.cycleArrfq[0] + ":00"
      : "", // 发起开始时间
    endTime: page.search.data.cycleArr ? searchData.cycleArrfq[1] + ":00" : "", // 发起结束时间
  };
  exportRef.value.open(params, "OA_EXAMINE_LIET", "");
}
// 数字转为中文
function numberToChinese(num) {
  if (num === 0) return "零";
  const chineseNum = [
    "零",
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
  ];
  const chineseUnit = [
    "",
    "十",
    "百",
    "千",
    "万",
    "十",
    "百",
    "千",
    "亿",
    "十",
    "百",
    "千",
  ];
  const str = num.toString();
  let result = "";
  let zeroFlag = false;
  const len = str.length;
  for (let i = 0; i < len; i++) {
    const digit = parseInt(str[i], 10);
    const unitIndex = len - i - 1;

    if (digit === 0) {
      zeroFlag = true;
      if (unitIndex % 4 === 0) {
        // 万、亿位
        result += chineseUnit[unitIndex];
        zeroFlag = false;
      }
    } else {
      if (zeroFlag) {
        result += "零";
        zeroFlag = false;
      }
      result += chineseNum[digit] + chineseUnit[unitIndex];
    }
  }
  // 处理以零结尾的情况
  while (result.endsWith("零")) {
    result = result.slice(0, -1);
  }
  // 处理一十开头的情况
  if (result.startsWith("一十")) {
    result = result.slice(1);
  }
  return result;
}
// 全选反选
let checkedAll = ref(false);
// 全选/反选
const selectAll = (checked) => {
  page.list.data.forEach((item) => {
    item.checked = checked;
  });
};

// 单个选择
const selectItem = () => {
  checkedAll.value = page.list.data.every((item) => item.checked);
};
// 打开订单处理
function openOrderProcessingRef(params) {
  orderProcessingRef.value.open(params, "shipment");
}
// 查看物流
function lookWl(params) {
  // 功能开发中
  elMessage({
    message: "功能开发中",
    type: "warning",
  });
}
// 查询
const queryTable = () => {
  page.search.data.cycleArr =
    page.search.data.cycleArr == null ? ["", ""] : page.search.data?.cycleArr;
  if (page.search.data.rqlx == "" && page.search.data?.cycleArr[0] != "") {
    elMessage({
      message: "请选择日期类型",
      type: "warning",
    });
    return;
  }
  if (page.search.data.rqlx != "" && page.search.data?.cycleArr[0] == "") {
    elMessage({
      message: "请选择日期",
      type: "warning",
    });
    return;
  }
  getTableData();
};
// onMounted

onMounted(() => {
  let query = route.query;
  if (query && query.memberId) {
    memberId.value = query.memberId;
    console.log("🚀 ~ onMounted ~ memberId.value:", memberId.value);
  }
  // 获得审批类型
  getApprovalType();
  // 获得表格数据
  getTableData();
  //获得商品分类
  getGoodsType();

  ///
});
//new

const handlerDatas = (arr) => {
  let newArr = arr.map((el) => {
    return el.productVo.categoryName;
  });
  if (!Array.isArray(newArr) || newArr.length === 0) return newArr;

  // 使用Set去重判断是否所有元素相同
  const uniqueValues = new Set(newArr);
  if (uniqueValues.size === 1) {
    return [newArr[0]]; // 返回包含单个元素的数组
  }
  return newArr; // 元素不相同则返回原数组
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.workhour {
  color: #444444;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;

  .workhour-item-line {
    width: 4px;
    height: 15px;
    border-radius: 3px;
    background-color: red;
    margin-right: 12px;
  }

  .workhour-item {
    margin-right: 23px;

    .red {
      color: red;
    }

    .blue {
      color: #10a3e7;
    }
  }
}

:deep(.noPadding .el-table__cell) {
  padding: 0 !important;
}

:deep(.noPadding .cell) {
  padding: 0;
  height: 100%;
}

// 订单详情
.order_details {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .good {
    width: 100%;

    .goodItem {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 3px 5px;
      border-bottom: 1px solid #ebeef5;

      // 左
      .left {
        width: 80px;
        height: 80px;

        img {
          width: 80px;
          height: 80px;
        }
      }

      //中
      .mid {
        width: 240px;
        line-height: 40px;
        text-align: left;
        margin-left: 20px;
      }

      // 右
      .right {
        width: 180px;
        line-height: 40px;
        margin-left: auto;
      }
    }

    :last-of-type {
      border: none;
    }
  }
}

//商品类型
.goodType {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;

  // padding-top: 35px;
  .goodTypeItem {
    box-sizing: content-box;
    padding: 3px 5px;
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #ebeef5;
  }

  :last-of-type {
    border: none;
  }
}

//收货信息
.shippingInfo {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}

.top {
  width: 100%;
  height: 30px;
  background-color: #fdeded !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
}

.top2 {
  width: 100%;
  height: 30px;
  background-color: #fdeded !important;
  color: #ba1f22;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
  position: absolute;
  top: 0;
}

//内容仿表格
:deep(.el-card__body) {
  border-left: 1px solid #ededed;
}

.tableMock {
  border: 1px solid #ededed;
  max-width: 1658px;
  background-color: #fff;
  height: 100%;
  overflow: hidden;
  position: relative;
  padding-top: 38px;
  display: flex;
  align-content: center;
  justify-content: center;
  align-content: center;
}

.tableHeader {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
  line-height: 38px;
  height: 38px;
  background: #f9f9f9;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  padding-right: 6px;
  box-sizing: border-box;
}

.tableHeader div {
  text-align: center;
  font-family: Microsoft YaHei UI;
  font-size: 14px;
  color: #303133;
  z-index: 0;
}

.tableContent {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  overflow: auto;
  height: 100%;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  /* 滚动条轨道背景颜色 */
}

::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  /* 滚动条滑块颜色 */
  border-radius: 3px;
  /* 滚动条滑块圆角 */
}

::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
  /* 鼠标悬停时滚动条滑块颜色 */
}

.borderLeft {
  border-left: 1px solid #ededed;
}

.borderRight {
  border-right: 1px solid #ededed;
}

.borderTop {
  border-top: 1px solid #ededed;
}

.borderBottom {
  border-bottom: 1px solid #ededed;
}

.select {
  width: 60px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.orderInfo {
  width: 890px;
}

.orderInfoFlex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.goodImg {
  width: 90px;
  height: 90px;
  border-radius: 4px;
  opacity: 1;
  padding: 0 !important;
}

.goodImg img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.goodInfo {
  // width: 526px;
  height: 94px;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  flex-wrap: wrap;
  align-items: flex-start;
  align-content: flex-start;
}

.goodInfo .goodTitle {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.goodInfo .goodTitle .left {
  // width: 245px;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  z-index: 0;
  text-align: left;
  display: flex;
}

.goodInfo .goodTitle .right {
  width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: right;
}

.goodInfo .goodNum {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.goodInfo .goodNum .left {
  min-width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: left;
  // 省略
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goodInfo .goodNum .right {
  min-width: 145px;
  font-size: 14px;
  color: #333333;
  z-index: 0;
  text-align: right;
}

.orderInfoaddselect {
  width: 950px;
}

.productType {
  width: 180px;
  font-size: 14px;
  color: #555;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  display: grid;
  grid-template-rows: repeat(auto-fill, 1fr);
}

.productTypeItem {
  width: 100%;
  height: 107px;
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
}

.productTypeItem:last-of-type {
  border: none;
}

.tabelshippingInfo {
  width: 300px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.tabelshippingInfo .maijia,
.tabelshippingInfo .lxfs {
  width: 100%;
  height: 25px;
  line-height: 25px;
  margin-bottom: 10px;
}

.tableStatus {
  width: 106px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
}

.tableStatus .dot {
  width: 8px;
  height: 8px;
  border-radius: 100px;
  background: #ff0001;
  margin-right: 5px;
}

.tableOperation {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.tableOperation .operationItem {
  width: 100%;
  height: 19px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #0b8df1;
  margin-bottom: 6px;
  cursor: pointer;
}

.tableItem {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  margin-top: 20px;
}

.tableItemTop {
  width: 100%;
  height: 38px;
  background: #f8fffa;
  display: flex;
  flex-wrap: nowrap;
}

.tableItemTopOther {
  width: 1536px;
  font-size: 14px;
  line-height: 38px;
  color: #303133;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.tableItemContent {
  width: 100%;
  // width: 1596px;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
}

.goodsItem {
  width: 100%;
  box-sizing: border-box;
  min-height: 110px;
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
}

.goodsItem .padding {
  padding: 10px 12px;
  text-align: center;
  font-size: 14px;
  color: #555;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  box-sizing: border-box;
  flex-wrap: wrap;
}

.goodsItemList {
  width: 100%;
  display: flex;
  // flex-wrap: wrap;
  border-bottom: 1px solid #ededed;
  // padding: 0 12px;
  box-sizing: border-box;
  &:last-of-type {
    border: none;
  }
}

.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 2px;
  background: #20b203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;
}

.u-empty .uni-image {
  width: 400rpx !important;
  height: 400rpx !important;
}
:deep(.orderInfoF .cell) {
  padding: 0 !important;
}
// 自定义表格
.table-container {
  width: 100%;
  margin: auto;
  border-collapse: collapse;
  border-bottom: 1px solid #ededed;
  border-left: 1px solid #ededed;
  border-right: 1px solid #ededed;
  height: calc(100% - 29px);
  background-color: #fff;
  .table-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: inset 0 -1px 0 0 #ededed, inset 0 1px 0 0 #ededed;
    box-sizing: border-box;
    .header-cell {
      color: #303133;
      font-size: 14px;
      width: 180px;
      // height: 38px;
      line-height: 38px;
      text-align: center;
      // background: #f9f9f9;
      box-sizing: border-box;
      // box-shadow: inset -1px -1px 0px 0px #ededed;
      // border-left: 1px solid #ededed;
      // 第一个
      &:first-of-type {
        border-left: none;
      }
    }

    .data-cell {
      color: #555;
      // width: 180px;
      min-height: 110px;
      padding: 8px 14px 14px 12px;
      display: flex;
      justify-content: center;
      font-size: 14px;
      gap: 6px;
      min-height: 110px;
      flex-grow: 1;
      border-right: 1px solid #ededed;
      .goodsItemList {
        width: 100%;
        display: flex;
        height: 100%;
        // flex-wrap: wrap;
        border-bottom: 1px solid #ededed;
        padding: 0 12px;
        &:last-of-type {
          border: none;
        }
        .goodImg {
          width: 90px;
          height: 90px;
          opacity: 1;
          padding: 0 !important;
          img {
            width: 90px;
            height: 90px;
            border-radius: 4px;
          }
        }

        .goodInfo {
          // width: 526px;
          height: 94px;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
          flex-wrap: wrap;
          align-items: flex-start;
          align-content: flex-start;
          .goodTitle {
            width: 100%;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .left {
              // width: 245px;
              font-size: 14px;
              font-weight: bold;
              color: #333333;
              z-index: 0;
              text-align: left;
              display: flex;
            }

            .right {
              width: 145px;
              font-size: 14px;
              color: #333333;
              z-index: 0;
              text-align: right;
            }
          }
        }

        .goodInfo .goodNum {
          width: 100%;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 10px;
          .left {
            min-width: 145px;
            font-size: 14px;
            color: #333333;
            z-index: 0;
            text-align: left;
            // 省略
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .right {
            min-width: 145px;
            font-size: 14px;
            color: #333333;
            z-index: 0;
            text-align: right;
          }
        }
      }
      .title {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .djsl {
        color: #555;
        box-sizing: border-box;
        text-align: right;
        font-size: 14px;
        .price,
        .amount {
          line-height: 18px;
          margin-bottom: 5px;
        }
      }
      .money {
        text-align: right;
        color: #555;
        font-size: 14px;
      }
      .buyer {
        text-align: left;
        color: #555;
        font-size: 14px;
      }
      .status {
        display: flex;
        color: #555;
        font-size: 14px;
        align-items: center;
        justify-content: center;
        min-height: 19px;
        .round {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ff8a00;
          margin-right: 10px;
        }
        .round1 {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ffda71;
          margin-right: 10px;
        }
        .round2 {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #068324;
          margin-right: 10px;
        }
        .round3 {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #ff0001;
          margin-right: 10px;
        }
      }
      .operation {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: center;
        text-align: center;
        .operationItem {
          padding: 0 6px;
          height: 19px;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: #0b8df1;
          // margin-bottom: 6px;
          cursor: pointer;
        }
      }
      .remark-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        min-height: 19px;
      }
    }
    .data-cell-center {
      align-content: center;
      align-content: center;
      flex-direction: column;
    }
    .order-product {
      width: 300px;
    }
    .order-product2 {
      width: 600px;
    }
    .order-product3 {
      width: 160px;
    }
  }
  .header-row {
    background-color: #f9f9f9;
    height: 38px;
  }
  .table-content {
    height: calc(100% - 29px);
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0bcbc;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #cfcdcd;
    }
    .row-indicator {
      margin-top: 20px;
      background-color: #f8fffa;
      border-top: 1px solid #ededed;
      height: 38px;
      width: 100%;
      .row-item {
        display: flex;
        font-size: 14px;
        gap: 16px;
        height: 100%;
        padding-left: 10px;
        align-items: center;
        color: #303133;
        .row-code {
          display: flex;
          height: 100%;
          align-items: center;
          .order-num {
            color: #0098ff;
            text-decoration: underline;
            display: flex;
            cursor: pointer;
            .copy-icon {
              width: 18px;
              height: 18px;
              margin-left: 5px;
            }
          }
        }
        .total {
          margin-left: auto;
          padding-right: 42.5px;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
