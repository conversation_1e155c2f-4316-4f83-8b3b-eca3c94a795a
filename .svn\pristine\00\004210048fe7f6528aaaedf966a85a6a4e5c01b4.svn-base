<template>
    <div>
        <ndDialog
            ref="ImportRecordDialog"
            width="800px"
            height="400px"
            :title="data.title"
            append-to-body
            :before-close="hsDetailClose"
            align-center
        >
            <div v-if="data.isLoing">
                <div class="recordDialogBox">
                    <div class="record">
                        <el-icon
                            v-if="data.recordItem"
                            style="color: #fc9f03"
                            size="40px"
                        >
                            <WarningFilled />
                        </el-icon>
                        <div v-if="data.recordItem">暂无导入记录</div>
                        <div v-else class="recordItem">
                            <div
                                v-if="!data.failureShow"
                                style="display: flex; align-items: center"
                            >
                                <el-icon style="color: #fc9f03" size="40px">
                                    <WarningFilled /> </el-icon
                                >失败
                            </div>
                            <div
                                v-else
                                style="display: flex; align-items: center"
                            >
                                <el-icon style="color: #51ee6b" size="40px">
                                    <SuccessFilled /> </el-icon
                                >成功
                            </div>
                        </div>
                    </div>
                    <div
                        class="failure"
                        v-if="data.errorFile && !data.failureShow"
                    >
                        你可以下载失败记录信息表，修改后重新上传
                    </div>
                    <!-- <div class="failure" v-if="data.failureShow == 1">{{ data.failureContext }}</div> -->
                    <div class="date">最近导入操作时间：{{ data.date }}</div>
                </div>
            </div>

            <div
                v-if="!data.isLoing"
                style="
                    width: 100%;
                    height: 200px;
                    text-align: center;
                    line-height: 200px;
                    font-size: 20px;
                    font-weight: bold;
                "
            >
                最近导入信息还在处理中
            </div>
            <template #footer>
                <ndButton
                    type="primary"
                    @click="exportExcel"
                    v-if="data.errorFile && !data.failureShow"
                    >下载失败记录</ndButton
                >
                <ndButton @click="close">再次上传</ndButton>
            </template>
        </ndDialog>
    </div>
</template>

<script setup>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import {
    ref,
    inject,
    reactive,
    watch,
    onMounted,
    onUpdated,
    onBeforeUnmount,
} from "vue";
import success from "@/assets/images/agree.png";
import noPass from "@/assets/images/noPass.png";
import { ElMessage } from "element-plus";
const $axios = inject("$axios");
// 变量
const emit = defineEmits(["farOpen", "again"]);
const data = reactive({
    title: "导入",
    success: "0",
    warn: "0",
    date: "",
    filePathFailure: "",
    filePath: "",
    failureShow: true,
    // failureContext: '',
    recordItem: true,
    isLoing: false,
    id: "",
    errorFile: false,
});
const ImportRecordDialog = ref(null);
let types = ref("");

const fileListInterval = ref(null);

onBeforeUnmount(() => {
    fileListInterval.value && clearTimeout(fileListInterval.value);
});

/**
 * 打开弹窗
 * @param {string} val type数据
 * @param {string} title 标题数据
 * @returns {void}
 */
const open = (val, title) => {
    types.value = val;
    data.title = title;
    data.isLoing = false;
    ImportRecordDialog.value.open();
    getImport();
};

/**
 * 获取导入记录
 * @returns {void}
 */
function getImport() {
    $axios({
        url: "/import/getImpQueue",
        serverName: "nd-base2",
        data: {
            types: types.value,
        },
        method: "get",
    }).then((res) => {
        if (res.data.code === 2000) {
            if (res.data.data) {
                if (res.data.data.result == 0 || res.data.data.result == 1) {
                    //0-失败，1-成功，2-正在处理
                    data.date = res.data.data.lastTime;
                    data.recordItem = false;
                    data.id = res.data.data.id || "";
                    data.errorFile = res.data.data.errorFile;
                    if (res.data.data.result == 1) {
                        data.failureShow = true;
                    } else if (res.data.data.result == 0) {
                        data.failureShow = false;
                    }
                    data.failNum = res.data.data.failNum;
                    data.isLoing = true;
                } else {
                    fileListInterval.value &&
                        clearTimeout(fileListInterval.value);
                    fileListInterval.value = setTimeout(() => {
                        data.isLoing = false;
                        getImport();
                    }, 2000);
                }
            } else {
                data.recordItem = true;
            }
        } else {
            data.isLoing = true;
            data.recordItem = true;
        }
    });
}

/**
 * 点击下载失败记录
 * @returns {void}
 */
const exportExcel = () => {
    // window.location.href = data.filePath;
    $axios({
        url: "/import/downloadFailureFile",
        method: "get",
        serverName: "nd-base2",
        responseType: "blob",
        data: {
            id: data.id,
        },
    }).then((r) => {
        if (!r.data) return;
        const link = document.createElement("a");
        let blob = new Blob([r.data], {
            type: "application/vnd.ms-excel",
        });
        link.href = URL.createObjectURL(blob);
        const nameData = decodeURIComponent(r.headers["content-disposition"]);
        const match = nameData.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        link.download = match[1].replace(/['"]/g, "");
        link.click();
        link.remove();
    });
};

/**
 * 关闭按钮
 * @returns {void}
 */
const hsDetailClose = () => {
    fileListInterval.value && clearTimeout(fileListInterval.value);
    emit("again");
    ImportRecordDialog.value.close();
};

/**
 * 关闭弹窗
 * @returns {void}
 */
const close = () => {
    data.success = "0";
    data.warn = "0";
    data.date = "";
    data.filePathFailure = "";
    data.filePath = "";
    data.failureShow = true;
    // data.failureContext = ''
    data.recordItem = true;
    fileListInterval.value && clearTimeout(fileListInterval.value);
    ImportRecordDialog.value.close();
    emit("farOpen");
};

// 方法导出，便于父组件接收并且调用
defineExpose({
    open,
});
</script>

<style lang="scss" scoped>
.recordDialogBox {
    position: relative;
    // height: 95%;
    height: 378px;

    .record {
        margin: 10px 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        .recordItem {
            font-size: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .failure {
        text-align: center;
        font-size: 16px;
    }

    .date {
        position: absolute;
        bottom: 0;
        right: 5%;
        color: #cccccc;
        text-align: right;
        font-size: 12px;
    }
}
</style>
