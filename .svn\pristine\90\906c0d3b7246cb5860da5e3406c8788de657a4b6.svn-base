<template>
    <!-- // 订单处理 -->
    <nd-dialog ref="refuseDialogRef" width="60vw" height="48vh" title="拒绝退款" align-center>
        <div class="refuse-box">
            <el-form ref="addFormRef" :model="form" :rules="rules" class="add-box" label-position="left">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="售后方案" label-width="80px">
                            不予处理
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="拒绝原因" label-width="80px" prop="shReject">
                            <nd-select filterable v-model="form.shReject" placeholder="请选择" width="100%" @change="changeReject">
                                <el-option label="商品已超出退款时效" value="商品已超出退款时效">商品已超出退款时效</el-option>
                                <el-option label="商品不属于退换范围" value="商品不属于退换范围">商品不属于退换范围</el-option>
                                <el-option label="用户无法提供有效凭证" value="用户无法提供有效凭证">用户无法提供有效凭证</el-option>
                            </nd-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="说明" label-width="80px" prop="shRemark">
                            <nd-input type="textarea" v-model="form.shRemark" placeholder="请输入" width="100%" maxlength="500"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <template #footer>
            <nd-button type="primary" icon="Pointer" @click="submit()">确&nbsp;定</nd-button>
            <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button>
        </template>
    </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义方法
let myEmit = defineEmits(["refreshTable"]);
// 定义属性
const props = defineProps({

});

// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const refuseDialogRef = ref(null);
// 定义表单数据
const addFormRef = ref(null);
let form = reactive({
    shhId:'',
    shReject:'',
    shRemark:''
});

// 表单校验规则
const rules = reactive({
    shReject: [{ required: true, message: "请输入拒绝原因", }],
});

// 打开弹窗 ==============================================================
// 打开弹窗
function open(data) {
    form.shhId = data.shhId;
    refuseDialogRef.value.open();
}

// 清空表单
const clear = () => {
    // 清空校验
    if (addFormRef.value) {
        addFormRef.value.resetFields();
    }
};

// 关闭弹窗 ==============================================================
const close = () => {
    refuseDialogRef.value.close();
    clear();
};

// 保存提交 ==============================================================
const submit = async () => {
    await addFormRef.value.validate((valid, fields) => {
        if (valid) {
            let params = {
                shhId: form.shhId,
                shWay: 2,
                shReject: form.shReject,
                shRemark: form.shRemark,
            };
            $axios({
                url: "/sh/saveSh",
                method: "post",
                serverName: 'nd-base2',
                data: params,
            }).then((res) => {
                if (res.data.code === 2000) {
                    // 轻提示
                    ElMessage({
                        message: "处理成功",
                        type: "success",
                    });
                    // 关闭弹框
                    myEmit("refreshTable");
                    close();
                } else {
                    ElMessage({
                        message: res.data.message,
                        type: "warning",
                    });
                }
            });
        } else {
            console.log("校验失败!", fields);
        }
    });
};
const changeReject=()=>{
    form.shRemark=''
}
// 暴露方法给父组件 =======================================================
defineExpose({
    open,
    clear,
});
</script>

<style lang="scss" scoped>
.goodstotal {
    margin-bottom: 20px;
}
.refuse-box{
    /* width: 100%; */
    height: calc(100% - 24px);
    background: #fff;
    padding: 12px;
    margin: 12px;
}
.add-box {
    :deep(.el-textarea__inner) {
        height: 80px;
    }
}
:deep(.el-form-item__label){
    justify-content:flex-end
}
:deep(.el-textarea__inner:focus){
    box-shadow: 0 0 0 1px #068324;
}
</style>