<template>
  <div class="nd-date-picker-box" :style="{ width: width }">
    <el-date-picker ref="datePicker" v-bind="$attrs" :teleported="teleported" popper-class="nd-date-picker-popper"></el-date-picker>
  </div>
</template>

<script setup>
defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px",
  },
  // 是否插入至body元素
  teleported: {
    type: Boolean,
    default: true
  }
})
</script>

<style lang="scss">
.nd-date-picker-popper {
  .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
  .el-date-table td.today .el-date-table-cell__text,
  .el-date-table td.available:hover,
  .el-date-picker__header-label:hover,
  .el-picker-panel__icon-btn:hover,
  .el-picker-panel__icon-btn:hover,
  .el-time-panel__btn.confirm,
  .el-year-table td.today .cell,
  .el-year-table td .cell:hover,
  .el-year-table td.current:not(.disabled) .cell,
  .el-year-table td.today .el-date-table-cell__text,
  .el-year-table td .el-date-table-cell__text:hover {
    color: #068324;
  }
  .el-date-table td.end-date .el-date-table-cell__text,
  .el-date-table td.start-date .el-date-table-cell__text,
  .el-year-table td.current:not(.disabled) .el-date-table-cell__text {
    background-color: #068324;
    color: #fff;
  }
  .el-year-table td.today .cell {
    font-weight: normal;
  }
  .el-year-table td.current:not(.disabled) .cell {
    font-weight: 700;
  }
  .el-button.is-plain {
    --el-button-hover-text-color: #068324;
    --el-button-hover-border-color: #068324;
  }
  .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
    background-color: #068324;
    color:#fff;
  }
}
</style>
<style lang="scss" scoped>
.nd-date-picker-box {
  :deep(.el-input__inner) {
    width: 100%;
    font-size: 14px;
  }

  :deep(.el-date-editor.el-input) {
    width: 100%;
  }

  :deep(.el-date-editor.el-input__wrapper) {
    width: 100%;
  }

  :deep(.el-range-input) {
    font-size: 14px;
  }

  :deep(.el-input__icon) {
    line-height: 100%;
  }

  :deep(.el-range-separator) {
    width: auto;
    height: auto;
    line-height: normal;
    font-size: 14px;
  }
  :deep(.el-range-editor.is-active) {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
  :deep(
      .nd-date-picker-popper
        .el-date-table
        td.end-date
        .el-date-table-cell__text,
      .nd-date-picker-popper
        .el-date-table
        td.start-date
        .el-date-table-cell__text
    ) {
    color: #fff !important;
  }
  :deep(.el-date-table td.current:not(.disabled) .el-date-table-cell__text) {
    background-color: #068324;
  }
  :deep(.el-year-table td .el-date-table-cell__text:hover) {
    color: #068324;
  }
}
</style>