<template>
  <div class="nd-tree-select-box" :style="{ width: width }">
    <el-tree-select v-bind="$attrs" popper-class="nd-tree-select-popper">
      <template #default="{ node, data }">
        <slot name="default" :node="node" :data="data"></slot>
      </template>
    </el-tree-select>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, reactive, nextTick } from "vue";

var prop = defineProps({
  // 宽度
  width: {
    type: String,
    default: "230px",
  },
});
</script>

<style lang="scss">
.nd-tree-select-popper {
  .el-select-dropdown__item.selected,
  .el-select-dropdown__item.is-selected {
    color: #068324 !important;
  }
  .el-select__wrapper.is-focused {
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
}
</style>
<style lang="scss" scoped>
.nd-tree-select-box {
  :deep(.el-select) {
    width: 100%;
  }
}
// 去除选中时蓝色边框
:deep(.el-input .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
:deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
</style>
