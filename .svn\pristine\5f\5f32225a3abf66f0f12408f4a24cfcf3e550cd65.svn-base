<template>
  <nd-dialog
    ref="shipmentDialogRef"
    width="48vw"
    height="44vh"
    :title="title"
    align-center
  >
    <div class="main">
      <div class="shipment">
        <el-form
          ref="addFormRef"
          :model="form"
          :rules="rules"
          class="add-box"
          label-position="right"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="发货方式" label-width="110px" prop="type">
                <nd-radio-group
                  v-model="form.type"
                  @change="handleStatusChange"
                >
                  <nd-radio :value="1">第三方物流发货</nd-radio>
                  <nd-radio :value="2">商家自行发货</nd-radio>
                </nd-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="form.type == 1" :span="24">
              <el-form-item label="物流公司" label-width="110px" prop="wlgs">
                <nd-select
                  filterable
                  v-model="form.wlgs"
                  placeholder=" "
                  width="90%"
                >
                  <!-- :value="item.dictKey" -->
                  <el-option
                    v-for="item in wlgsList"
                    :key="item.dictId"
                    :label="item.dictValue"
                    :value="item.dictValue"
                    >{{ item.dictValue }}</el-option
                  >
                </nd-select>
              </el-form-item>
            </el-col>
            <el-col v-if="form.type == 1" :span="24">
              <el-form-item label="物流单号" label-width="110px" prop="wlbh">
                <nd-input
                  type2="number5"
                  v-model="form.wlbh"
                  placeholder=" "
                  width="90%"
                  maxlength="30"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="form.type == 2" :span="24">
              <el-form-item label="司机姓名" label-width="110px" prop="sjName">
                <nd-input
                  v-model="form.sjName"
                  placeholder=" "
                  width="90%"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="form.type == 2" :span="24">
              <el-form-item
                label="司机联系方式"
                label-width="110px"
                prop="sjLxfs"
              >
                <nd-input
                  type2="number5"
                  v-model="form.sjLxfs"
                  placeholder=" "
                  width="90%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="发货日期" label-width="110px" prop="fhTime">
                <nd-date-picker
                  v-model="form.fhTime"
                  :empty-values="['', '']"
                  :value-on-clear="''"
                  range-separator="至"
                  type="date"
                  format="YYYY-MM-DD "
                  value-format="YYYY-MM-DD"
                  placeholder="请选择"
                  width="90%"
                ></nd-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" label-width="110px" prop="remake">
                <nd-input
                  maxlength="500"
                  type="textarea"
                  v-model="form.remake"
                  placeholder=" "
                  width="90%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <template #footer>
      <nd-button type="" icon="Pointer" @click="submit('1')"
        >确&nbsp;定</nd-button
      >
      <nd-button type="" icon="Back" @click="close">返&nbsp;回</nd-button>
    </template>
  </nd-dialog>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndUpload from "@/components/business/ndbUpload/index.vue";
import ndTabs from "@/components/ndTabs.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
// 导入element-plus方法
import { ElMessage } from "element-plus";
// 导入vue
import { ref, inject, onMounted, reactive, nextTick, watch } from "vue";
// 定义axios
const $axios = inject("$axios");
// 定义emit
let emit = defineEmits(["before-close"]);
// 定义属性
const props = defineProps({});
// 定义时间格式化函数
const formatDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  // return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return `${year}-${month}-${day}`;
};
// 定义当前组件的变量 ====================================================
// 定义对话框组件的ref
const shipmentDialogRef = ref(null);
// 定义对话框的标题
let title = ref("");
// 定义表单数据
const addFormRef = ref(null);
let tableContent = ref(null);
let tableData = ref([]);
let orderId = ref("");
let form = ref({
  wlId: 0,
  orderId: "",
  detailIds: [],
  wlbh: "",
  wlgs: "",
  remark: "",
  status: "",
  fhTime: formatDateTime(),
  czr: localStorage.getItem("syUserName"),
  sjName: "", //司机姓名
  sjLxfs: "", //司机联系方式
  type: 1, //发货方式 1第三方物流返回 2商家自行发货
});
// 多选
const handleSelectionChange = (val) => {
  form.value.detailIds = val.map((item) => item.detailId);
};
// 定义校验规则 ==========================================================

// 自定义校验规则 --- 正则 --- 功能菜单名称最长不得超过10个汉字
const validateFunctionName = (rule, value, callback) => {
  const chineseReg = /[\u4e00-\u9fa5]/g; // 汉字的正则表达式
  let chineseCharacters = form.value.menuName.match(chineseReg); // 匹配输入字符串中的汉字
  if (chineseCharacters && chineseCharacters.length > 10) {
    return callback(new Error("error"));
  } else {
    return callback();
  }
};

// 表单校验规则
const rules = reactive({
  type: [{ required: true, message: "请选择发货方式" }],
  wlgs: [{ required: true, message: "请选择物流公司" }],
  wlbh: [{ required: true, message: "请输入物流单号" }],
  sjName: [{ required: true, message: "请输入司机姓名" }],
  sjLxfs: [
    { required: true, message: "请输入司机联系方式" },
    {
      validator: (rule, value, callback) => {
        if (value) {
          const reg = /^1[3-9]\d{9}$/;
          if (!reg.test(value)) {
            callback(new Error("请输入有效的11位手机号码"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  fhTime: [{ required: true, message: "请选择发货日期" }],
  functionName: [
    { required: true, message: "请输入功能点名称", trigger: "blur" },
    // { min: 1, max: 50, message: "最长不得超过50个汉字", trigger: "blur" },
    {
      validator: validateFunctionName,
      trigger: "blur",
      message: "最长不得超过50个汉字",
    },
  ],
});

// 打开弹窗 ==============================================================
// 打开弹窗
function open(params, status) {
  clear();
  if (status === "shipment") {
    title.value = "物流选择";
    orderId.value = params.orderId;
    form.value.orderId = params.orderId;
    form.value.detailIds = params.detailIds;
    getDetail();
    getWlInfo();
    getDictfindList();
    shipmentDialogRef.value.open();
  }
}
// 获得详情
function getDetail() {
  $axios({
    url: "/order/manage/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: orderId.value,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      tableContent.value = res.data.data;
      tableData.value = tableContent.value.detailVos;
    }
  });
}
//查看物流信息
function getWlInfo() {
  $axios({
    url: "/order/manage/wl/find",
    serverName: "nd-base2",
    method: "get",
    data: {
      orderId: orderId.value,
    },
  }).then((res) => {
    if (res.data.code === 2000) {
    }
  });
}
// 获取物流公司字典
let wlgsList = ref([]);
const getDictfindList = () => {
  $axios({
    url: "/dict/findAll",
    serverName: "nd-base2",
    method: "get",
    data: {
      dictMark: "WLGS",
    },
  }).then((res) => {
    if (res.data.code === 2000) {
      wlgsList.value = res.data.data;
    }
  });
};

// 清空表单
const clear = () => {
  // 清空校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
};
const handleStatusChange = (newVal) => {};

// 关闭弹窗 ==============================================================
const close = () => {
  emit("before-close");
  shipmentDialogRef.value.close();
  clear();
};

// 保存提交 ==============================================================
const submit = async () => {
  if (form.value.detailIds.length === 0) {
    ElMessage({
      message: "请选择商品",
      type: "warning",
    });
    return;
  }
  // 发货
  await addFormRef.value.validate((valid, fields) => {
    // delete form.value.testDutyName

    if (valid) {
      let params = form.value;
      $axios({
        url: "/order/manage/send/goods",
        method: "post",
        serverName: "nd-base2",
        data: params,
      }).then((res) => {
        if (res.data.code === 2000) {
          console.log("编辑成功");
          // 轻提示
          ElMessage({
            message: "保存成功",
            type: "success",
          });
          // 关闭弹框
          emit("before-close");
          close();
        } else {
          ElMessage({
            message: res.data.message,
            type: "warning",
          });
        }
      });
    } else {
      console.log("校验失败!", fields);
    }
  });
};

// 暴露方法给父组件 =======================================================
defineExpose({
  open,
  clear,
});
</script>

<style lang="scss" scoped>
.main {
  padding: 12px;
  width: 100%;
  background-color: #f7f7f7;
  .shipment {
    padding: 12px;
    background-color: #fff;
    border-radius: 5px;
    border: 1px solid #ebeef5;
  }
}
.goodstotal {
  margin-bottom: 20px;
}

.add-box {
  padding: 0px;
  margin-top: 32px;

  :deep(.el-textarea__inner) {
    height: 80px;
  }
}

.item1 {
  flex: 4;
  padding: 8px 12px;
  display: flex;

  .goodImg {
    width: 90px;
    height: 90px;
    border-radius: 4px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .goodInfo {
    // width: 100%;
    text-align: left;
    margin-left: 10px;

    .title {
      font-size: 14px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 10px;
      display: flex;
    }
  }
}

.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  min-width: 45px;
  height: 20px;
  border-radius: 2px;
  padding: 0 2px;
  background: #20b203;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  margin-right: 4px;
  text-align: center;
  margin-right: 5px;
}
:deep(.dialog-content-box) {
  padding: 12px !important;
}
</style>
